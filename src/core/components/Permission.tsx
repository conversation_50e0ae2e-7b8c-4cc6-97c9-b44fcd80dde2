import { useAuth } from '@/contexts/AuthContext'
import { Box } from '@mui/material'

export default ({ children, permission }: { children: React.ReactNode; permission: string | string[] }) => {
  const { accountPermissions } = useAuth()

  if (Array.isArray(permission)) {
    if (permission.filter(perm => accountPermissions.includes(perm)).length === 0) {
      return null
    }
    return children
  }

  if (!accountPermissions.includes(permission)) {
    return null
  }

  return children
}
