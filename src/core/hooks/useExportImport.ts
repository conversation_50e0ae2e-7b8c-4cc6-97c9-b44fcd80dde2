import { useExportMutation, useImportMutation } from '@/api/services/export-import/mutation'
import ExportImportQueryMethods from '@/api/services/export-import/query'
import { ExportImportScope, ExportPayload, ExportType, ImportPayload, ImportType } from '@/types/exportImportTypes'
import { useMutation } from '@tanstack/react-query'
import { useCallback, useState } from 'react'
import { toast } from 'react-toastify'

export type ImportState = {
  importFn: (payload: Omit<ImportPayload, 'scope'>) => Promise<ImportType>
  getTemplate: () => Promise<string>
  isLoading: boolean
  isImportTemplateLoading: boolean
}

export const useImportState = (scope: ExportImportScope) => {
  const [openProcessingImportDialog, setOpenProcessingImportDialog] = useState(false)
  const { mutateAsync, isLoading } = useImportMutation({
    onSuccess: () => {
      setOpenProcessingImportDialog(true)
    },
    onError: error => {
      toast.error('impor data gagal')
      console.error('error', error)
    }
  })
  const { mutateAsync: getTemplate, isLoading: isImportTemplateLoading } = useMutation({
    mutationFn: () => ExportImportQueryMethods.getImportTemplate(scope)
  })

  const importFn = useCallback(
    (payload: Omit<ImportPayload, 'scope'>) =>
      mutateAsync({
        scope,
        ...payload
      }),
    [scope]
  )

  return {
    importFn,
    getTemplate,
    isLoading,
    isImportTemplateLoading,
    openProcessingImportDialog,
    setOpenProcessingImportDialog
  }
}

export type ExportState<T> = {
  exportFn: (payload: ExportPayload<T>) => Promise<ExportType>
  isExporting: boolean
}

export const useExportState = <T>(
  scope: ExportImportScope,
  onSuccess?: (success: boolean) => void,
  initialPayload?: T
) => {
  const { mutateAsync, isLoading: isExporting } = useExportMutation()
  const exportFn = useCallback(
    (payload: ExportPayload<T>) =>
      mutateAsync(
        { scope, payload },
        {
          onSuccess: resp => {
            onSuccess && onSuccess(true)
          },
          onError: error => {
            onSuccess && onSuccess(false)
            toast.error('Data gagal di ekspor')
            console.error(error)
          }
        }
      ),
    [scope]
  )

  return {
    exportFn,
    isExporting
  }
}
