import React, { useState, useEffect } from 'react'

const usePartialState = <T extends Partial<T>>(
  defaultState: T,
  key?: string
): [T, (fieldName: string, value: any) => void, (state: T) => void] => {
  const getInitialState = (): T => {
    if (key) {
      const storedState = localStorage.getItem(key)
      return storedState ? JSON.parse(storedState) : defaultState
    }
    return defaultState
  }

  const [state, setState] = useState<T>(getInitialState)

  useEffect(() => {
    if (key) {
      localStorage.setItem(key, JSON.stringify(state))
    }
  }, [key, state])

  const setStateByField = (fieldName: string, value: any) => {
    setState(prevState => ({
      ...prevState,
      [fieldName]: value
    }))
  }

  const setEntireState = (data: T) => {
    setState(data)
  }

  return [state, setStateByField, setEntireState]
}

export default usePartialState
