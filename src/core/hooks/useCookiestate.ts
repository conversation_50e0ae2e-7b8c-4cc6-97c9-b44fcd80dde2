import { useState, useEffect } from 'react'
import Cookies from 'js-cookie'

const useCookieState = <T>(key: string, initialValue?: T, secure?: boolean) => {
  const [state, setState] = useState<T | undefined>(() => {
    const cookieValue = Cookies.get(key)
    return cookieValue !== undefined && cookieValue !== null ? (JSON.parse(cookieValue) as T) : initialValue
  })

  useEffect(() => {
    if (state !== undefined) {
      Cookies.set(key, JSON.stringify(state), { expires: 7, secure })
    } else {
      Cookies.remove(key)
    }
  }, [key, state])

  const clear = () => {
    setState(initialValue)
    Cookies.remove(key)
  }

  return [state, setState, clear] as const
}

export default useCookieState
