import { useEffect, useState } from 'react'
import localforage from 'localforage'

const useLocalForage = <T>(key: string, defaultValue?: T) => {
  const [value, setValue] = useState<T | null>(defaultValue ?? null)
  const [error, setError] = useState<string | null>(null)

  const getValue = async () => {
    try {
      const storedValue = await localforage.getItem<T>(key)
      if (storedValue !== null) {
        setValue(storedValue)
      } else {
        setValue(defaultValue ?? null)
      }
    } catch (err) {
      setError('Error retrieving data')
      console.error(err)
    }
  }

  const setValueInLocalForage = async (newValue: T) => {
    try {
      await localforage.setItem(key, newValue)
      setValue(newValue)
    } catch (err) {
      setError('Error saving data')
      console.error(err)
    }
  }

  const removeValue = async () => {
    try {
      await localforage.removeItem(key)
      setValue(defaultValue ?? null)
    } catch (err) {
      setError('Error removing data')
      console.error(err)
    }
  }

  useEffect(() => {
    getValue()
  }, [key])

  return { value, error, setValue: setValueInLocalForage, removeValue }
}

export default useLocalForage
