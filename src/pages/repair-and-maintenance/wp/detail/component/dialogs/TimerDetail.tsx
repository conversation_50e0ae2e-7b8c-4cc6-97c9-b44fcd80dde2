import Table from '@/components/table'
import { UserOutlineType } from '@/types/userTypes'
import { WpSessionType } from '@/types/wpTypes'
import { Dialog, DialogContent, DialogTitle, Grid, IconButton, Typography } from '@mui/material'
import {
  createColumnHelper,
  getSortedRowModel,
  getFilteredRowModel,
  getFacetedRowModel,
  getFacetedMinMaxValues,
  getFacetedUniqueValues,
  useReactTable,
  getCoreRowModel
} from '@tanstack/react-table'
import { formatDate } from 'date-fns'

type DialogTimerDetailsProps = {
  open: boolean
  setOpen: (open: boolean) => void
  row: WpSessionType
}

const columnHelper = createColumnHelper<UserOutlineType>()

const DialogTimerDetails = ({ row, open, setOpen }: DialogTimerDetailsProps) => {
  const table = useReactTable({
    data: row.labors,
    columns: [
      columnHelper.display({
        id: 'number',
        size: 20,
        cell: ({ row }) => <Typography>{row.index + 1}</Typography>
      }),
      columnHelper.accessor('fullName', {
        header: 'Nama'
      }),
      columnHelper.accessor('title', {
        header: 'Jabatan'
      })
    ],
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })
  return (
    <Dialog open={open} onClose={() => setOpen(false)} maxWidth='md'>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Timer Pengerjaan
        <Typography>Lihat Detil Sesi pengerjaan</Typography>
      </DialogTitle>
      <DialogContent className='pbs-0 pbe-16 !pb-12 sm:px-12 !py-4'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          <Grid item xs={12}>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-y-4 gap-x-12'>
              <div className='flex flex-col gap-2'>
                <Typography component={'small'} className='text-xs'>
                  Mulai
                </Typography>
                <Typography>{formatDate(new Date(row.startedAt), 'dd/MM/yyyy HH:mm')}</Typography>
              </div>
              <div className='flex flex-col gap-2'>
                <Typography component={'small'} className='text-xs'>
                  Selesai
                </Typography>
                <Typography>{formatDate(new Date(row.stoppedAt), 'dd/MM/yyyy HH:mm')}</Typography>
              </div>
              <div className='flex flex-col gap-2 col-span-2'>
                <Typography component={'small'} className='text-xs'>
                  Durasi
                </Typography>
                <Typography>{row.durationInSec} detik</Typography>
              </div>
              <div className='flex flex-col gap-2 col-span-2'>
                <Typography component={'small'} className='text-xs'>
                  Catatan
                </Typography>
                <Typography>{row.note ?? '-'}</Typography>
              </div>
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-3'>
              <Typography>Labors</Typography>
              <div className='rounded-md shadow-md'>
                <Table headerColor='green' table={table} disablePagination />
              </div>
            </div>
          </Grid>
        </Grid>
      </DialogContent>
    </Dialog>
  )
}

export default DialogTimerDetails
