import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  TextField,
  Typography
} from '@mui/material'
import { useState } from 'react'
import { useFilePicker } from 'use-file-picker'

type ApproveDialogConfirmProps = {
  open: boolean
  setOpen: (open: boolean) => void
  type: 'TAKE' | 'RETURN'
  id: number
  loading?: boolean
  handleSubmit: (dto: { takenBy: string; uploadId: string; id?: number }) => void
}

const ApproveDialogConfirm = ({ open, setOpen, type, handleSubmit, ...props }: ApproveDialogConfirmProps) => {
  const [{ uploadId, takenBy, id }, setField] = useState<{ takenBy: string; uploadId: string; id?: number }>({
    takenBy: '',
    uploadId: '',
    id: props.id
  })
  const { filesContent, openFilePicker } = useFilePicker({
    accept: 'image/jpeg,image/png,image/gif,image/bmp,image/webp',
    multiple: false,
    readAs: 'DataURL'
  })

  const handleClose = () => {
    setOpen(false)
  }

  const onSubmit = () => {
    handleSubmit({ takenBy, uploadId: filesContent[0].content, id })
  }

  return (
    <Dialog fullWidth open={open} onClose={() => setOpen(false)}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-16'>
        Setujui {type === 'TAKE' ? 'Pengambilan' : 'Pengembalian'} Barang MRO
        <Typography component='span' className='flex flex-col text-center'>
          Masukkan data {type === 'TAKE' ? 'pengambilan' : 'pengembalian'} barang untuk dokumen MRO
        </Typography>
      </DialogTitle>
      <DialogContent className='overflow-visible pbs-0 sm:px-16'>
        <IconButton onClick={handleClose} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <div className='space-y-4'>
          <small>Masukkan data {type === 'TAKE' ? 'pengambilan' : 'pengembalian'} MRO</small>
          <div className='flex flex-col gap-4'>
            <TextField
              label='Diambil oleh'
              value={takenBy}
              onChange={e => setField(curr => ({ ...curr, takenBy: e.target.value }))}
            />
            <div className='flex flex-col'>
              <Typography variant='subtitle2' marginBottom={2}>
                Unggah Foto Bukti Pengambilan
              </Typography>
              <div className='flex items-center gap-4'>
                <TextField
                  size='small'
                  value={filesContent?.[0]?.name}
                  fullWidth
                  placeholder='Tidak ada file dipilih'
                  aria-readonly
                  className='flex-1'
                />
                <Button variant='contained' onClick={openFilePicker}>
                  Unggah
                </Button>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
      <DialogActions className='justify-center pbs-0 sm:pbe-16 sm:px-16'>
        <Button disabled={props.loading} variant='outlined' onClick={handleClose}>
          Batalkan
        </Button>
        <Button
          disabled={!takenBy || filesContent.length === 0 || props.loading}
          variant='contained'
          color='primary'
          onClick={onSubmit}
        >
          Setujui
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default ApproveDialogConfirm
