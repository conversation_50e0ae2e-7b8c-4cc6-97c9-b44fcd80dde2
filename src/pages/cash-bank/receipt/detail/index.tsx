import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Grid, Typography } from '@mui/material'
import { Link } from 'react-router-dom'
import { useReceipt } from '../context/ReceiptContext'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { getStatusConfig } from '../config/utils'
import CreatedByCard from './components/CreatedByCard'
import ApprovalDetailCard from './components/ApprovalDetailCard'
import DocInvoiceCard from './components/DocInvoiceCard'
import InvoiceDetailCard from './components/InvoiceDetailCard'
import PaymentDetailCard from './components/PaymentDetailCard'
import ItemListCard from './components/AccountListCard'

const PaymentDetailPage = () => {
  const { cashReceiptData } = useReceipt()
  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Kas & Bank</Typography>
          </Link>
          <Link to='/cash-bank/receipt' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Penerimaan</Typography>
          </Link>
          <Typography>Detil Dokumen</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end'>
          <div className='flex flex-col'>
            <div className='flex gap-2 items-start'>
              <Typography variant='h4'>No. Pencatatan {cashReceiptData?.number}</Typography>
              <Chip
                size='small'
                label={getStatusConfig(cashReceiptData?.status).label}
                color={getStatusConfig(cashReceiptData?.status).color as any}
                variant='tonal'
              />
            </div>
            <Typography>
              {formatDate(cashReceiptData?.createdAt ?? Date.now(), 'eeee, dd/MM/yyyy', { locale: id })}
            </Typography>
          </div>
          <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
            {/* <Button
              disabled={loadingMutate}
              onClick={() => router.back()}
              color='secondary'
              variant='outlined'
              className='is-full sm:is-auto'
            >
              Ekspor
            </Button>
            <Button
              variant='contained'
              disabled={loadingMutate}
              onClick={handleSubmit(onSubmit)}
              className='is-full sm:is-auto'
            >
              Cetak
            </Button> */}
          </div>
        </div>
      </Grid>
      <Grid item xs={12}>
        <ItemListCard />
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          {/* {cashReceiptData?.purchaseInvoiceId && (
            <>
              <Grid item xs={12}>
                <DocInvoiceCard purchaseInvoiceId={cashReceiptData?.purchaseInvoiceId} />
              </Grid>
              <Grid item xs={12}>
                <InvoiceDetailCard purchaseInvoiceId={cashReceiptData?.purchaseInvoiceId} />
              </Grid>
            </>
          )} */}
          <Grid item xs={12}>
            <PaymentDetailCard />
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <CreatedByCard />
          </Grid>
          <Grid item xs={12}>
            <ApprovalDetailCard approvalList={cashReceiptData?.approvals ?? []} />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default PaymentDetailPage
