import { Card, CardContent, Typography } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

type Props = {
  purchaseInvoiceId?: string
}

const InvoiceDetailCard = ({ purchaseInvoiceId }: Props) => {
  // TODO: get invoice data
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Faktur</Typography>
        </div>
        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          <div className='flex flex-col gap-2'>
            <small>Vendor</small>
            <Typography>KK Tractor Shop Jakarta</Typography>
          </div>
          <div className='flex flex-col gap-2'>
            <small>Syarat Bayar</small>
            <Typography>NET 30</Typography>
          </div>
          <div className='flex flex-col gap-2'>
            <small>Tanggal Faktur</small>
            <Typography>{formatDate(Date.now(), 'dd/MM/yyyy')}</Typography>
          </div>
          <div className='flex flex-col gap-2'>
            <small>Jatuh Tempo</small>
            <Typography color='error'>{formatDate(Date.now(), 'dd/MM/yyyy')}</Typography>
          </div>
          <div className='flex flex-col gap-2 col-span-2'>
            <small>Memo</small>
            <Typography>Pembayaran Pembelian</Typography>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default InvoiceDetailCard
