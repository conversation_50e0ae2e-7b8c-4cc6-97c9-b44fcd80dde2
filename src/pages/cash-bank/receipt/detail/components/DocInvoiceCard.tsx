import { Card, CardContent, Typography } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

type Props = {
  purchaseInvoiceId?: string
}

const DocInvoiceCard = ({ purchaseInvoiceId }: Props) => {
  // TODO: get invoice data
  return (
    <Card>
      <CardContent className='flex flex-col gap-2'>
        <small>Dibuat dari F<PERSON>tur Pembelian</small>
        <Typography variant='h5'>No. Faktur {'FKTBELI/2025/000000'}</Typography>
        <Typography variant='caption'>{formatDate(Date.now(), 'eeee, dd/MM/yyyy', { locale: id })}</Typography>
      </CardContent>
    </Card>
  )
}

export default DocInvoiceCard
