import { AccountType } from '@/types/accountTypes'
import { CodeNameType } from '@/types/common'
import { CurrenciesType } from '@/types/currenciesTypes'
import { ListParams } from '@/types/payload'
import { ProjectType } from '@/types/projectTypes'
import { ApproverType, UserOutlineType } from '@/types/userTypes'

export type PaymentTypes = 'GENERAL' | 'PURCHASE'
export type PaymentStatus = 'PROCESSED' | 'APPROVED' | 'REJECTED' | 'CANCELED'

export type PaymentParams = {
  status?: PaymentStatus
  type?: PaymentTypes
  purchaseInvoiceId?: string
  projectId?: string
} & ListParams

export type CreatePaymentItemPayload = {
  accountId: string
  amount: number
  description: string
  code?: string
  name?: string
}

type CashPaymentType = {
  type: string
  items: CreatePaymentItemPayload[]
  currencyId: string
  exchangeRate: number
  accountId: string
  approvals: { userId: string }[]
  memo: string
}

export type CreatePaymentPayload = {
  purchaseInvoiceId: string
  useCheckNumber: boolean
  checkNumber: string
  departmentId: string
  siteId: string
  projectId: string
} & CashPaymentType

export type PaymentApprovalPayload = {
  paymentId: string
  approvalId: number
  status: string
  rejectionNote?: string
}

export type PaymentApproval = ApproverType

export type PaymentType = {
  id: string
  type: string
  number: string
  itemsCount: number
  approvalsCount: number
  currencyId: string
  exchangeRate: number
  totalAmount: number
  lcTotalAmount: number
  accountId: string
  memo: string
  checkNumber: string
  voucherNumber: string
  status: string
  companyId: string
  parentCompanyId: string
  departmentId: string
  siteId: string
  projectId: string
  createdAt: string
  updatedAt: string
  createdBy: string
  account: AccountType
  purchaseInvoiceId: string
  purchaseInvoice: {
    id: string
    companyId: string
    parentCompanyId: string
    departmentId: string
    siteId: string
    projectId: string
    vendorId: string
    currencyId: string
    number: string
    vendorNumber: string
    subTotalAmount: number
    otherAmount: number
    discountType: string
    discountValue: number
    discountAmount: number
    totalAmount: number
    lcTotalAmount: number
    status: string
    note: string
    exchangeRate: number
    approvalsCount: number
    documentUrl: string
    documentMimeType: string
    isGeneralPurchase: true
    isDownPayment: true
    paymentTerms: string
    paymentDueDays: number
    paymentDueDate: string
    invoiceDate: string
    createdAt: string
    updatedAt: string
    approvedAt: string
    paidAt: string
    approvals: [string]
    orders: [string]
    otherExpense: [string]
    currency: CurrenciesType
    vendor: {
      id: string
      code: string
      type: string
      name: string
      email: string
      taxplayerNumber: string
      paymentTerms: string
      paymentDueDays: number
      categoryId: string
      companyId: string
      parentCompanyId: string
      createdAt: string
      updatedAt: string
      addresses: [
        {
          id: string
          vendorId: string
          address: string
          phoneNumber: string
          faxNumber: string
          picName: string
          picPhoneNumber: string
          isDefault: true
          createdAt: string
          updatedAt: string
        }
      ]
      category: {
        id: string
        type: string
        code: string
        name: string
        parentId: string
        companyId: string
        parentCompanyId: string
        createdAt: string
        updatedAt: string
        parent: string
      }
      createdByUser: {
        id: string
        fullName: string
        title: string
        profilePictureUrl: string
      }
    }
    createdByUser: {
      id: string
      fullName: string
      title: string
      profilePictureUrl: string
    }
  }
  items: [string]
  approvals: PaymentApproval[]
  department: CodeNameType
  site: CodeNameType
  project: ProjectType
  createdByUser: UserOutlineType
}

export type CashReceiptParams = {
  type?: 'GENERAL' | 'SALES'
  status?: PaymentStatus
  projectId?: string
} & ListParams

export type CreateCashReceiptPayload = {
  type: 'GENERAL' | 'SALES'
  voucherNumber?: string
  transactionDate: string
  departmentId?: string
  siteId?: string
  projectId?: string
} & CashPaymentType

export type CashReceiptItemType = {
  id: string
  cashReceiptId: string
  accountId: string
  amount: number
  lcAmount: number
  description: string
  createdAt: string
  updatedAt: string
}

export type CashReceiptType = {
  id: string
  type: string
  number: string
  itemsCount: 0
  approvalsCount: 0
  currencyId: string
  exchangeRate: 0
  totalAmount: 0
  lcTotalAmount: 0
  accountId: string
  memo: string
  voucherNumber: string
  transactionDate: string
  status: string
  companyId: string
  parentCompanyId: string
  departmentId: string
  siteId: string
  projectId: string
  createdAt: string
  updatedAt: string
  createdBy: string
  account: AccountType
  currency: CurrenciesType
  items: (CashReceiptItemType & {
    account: AccountType
  })[]
  approvals: ApproverType[]
  department: CodeNameType
  site: CodeNameType
  project: ProjectType
  createdByUser: UserOutlineType
}
