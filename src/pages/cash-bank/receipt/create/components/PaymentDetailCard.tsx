import AccountsQueryMethods, { ACCOUNT_LIST_QUERY_KEY } from '@/api/services/account/query'
import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { AccountType } from '@/types/accountTypes'
import {
  Autocomplete,
  Button,
  Card,
  CardContent,
  Checkbox,
  debounce,
  FormControlLabel,
  Grid,
  TextField,
  Typography
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'
import { Controller, useFormContext, useWatch } from 'react-hook-form'
import { useFilePicker } from 'use-file-picker'
import { CreateCashReceiptPayload, CreatePaymentPayload } from '../../config/types'
import { isNullOrUndefined } from '@/utils/helper'
import CurrencyField from '@/components/numeric/CurrencyField'

const ReceiptDetailCard = () => {
  const [selectedItem, setSelectedItem] = useState<AccountType>()
  const [itemQuery, setItemQuery] = useState('')
  const { control } = useFormContext<CreateCashReceiptPayload>()
  const { openFilePicker, filesContent } = useFilePicker({
    readAs: 'DataURL',
    accept: 'image/jpeg, image/png'
  })

  const { data: itemsListResponse, remove: removeItemsListResponse } = useQuery({
    enabled: !!itemQuery,
    queryKey: [ACCOUNT_LIST_QUERY_KEY, itemQuery],
    queryFn: () =>
      AccountsQueryMethods.getAccountList({
        limit: Number.MAX_SAFE_INTEGER,
        level: 1,
        search: itemQuery,
        accountTypeCodes: 'CASH_BANK'
      })
  })

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-4'>
          <div className='flex justify-between'>
            <Typography variant='h5'>Detil Penerimaan</Typography>
          </div>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='transactionDate'
                rules={{ required: true }}
                render={({ field, fieldState: { error } }) => (
                  <AppReactDatepicker
                    selected={field.value ? new Date(field.value) : null}
                    onChange={(date: Date | null) => field.onChange(date?.toISOString().split('T')[0])}
                    placeholderText='Pilih tanggal'
                    dateFormat='dd/MM/yyyy'
                    customInput={
                      <TextField
                        fullWidth
                        label='Tanggal Penerimaan'
                        error={!!error}
                        helperText={error?.message}
                        InputProps={{
                          readOnly: true
                        }}
                      />
                    }
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='accountId'
                rules={{ required: true }}
                render={({ field, fieldState: { error } }) => (
                  <Autocomplete
                    key={JSON.stringify(selectedItem)}
                    value={selectedItem}
                    onInputChange={debounce((e, newValue, reason) => {
                      if (reason === 'input') {
                        setItemQuery(newValue as string)
                      }
                    }, 700)}
                    options={itemsListResponse?.items ?? []}
                    getOptionLabel={(option: AccountType) => `[${option.code}] ${option.name}`}
                    freeSolo={!itemQuery}
                    noOptionsText='Akun tidak ditemukan'
                    onChange={(e, newValue: AccountType) => {
                      if (newValue) {
                        setSelectedItem(newValue)
                        field.onChange(newValue.id)
                        removeItemsListResponse()
                      }
                    }}
                    renderInput={params => (
                      <TextField
                        {...params}
                        InputProps={{
                          ...params.InputProps,
                          onKeyDown: e => {
                            if (e.key === 'Enter') {
                              e.stopPropagation()
                            }
                          }
                        }}
                        error={!!error}
                        placeholder='Cari akun perkiraan'
                        label='Diterima di Akun'
                      />
                    )}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label='No Voucher'
                fullWidth
                helperText='No. Voucher akan terisi otomatis jika tidak diinput'
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='memo'
                render={({ field }) => <TextField {...field} fullWidth label='Memo' />}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='items'
                render={({ field: { onChange, value } }) => {
                  const totalAmount = value.reduce((acc, item) => acc + item.amount, 0)
                  return (
                    <TextField
                      label='Nominal Diterima'
                      fullWidth
                      value={totalAmount}
                      InputProps={{ inputComponent: CurrencyField as any }}
                    />
                  )
                }}
              />
            </Grid>
            {/* Currently not used */}
            {/* <Grid item xs={12}>
              <AppReactDatepicker
                boxProps={{ className: 'is-full' }}
                // selected={value ? toDate(value) : undefined}
                // onChange={(date: Date) => onChange(formatISO(date))}
                dateFormat='eeee dd/MM/yyyy'
                customInput={
                  <TextField
                    fullWidth
                    label='Tanggal Bayar'
                    className='flex-1'
                    InputProps={{
                      readOnly: true
                    }}
                  />
                }
              />
            </Grid> */}
            {/* <Grid item xs={12}>
              <Controller
                control={control}
                name='useCheckNumber'
                render={({ field }) => (
                  <FormControlLabel
                    label={<Typography>Gunakan Nomor Cek</Typography>}
                    control={<Checkbox {...field} />}
                  />
                )}
              />
            </Grid> */}
            {/* {!!useCheckNumber && (
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='checkNumber'
                  rules={{
                    validate: value => {
                      if (isNullOrUndefined(value) && !!useCheckNumber) {
                        return 'Silahkan isi nomor cek'
                      }
                      return true
                    }
                  }}
                  render={({ field, fieldState: { error } }) => (
                    <TextField fullWidth label='Nomor Cek' {...field} error={!!error} />
                  )}
                />
              </Grid>
            )} */}
            {/* Currently not used */}
            {/* <Grid item xs={12}>
              <div className='flex flex-col gap-2'>
                <Typography variant='subtitle1' marginBottom={2}>
                  Unggah Bukti Transaksi
                </Typography>
                <div className='flex items-center gap-4'>
                  <TextField
                    size='small'
                    fullWidth
                    value={filesContent?.[0]?.name}
                    placeholder='Tidak ada file dipilih'
                    aria-readonly
                    className='flex-1'
                  />
                  <Button variant='contained' onClick={() => openFilePicker()}>
                    Unggah
                  </Button>
                </div>
              </div>
            </Grid> */}
          </Grid>
        </CardContent>
      </Card>
    </>
  )
}

export default ReceiptDetailCard
