import { toCurrency } from '@/utils/helper'
import { Autocomplete, Card, CardContent, CardHeader, debounce, TextField, Typography } from '@mui/material'
import { formatDate } from 'date-fns'
import { useState } from 'react'
import CashbankListTableSelection from './CashbankListTableSelection'

const ReconciliationCardResult = () => {
  const [itemSearchQuery, setItemSearchQuery] = useState('')
  return (
    <Card>
      <CardHeader
        className='bg-[#DBF7E8]'
        title={<Typography variant='h5'>Jurnal Equalindo360</Typography>}
      ></CardHeader>
      <CardContent className='flex flex-col gap-4 mt-4'>
        <div className='p-4 flex justify-between items-end bg-[#4C4E640D]'>
          <div className='flex flex-col gap-2'>
            <Typography className='font-semibold'><PERSON><PERSON></Typography>
            <Typography>[1001] Kas</Typography>
          </div>
          <Typography variant='h5' color='primary'>
            {toCurrency(0)}
          </Typography>
        </div>
        <Autocomplete
          // key={JSON.stringify(selectedItem)}
          filterOptions={x => x}
          isOptionEqualToValue={(option, value) => option.id === value.id}
          onInputChange={debounce((e, newValue, reason) => {
            if (reason === 'input') {
              setItemSearchQuery(newValue)
            }
          }, 700)}
          options={[]}
          freeSolo={!itemSearchQuery}
          fullWidth
          // value={selectedItem}
          onChange={(e, newValue) => {
            // setSelectedItem(newValue)
            // removeItemList()
          }}
          noOptionsText='Pencatatan tidak ditemukan'
          // loading={fetchItemsLoading}
          renderInput={params => (
            <TextField
              {...params}
              label=''
              placeholder='Cari no pencatatan, nominal transaksi, atau memo'
              variant='outlined'
              fullWidth
              size='small'
              InputProps={{
                ...params.InputProps,
                startAdornment: <i className='ri-search-line text-textSecondary size-5 mx-2' />,
                // endAdornment: <>{fetchItemsLoading ? <CircularProgress /> : null}</>,
                onKeyDown: e => {
                  if (e.key === 'Enter') {
                    e.stopPropagation()
                  }
                }
              }}
            />
          )}
          getOptionLabel={(option: any) => `${option.number} | ${option.name} | ${option.brandName}`}
          renderOption={(props, option) => {
            const { key, ...optionProps } = props
            return (
              <li key={key} {...optionProps}>
                <Typography>
                  {option.number} | {option.name} | {option.brandName}
                </Typography>
              </li>
            )
          }}
        />
        <div className='flex justify-between items-center'>
          <small>
            {formatDate(Date.now(), 'dd/MM/yyyy')} - {formatDate(Date.now(), 'dd/MM/yyyy')}
          </small>
          <small>6 Transaksi</small>
        </div>
        <div>
          <CashbankListTableSelection />
        </div>
      </CardContent>
    </Card>
  )
}

export default ReconciliationCardResult
