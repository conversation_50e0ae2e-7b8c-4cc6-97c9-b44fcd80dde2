import CurrencyField from '@/components/numeric/CurrencyField'
import { toCurrency } from '@/utils/helper'
import { Card, CardContent, CardHeader, TextField, Typography } from '@mui/material'

const ReconciliationBalanceCard = () => {
  return (
    <Card>
      <CardHeader className='bg-[#DBF7E8]' title={<Typography variant='h5'>Saldo Aktual</Typography>}></CardHeader>
      <CardContent className='flex flex-col gap-4 mt-4'>
        <div className='p-4 flex justify-between items-end bg-[#4C4E640D]'>
          <div className='flex flex-col gap-2'>
            <Typography className='font-semibold'>Saldo Aktual</Typography>
            <Typography>Rekening Koran</Typography>
          </div>
          <TextField
            InputProps={{ inputComponent: CurrencyField as any }}
            value={0}
            size='small'
            className='w-[40%] bg-white'
          />
        </div>
      </CardContent>
    </Card>
  )
}

export default ReconciliationBalanceCard
