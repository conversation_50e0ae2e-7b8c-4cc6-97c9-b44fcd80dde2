import { toCurrency } from '@/utils/helper'
import { Card, CardContent, Typography } from '@mui/material'

const ReconciliationSummary = () => {
  return (
    <Card>
      <CardContent>
        <div className='flex justify-between gap-4 items-center'>
          <div className='flex flex-col rounded-[8px] px-4 py-2 bg-[#DBF7E8] is-full'>
            <small>Total Saldo Jurnal</small>
            <Typography variant='h5' color='primary'>
              {toCurrency(0)}
            </Typography>
          </div>
          <div className='flex flex-col rounded-[8px] px-4 py-2 bg-[#4C4E640D] is-full'>
            <small>Saldo Aktual</small>
            <Typography variant='h5'>{toCurrency(0)}</Typography>
          </div>
          <div className='flex flex-col rounded-[8px] px-4 py-2 bg-[#4C4E640D] is-full'>
            <small>Selisih Saldo</small>
            <Typography variant='h5' color='error'>
              {toCurrency(0)}
            </Typography>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default ReconciliationSummary
