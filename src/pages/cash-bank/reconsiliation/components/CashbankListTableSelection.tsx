import React from 'react'
import { useReactTable, getCoreRowModel, flexRender, ColumnDef, RowSelectionState } from '@tanstack/react-table'
import { Checkbox, Typography } from '@mui/material'
import classNames from 'classnames'
import { formatDate } from 'date-fns'

type ExtendedColumnDef<T> = ColumnDef<T> & {
  align?: 'left' | 'right' | 'center'
}

type CashbankData = {
  id: string
  type: string // e.g. "Penerimaan" or "Pembayaran"
  code: string // e.g. "ACC/PDPT/072025/00001"
  description: string // e.g. "Penerimaan lorem ipsum"
  date: string // e.g. "11/05/2025"
  debit: number
  credit: number
}

const formatCurrency = (value: number) => {
  if (value === 0) return 'Rp 0'
  return `Rp ${value.toLocaleString('id-ID')}`
}

const columns: ExtendedColumnDef<CashbankData>[] = [
  {
    id: 'select',
    header: '',
    size: 20,
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onChange={row.getToggleSelectedHandler()}
        disabled={!row.getCanSelect()}
        indeterminate={row.getIsSomeSelected()}
      />
    ),
    enableSorting: false
  },
  {
    accessorKey: 'type',
    header: '',
    cell: ({ row }) => {
      const data = row.original
      return (
        <div className='flex flex-col space-y-1'>
          <Typography className='text-sm font-medium'>{data.type}</Typography>
          <Typography className='cursor-pointer text-sm font-medium' color='primary'>
            {data.code}
          </Typography>
          <span className='text-xs text-gray-500'>{data.description}</span>
          <span className='text-xs text-gray-400'>{formatDate(data.date, 'dd/MM/yyyy')}</span>
        </div>
      )
    },
    size: 300
  },
  {
    accessorKey: 'debit',
    header: '',
    cell: ({ getValue }) => {
      const value = getValue<number>()
      return (
        <div className='flex flex-col gap-1'>
          <small>Debit</small>
          <span className={`text-sm font-medium ${value > 0 ? 'text-error' : 'text-gray-700'}`}>
            {formatCurrency(value)}
          </span>
        </div>
      )
    },
    align: 'left'
  },
  {
    accessorKey: 'credit',
    header: '',
    cell: ({ getValue }) => {
      const value = getValue<number>()
      return (
        <div className='flex flex-col gap-1'>
          <small>Kredit</small>
          <span className={`text-sm font-medium ${value > 0 ? 'text-green-600' : 'text-gray-700'}`}>
            {formatCurrency(value)}
          </span>
        </div>
      )
    },
    align: 'left',
    size: 150
  }
]

const defaultData: CashbankData[] = [
  {
    id: '1',
    type: 'Penerimaan',
    code: 'ACC/PDPT/072025/00001',
    description: 'Penerimaan lorem ipsum',
    date: '11/05/2025',
    debit: 0,
    credit: ********
  },
  {
    id: '2',
    type: 'Pembayaran',
    code: 'ACC/PDPT/072025/00001',
    description: 'Pembayaran lorem ipsum',
    date: '11/05/2025',
    debit: ********,
    credit: 0
  },
  {
    id: '3',
    type: 'Penerimaan',
    code: 'ACC/PDPT/072025/00001',
    description: 'Penerimaan lorem ipsum',
    date: '11/05/2025',
    debit: 0,
    credit: ********
  },
  {
    id: '4',
    type: 'Penerimaan',
    code: 'ACC/PDPT/072025/00001',
    description: 'Penerimaan lorem ipsum',
    date: '11/05/2025',
    debit: 0,
    credit: ********
  },
  {
    id: '5',
    type: 'Penerimaan',
    code: 'ACC/PDPT/072025/00001',
    description: 'Penerimaan lorem ipsum',
    date: '11/05/2025',
    debit: 0,
    credit: ********
  }
]

const CashbankListTableSelection: React.FC = () => {
  const [data] = React.useState(() => [...defaultData])
  const [rowSelection, setRowSelection] = React.useState<RowSelectionState>({})

  const table = useReactTable({
    data,
    columns,
    state: {
      rowSelection
    },
    onRowSelectionChange: setRowSelection,
    getCoreRowModel: getCoreRowModel(),
    enableRowSelection: true,
    enableMultiRowSelection: true
  })

  return (
    <div className='w-full'>
      <div className='overflow-x-auto md:rounded-lg'>
        <table className='min-w-full divide-y divide-gray-300'>
          <tbody className='bg-white divide-y divide-gray-200'>
            {table.getRowModel().rows.map(row => (
              <tr key={row.id} className='transition-colors duration-150'>
                {row.getVisibleCells().map(cell => (
                  <td
                    key={cell.id}
                    className={classNames(
                      'whitespace-nowrap',
                      (cell.column.columnDef as ExtendedColumnDef<CashbankData>).align === 'right'
                        ? 'text-right'
                        : 'text-left',
                      cell.column.columnDef?.size ? `w-[${cell.column.columnDef?.size}px] p-2` : 'p-4'
                    )}
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}

export default CashbankListTableSelection
