import { Button, Grid, Typography } from '@mui/material'
import ReconciliationFormCard from './components/ReconciliationFormCard'
import ReconciliationCardResult from './components/ReconciliationResultCard'
import ReconciliationBalanceCard from './components/ReconciliationBalanceCard'
import ReconciliationSummary from './components/ReconciliationSummaryCard'

const ReconsiliationPage = () => {
  return (
    <Grid container spacing={4}>
      <Grid item xs={12}></Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end'>
          <div className='flex flex-col'>
            <Typography variant='h4'>Rekonsiliasi Bank</Typography>
            <Typography>Pilih akun kas/bank dan lakukan rekonsiliasi</Typography>
          </div>
          <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
            <Button variant='contained'>Cetak Rekonsiliasi</Button>
          </div>
        </div>
      </Grid>
      <Grid item xs={12}>
        <ReconciliationFormCard />
      </Grid>
      <Grid item xs={12} md={6}>
        <ReconciliationCardResult />
      </Grid>
      <Grid item xs={12} md={6}>
        <ReconciliationBalanceCard />
      </Grid>
      <Grid item xs={12}>
        <ReconciliationSummary />
      </Grid>
    </Grid>
  )
}

export default ReconsiliationPage
