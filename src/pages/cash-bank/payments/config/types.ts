import { AccountType } from '@/types/accountTypes'
import { CodeNameType } from '@/types/common'
import { CurrenciesType } from '@/types/currenciesTypes'
import { ListParams } from '@/types/payload'
import { ProjectType } from '@/types/projectTypes'
import { PurchaseInvoice } from '@/types/purchaseInvoiceTypes'
import { ApproverType, UserOutlineType } from '@/types/userTypes'

export type PaymentTypes = 'GENERAL' | 'PURCHASE'
export type PaymentStatus = 'PROCESSED' | 'APPROVED' | 'REJECTED' | 'CANCELED'

export type PaymentParams = {
  status?: PaymentStatus
  type?: PaymentTypes
  purchaseInvoiceId?: string
  projectId?: string
} & ListParams

export type CreatePaymentItemPayload = {
  accountId: string
  amount: number
  description: string
  code?: string
  name?: string
}

export type CreatePaymentPayload = {
  type: PaymentParams['type']
  purchaseInvoiceId: string
  items: CreatePaymentItemPayload[]
  currencyId: string
  exchangeRate: number
  accountId: string
  approvals: { userId: string }[]
  memo: string
  useCheckNumber: boolean
  checkNumber: string
  departmentId: string
  siteId: string
  projectId: string
}

export type PaymentApprovalPayload = {
  paymentId: string
  approvalId: number
  status: string
  rejectionNote?: string
}

export type PaymentApproval = ApproverType

export type PaymentType = {
  id: string
  type: string
  number: string
  itemsCount: number
  approvalsCount: number
  currencyId: string
  exchangeRate: number
  totalAmount: number
  lcTotalAmount: number
  accountId: string
  memo: string
  checkNumber: string
  voucherNumber: string
  status: string
  companyId: string
  parentCompanyId: string
  departmentId: string
  siteId: string
  projectId: string
  createdAt: string
  updatedAt: string
  createdBy: string
  account: AccountType
  purchaseInvoiceId: string
  purchaseInvoice: PurchaseInvoice
  items: [string]
  approvals: PaymentApproval[]
  department: CodeNameType
  site: CodeNameType
  project: ProjectType
  createdByUser: UserOutlineType
}
