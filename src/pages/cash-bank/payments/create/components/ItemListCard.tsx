import {
  <PERSON>complete,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  debounce,
  FormHelperText,
  IconButton,
  TextField,
  Typography
} from '@mui/material'
import { useEffect, useMemo, useState } from 'react'
import DialogItems from './dialog-items'
import { Controller, useFieldArray, useFormContext, useWatch } from 'react-hook-form'
import { CreatePaymentItemPayload, CreatePaymentPayload } from '../../config/types'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { tableColumns } from '../config/table'
import Table from '@/components/table'
import { isNullOrUndefined, toCurrency } from '@/utils/helper'
import { PurchaseInvoice, PurchaseInvoiceStatus } from '@/types/purchaseInvoiceTypes'
import { useQuery } from '@tanstack/react-query'
import PurchaseInvoiceQueryMethods, {
  PURCHASE_INVOICE_DETAIL_QUERY_KEY,
  PURCHASE_INVOICE_LIST_QUERY_KEY
} from '@/api/services/purchase-invoice/query'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { statusChipValue } from '@/pages/purchase-invoice/list/config/table'
import { useSearchParams } from 'react-router-dom'

const ItemListCard = () => {
  const [searchParams] = useSearchParams()
  const [dialogItem, setDialogItem] = useState<boolean>(false)
  const [selectedInvoice, setSelectedInvoice] = useState<PurchaseInvoice>()
  const [invoiceSearchQuery, setInvoiceSearchQuery] = useState('')
  const { control, reset, getValues } = useFormContext<CreatePaymentPayload>()
  const { remove, fields, append } = useFieldArray({ control, name: 'items' })

  const {
    data: invoiceList,
    remove: removeInvoiceList,
    isFetching: loadingItems
  } = useQuery({
    enabled: !!invoiceSearchQuery,
    queryKey: [PURCHASE_INVOICE_LIST_QUERY_KEY, invoiceSearchQuery],
    queryFn: () =>
      PurchaseInvoiceQueryMethods.getPaginatedPurchaseInvoices({
        limit: Number.MAX_SAFE_INTEGER,
        status: PurchaseInvoiceStatus.APPROVED,
        search: invoiceSearchQuery
      })
  })

  useQuery({
    enabled: !!searchParams.get('purchaseInvoiceId'),
    queryKey: [PURCHASE_INVOICE_DETAIL_QUERY_KEY, searchParams.get('purchaseInvoiceId')],
    queryFn: async () => {
      const res = await PurchaseInvoiceQueryMethods.getPurchaseInvoice(searchParams.get('purchaseInvoiceId'))
      if (res) setSelectedInvoice(res)
    }
  })

  const typeWatch = useWatch({ control, name: 'type' })

  const tableOptions: any = useMemo(
    () => ({
      data: fields ?? [],
      columns: tableColumns({
        delete: index => remove(index)
      }),
      initialState: {
        pagination: {
          pageSize: 10,
          pageIndex: 0
        }
      },
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [fields, remove]
  )

  const table = useReactTable(tableOptions)

  const handleAddItem = (item: CreatePaymentItemPayload) => {
    append(item)
    setDialogItem(false)
  }

  const removePurchaseInvoice = () => {
    reset({
      ...getValues(),
      purchaseInvoiceId: null
    })
    setSelectedInvoice(null)
  }

  useEffect(() => {
    if (typeWatch !== 'PURCHASE') {
      removePurchaseInvoice()
    }
  }, [typeWatch])

  if (typeWatch === 'GENERAL') {
    return (
      <>
        <Card>
          <CardContent className='flex flex-col gap-4'>
            <div className='flex justify-between'>
              <Typography variant='h5'>List Item</Typography>
              <Button onClick={() => setDialogItem(true)} variant='outlined'>
                Tambah Item
              </Button>
            </div>
            <div>
              {fields?.length > 0 ? (
                <div className='flex flex-col gap-4'>
                  <div className='rounded-[8px] shadow-md'>
                    <Table table={table} disablePagination headerColor='green' />
                  </div>
                  <div className='flex flex-col gap-1 rounded-md p-3 bg-[#DBF7E8]'>
                    <small>Total Pembayaran</small>
                    <Typography color='primary'>
                      {toCurrency(fields.reduce((acc, item) => acc + item.amount, 0))}
                    </Typography>
                  </div>
                </div>
              ) : (
                <Controller
                  control={control}
                  name='items'
                  rules={{ required: true }}
                  render={({ fieldState: { error } }) => (
                    <>
                      <div className='flex flex-col justify-center items-center gap-2'>
                        <Typography variant='h5'>Belum ada item</Typography>
                        <Typography variant='body1'>
                          Tambahkan detil item yang harus dibayar dengan tombol diatas
                        </Typography>
                      </div>
                      {!!error && <FormHelperText error>Wajib diisi.</FormHelperText>}
                    </>
                  )}
                />
              )}
            </div>
          </CardContent>
        </Card>
        {dialogItem && <DialogItems onSubmit={handleAddItem} open={dialogItem} setOpen={setDialogItem} />}
      </>
    )
  }
  if (typeWatch === 'PURCHASE') {
    return (
      <Card>
        <CardContent className='flex flex-col gap-4'>
          <div className='flex justify-between'>
            <Typography variant='h5'>Pembayaran Untuk Faktur</Typography>
          </div>
          {!!selectedInvoice ? (
            <>
              <div className='flex justify-between gap-2 p-4 rounded-[8px] bg-[#4C4E640D]'>
                <div className='flex flex-col gap-2'>
                  <div className='flex gap-2 items-center'>
                    <Typography variant='h5'>No. Faktur {selectedInvoice?.number}</Typography>
                    <Chip
                      label={statusChipValue[selectedInvoice?.status]?.label}
                      size='small'
                      variant='tonal'
                      color={statusChipValue[selectedInvoice?.status]?.color}
                    />
                  </div>
                  <small>{formatDate(selectedInvoice?.createdAt, 'eeee, dd/MM/yyyy', { locale: id })}</small>
                </div>
                <IconButton onClick={removePurchaseInvoice}>
                  <i className='ri-close-circle-line' />
                </IconButton>
              </div>
              <div className='grid grid-cols-2 md:grid-cols-4 gap-2'>
                <div className='flex flex-col gap-2 p-4 rounded-[8px] bg-[#4C4E640D]'>
                  <small>Sub Total Faktur</small>
                  <Typography className='font-semibold'>{toCurrency(selectedInvoice?.subTotalAmount)}</Typography>
                </div>
                <div className='flex flex-col gap-2 p-4 rounded-[8px] bg-[#4C4E640D]'>
                  <small>Biaya Lain Lain</small>
                  <Typography className='font-semibold'>{toCurrency(selectedInvoice?.otherAmount)}</Typography>
                </div>
                <div className='flex flex-col gap-2 p-4 rounded-[8px] bg-[#4C4E640D]'>
                  <small>Diskon Faktur</small>
                  <Typography className='font-semibold'>{toCurrency(selectedInvoice?.discountAmount)}</Typography>
                </div>
                <div className='flex flex-col gap-2 p-4 rounded-[8px] bg-[#DBF7E8]'>
                  <small>Total Faktur</small>
                  <Typography variant='h6' color='primary' className='font-semibold'>
                    {toCurrency(selectedInvoice?.totalAmount)}
                  </Typography>
                </div>
              </div>
            </>
          ) : (
            <Controller
              control={control}
              name='purchaseInvoiceId'
              rules={{
                validate: value => {
                  if (isNullOrUndefined(value) && typeWatch === 'PURCHASE') {
                    return 'Wajib diisi.'
                  }
                  return true
                }
              }}
              render={({ field, fieldState: { error } }) => (
                <Autocomplete
                  key={JSON.stringify(selectedInvoice)}
                  filterOptions={x => x}
                  isOptionEqualToValue={(option, value) => option.id === value.id}
                  onInputChange={debounce((e, newValue, reason) => {
                    if (reason === 'input') {
                      setInvoiceSearchQuery(newValue)
                    }
                  }, 700)}
                  options={invoiceList?.items ?? []}
                  freeSolo={!invoiceSearchQuery}
                  fullWidth
                  value={selectedInvoice}
                  onChange={(e, newValue: PurchaseInvoice) => {
                    if (newValue) {
                      setSelectedInvoice(newValue)
                      field.onChange(newValue.id)
                      removeInvoiceList()
                    }
                  }}
                  noOptionsText='Faktur tidak ditemukan'
                  // loading={fetchItemsLoading}
                  renderInput={params => (
                    <TextField
                      {...params}
                      label=''
                      placeholder='Cari Nomor Faktur'
                      variant='outlined'
                      fullWidth
                      {...(!!error && { error: true })}
                      InputProps={{
                        ...params.InputProps,
                        startAdornment: <i className='ri-search-line text-textSecondary size-5 mx-2' />,
                        endAdornment: <>{loadingItems ? <CircularProgress /> : null}</>,
                        onKeyDown: e => {
                          if (e.key === 'Enter') {
                            e.stopPropagation()
                          }
                        }
                      }}
                    />
                  )}
                  getOptionLabel={(option: PurchaseInvoice) => `${option.number} | ${option.vendor?.name}`}
                />
              )}
            />
          )}
        </CardContent>
      </Card>
    )
  }
}

export default ItemListCard
