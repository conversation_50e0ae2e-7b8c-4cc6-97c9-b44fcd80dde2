import { Card, CardContent, Typography } from '@mui/material'
import { usePayment } from '../../context/PaymentContext'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { toCurrency } from '@/utils/helper'

const PaymentDetailCard = () => {
  const { paymentData } = usePayment()
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Pembayaran</Typography>
        </div>
        <div className='rounded-md p-4 bg-[#4C4E640D] flex flex-col gap-2'>
          <Typography variant='h5' color='primary'>
            {paymentData?.number}
          </Typography>
          <Typography>
            {formatDate(paymentData?.createdAt ?? Date.now(), 'eeee, dd/MM/yyyy', { locale: id })}
          </Typography>
        </div>
        <div className='flex flex-col gap-2 w-full'>
          <small>Pembayaran Dengan</small>
          <Typography>{paymentData?.account?.name}</Typography>
        </div>
        <div className='flex flex-col gap-2 w-full'>
          <small>Memo</small>
          <Typography>{paymentData?.memo}</Typography>
        </div>
        <div className='flex flex-col gap-2 w-full'>
          <small>Nominal Bayar</small>
          <Typography>{toCurrency(paymentData?.totalAmount)}</Typography>
        </div>
        <div className='flex flex-col gap-2 w-full'>
          <small>Tanggal Pembayaran</small>
          <Typography>{formatDate(paymentData?.createdAt ?? Date.now(), 'dd/MM/yyyy', { locale: id })}</Typography>
        </div>
        <div className='flex flex-col md:flex-row md:justify-between gap-2 w-full'>
          <div className='flex flex-col gap-2 w-full'>
            <small>Tanggal Cek</small>
            <Typography>{formatDate(paymentData?.createdAt ?? Date.now(), 'dd/MM/yyyy', { locale: id })}</Typography>
          </div>
          <div className='flex flex-col gap-2 w-full'>
            <small>No Cek</small>
            <Typography>{paymentData?.checkNumber ?? '-'}</Typography>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default PaymentDetailCard
