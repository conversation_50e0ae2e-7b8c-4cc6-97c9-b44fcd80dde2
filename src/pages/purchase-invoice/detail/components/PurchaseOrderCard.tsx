// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

// Utils
import { toCurrency } from '@/utils/helper'

// Context
import { usePurchaseInvoice } from '../../context/PurchaseInvoiceContext'

const PurchaseOrderCard = () => {
  const { purchaseInvoiceData } = usePurchaseInvoice()

  if (!purchaseInvoiceData || !purchaseInvoiceData.orders?.length) return null

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <Typography variant='h5'>Purchase Order</Typography>
        {purchaseInvoiceData?.orders?.map(order => (
          <div className='bg-gray-50 rounded-lg p-4'>
            <div className='flex flex-col gap-3'>
              <div className='flex justify-between items-start'>
                <div className='flex flex-col gap-1'>
                  <Typography variant='body1' className='font-medium text-gray-700'>
                    No. PO: {order?.purchaseOrder?.number}
                  </Typography>
                  <Typography variant='body2' className='text-textSecondary'>
                    {formatDate(purchaseInvoiceData.createdAt, 'eeee dd/MM/yyyy, HH:mm', { locale: id })}
                  </Typography>
                </div>
              </div>

              <div className='flex justify-between items-center'>
                <Typography variant='body1' className='font-medium text-green-600'>
                  Total Purchase {toCurrency(order?.purchaseOrder?.lcGrandTotal)}
                </Typography>
              </div>

              {/* Down payment section if applicable */}
              {purchaseInvoiceData.isDownPayment && (
                <div className='bg-green-100 rounded-lg p-3 mt-2'>
                  <div className='flex justify-between items-center'>
                    <Typography className='text-green-800'>Jumlah Uang Muka</Typography>
                    <Typography variant='body1' className='font-medium text-green-600'>
                      {toCurrency(order?.downPaymentAmount ?? 0)}
                    </Typography>
                  </div>
                </div>
              )}

              {!purchaseInvoiceData.isDownPayment && <div />}
            </div>
          </div>
        ))} 
      </CardContent>
    </Card>
  )
}

export default PurchaseOrderCard
