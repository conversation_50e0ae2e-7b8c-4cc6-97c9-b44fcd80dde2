// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import { Box, Table, TableHead, TableBody, TableRow, TableCell, IconButton } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { useQuery } from '@tanstack/react-query'

// Utils
import { toCurrency } from '@/utils/helper'

// Context
import { usePurchaseInvoice } from '../../context/PurchaseInvoiceContext'

// API
import PurchaseInvoiceQueryMethods, {
  PURCHASE_INVOICE_DOWN_PAYMENTS_QUERY_KEY
} from '@/api/services/purchase-invoice/query'
import MgQueryMethods from '@/api/services/mg/query'

const PurchaseOrderCard = () => {
  const { purchaseInvoiceData } = usePurchaseInvoice()

  // Fetch down payment invoices for the first purchase order
  const firstOrder = purchaseInvoiceData?.orders?.[0]
  const { data: downPaymentInvoices = [], isLoading: isLoadingDownPayments } = useQuery({
    queryKey: [PURCHASE_INVOICE_DOWN_PAYMENTS_QUERY_KEY, firstOrder?.purchaseOrderId],
    queryFn: () => PurchaseInvoiceQueryMethods.getPurchaseInvoiceDownPayments(firstOrder!.purchaseOrderId),
    enabled: !!firstOrder?.purchaseOrderId && !purchaseInvoiceData?.isDownPayment
  })

  // Fetch incoming material details for each order to get complete item information
  const incomingMaterialQueries =
    purchaseInvoiceData?.orders?.map(order =>
      useQuery({
        queryKey: ['incoming-material', order.incomingMaterialId],
        queryFn: () => MgQueryMethods.getIm(order.incomingMaterialId),
        enabled: !!order.incomingMaterialId && !purchaseInvoiceData?.isDownPayment
      })
    ) || []

  if (!purchaseInvoiceData || !purchaseInvoiceData.orders?.length) return null

  // If this is a down payment invoice, show the simple view
  if (purchaseInvoiceData.isDownPayment) {
    return (
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <Typography variant='h5'>Purchase Order</Typography>
          {purchaseInvoiceData?.orders?.map((order, index) => (
            <div key={index} className='bg-gray-50 rounded-lg p-4'>
              <div className='flex flex-col gap-3'>
                <div className='flex justify-between items-start'>
                  <div className='flex flex-col gap-1'>
                    <Typography variant='body1' className='font-medium text-gray-700'>
                      No. PO: {order?.purchaseOrder?.number}
                    </Typography>
                    <Typography variant='body2' className='text-textSecondary'>
                      {formatDate(purchaseInvoiceData.createdAt, 'eeee dd/MM/yyyy, HH:mm', { locale: id })}
                    </Typography>
                  </div>
                </div>

                <div className='flex justify-between items-center'>
                  <Typography variant='body1' className='font-medium text-green-600'>
                    Total Purchase {toCurrency(order?.purchaseOrder?.lcGrandTotal)}
                  </Typography>
                </div>

                <div className='bg-green-100 rounded-lg p-3 mt-2'>
                  <div className='flex justify-between items-center'>
                    <Typography className='text-green-800'>Jumlah Uang Muka</Typography>
                    <Typography variant='body1' className='font-medium text-green-600'>
                      {toCurrency(order?.downPaymentAmount ?? 0)}
                    </Typography>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    )
  }

  // For regular invoices (not down payment), show the detailed view
  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <Typography variant='h5'>Purchase Order</Typography>

        {purchaseInvoiceData?.orders?.map((order, orderIndex) => (
          <Box
            key={orderIndex}
            sx={{ backgroundColor: 'rgba(76, 78, 100, 0.05)', borderRadius: '8px', padding: '16px' }}
          >
            {/* Purchase Order Header */}
            <div className='flex flex-col gap-3 mb-6'>
              <div className='flex justify-between items-start'>
                <div className='flex flex-col gap-1'>
                  <Typography variant='body1' className='font-medium text-gray-700'>
                    No. PO: {order?.purchaseOrder?.number}
                  </Typography>
                  <Typography variant='body2' className='text-textSecondary'>
                    {formatDate(purchaseInvoiceData.createdAt, 'eeee dd/MM/yyyy, HH:mm', { locale: id })}
                  </Typography>
                </div>
              </div>

              <div className='flex justify-between items-center'>
                <Typography variant='body1' className='font-medium text-green-600'>
                  Total Purchase {toCurrency(order?.purchaseOrder?.lcGrandTotal)}
                </Typography>
              </div>
            </div>

            {/* Items Table - Barang */}
            <Box sx={{ marginBottom: '24px' }}>
              <Typography
                variant='h6'
                sx={{
                  marginBottom: '16px',
                  fontWeight: 600,
                  fontSize: '16px',
                  color: 'rgba(76, 78, 100, 0.87)'
                }}
              >
                Barang
              </Typography>

              <Box sx={{ border: '1px solid rgba(76, 78, 100, 0.12)', borderRadius: '8px', overflow: 'hidden' }}>
                <Table>
                  <TableHead sx={{ backgroundColor: 'rgba(76, 78, 100, 0.04)' }}>
                    <TableRow>
                      <TableCell
                        sx={{
                          padding: '12px 16px',
                          fontWeight: 500,
                          fontSize: '12px',
                          textTransform: 'uppercase',
                          color: 'rgba(76, 78, 100, 0.87)'
                        }}
                      >
                        Kode Barang
                      </TableCell>
                      <TableCell
                        sx={{
                          padding: '12px 16px',
                          fontWeight: 500,
                          fontSize: '12px',
                          textTransform: 'uppercase',
                          color: 'rgba(76, 78, 100, 0.87)'
                        }}
                      >
                        Nama Item
                      </TableCell>
                      <TableCell
                        sx={{
                          padding: '12px 16px',
                          fontWeight: 500,
                          fontSize: '12px',
                          textTransform: 'uppercase',
                          color: 'rgba(76, 78, 100, 0.87)'
                        }}
                      >
                        Harga Satuan
                      </TableCell>
                      <TableCell
                        sx={{
                          padding: '12px 16px',
                          fontWeight: 500,
                          fontSize: '12px',
                          textTransform: 'uppercase',
                          color: 'rgba(76, 78, 100, 0.87)'
                        }}
                      >
                        Qty
                      </TableCell>
                      <TableCell
                        sx={{
                          padding: '12px 16px',
                          fontWeight: 500,
                          fontSize: '12px',
                          textTransform: 'uppercase',
                          color: 'rgba(76, 78, 100, 0.87)'
                        }}
                      >
                        Sisa Qty
                      </TableCell>
                      <TableCell
                        sx={{
                          padding: '12px 16px',
                          fontWeight: 500,
                          fontSize: '12px',
                          textTransform: 'uppercase',
                          color: 'rgba(76, 78, 100, 0.87)'
                        }}
                      >
                        Total
                      </TableCell>
                      <TableCell
                        sx={{
                          padding: '12px 16px',
                          fontWeight: 500,
                          fontSize: '12px',
                          textTransform: 'uppercase',
                          color: 'rgba(76, 78, 100, 0.87)'
                        }}
                      >
                        Action
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {!order.items || order.items.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} sx={{ textAlign: 'center', padding: '20px' }}>
                          <Typography color='textSecondary'>No items found</Typography>
                        </TableCell>
                      </TableRow>
                    ) : (
                      order.items.map((item, itemIndex) => {
                        const total = (item.pricePerUnit || 0) * (item.quantity || 0)
                        // Get the corresponding incoming material data for this order
                        const incomingMaterialData = incomingMaterialQueries[orderIndex]?.data
                        // Find the matching item in the incoming material items
                        const imItem = incomingMaterialData?.items?.find(
                          imItem => imItem.id === item.incomingMaterialItemId
                        )

                        return (
                          <TableRow key={itemIndex} sx={{ borderBottom: '1px solid rgba(76, 78, 100, 0.12)' }}>
                            <TableCell
                              sx={{ padding: '15px 16px', fontSize: '14px', color: 'rgba(76, 78, 100, 0.87)' }}
                            >
                              {imItem?.item?.number || 'N/A'}
                            </TableCell>
                            <TableCell
                              sx={{ padding: '15px 16px', fontSize: '14px', color: 'rgba(76, 78, 100, 0.87)' }}
                            >
                              {imItem?.item?.name || 'N/A'}
                            </TableCell>
                            <TableCell
                              sx={{ padding: '15px 16px', fontSize: '14px', color: 'rgba(76, 78, 100, 0.87)' }}
                            >
                              {toCurrency(item.pricePerUnit || 0)}
                            </TableCell>
                            <TableCell
                              sx={{ padding: '15px 16px', fontSize: '14px', color: 'rgba(76, 78, 100, 0.87)' }}
                            >
                              {item.quantity || 0} {imItem?.quantityUnit || 'pcs'}
                            </TableCell>
                            <TableCell
                              sx={{ padding: '15px 16px', fontSize: '14px', color: 'rgba(76, 78, 100, 0.87)' }}
                            >
                              {imItem?.remainingQuantity || 0} {imItem?.quantityUnit || 'pcs'}
                            </TableCell>
                            <TableCell
                              sx={{ padding: '15px 16px', fontSize: '14px', color: 'rgba(76, 78, 100, 0.87)' }}
                            >
                              {toCurrency(total)}
                            </TableCell>
                            <TableCell sx={{ padding: '16px', textAlign: 'center' }}>
                              <IconButton size='small'>
                                <i className='ri-eye-line' />
                              </IconButton>
                            </TableCell>
                          </TableRow>
                        )
                      })
                    )}
                  </TableBody>
                </Table>
              </Box>
            </Box>

            {/* Down Payments Table - Uang Muka */}
            <Box sx={{ marginBottom: '24px' }}>
              <Typography
                variant='h6'
                sx={{
                  marginBottom: '16px',
                  fontWeight: 600,
                  fontSize: '16px',
                  color: 'rgba(76, 78, 100, 0.87)'
                }}
              >
                Uang Muka
              </Typography>

              <Box sx={{ border: '1px solid rgba(76, 78, 100, 0.12)', borderRadius: '8px', overflow: 'hidden' }}>
                <Table>
                  <TableHead sx={{ backgroundColor: 'rgba(76, 78, 100, 0.04)' }}>
                    <TableRow>
                      <TableCell
                        sx={{
                          padding: '12px 16px',
                          fontWeight: 500,
                          fontSize: '12px',
                          textTransform: 'uppercase',
                          color: 'rgba(76, 78, 100, 0.87)'
                        }}
                      >
                        No. Faktur
                      </TableCell>
                      <TableCell
                        sx={{
                          padding: '12px 16px',
                          fontWeight: 500,
                          fontSize: '12px',
                          textTransform: 'uppercase',
                          color: 'rgba(76, 78, 100, 0.87)'
                        }}
                      >
                        Tanggal Faktur
                      </TableCell>
                      <TableCell
                        sx={{
                          padding: '12px 16px',
                          fontWeight: 500,
                          fontSize: '12px',
                          textTransform: 'uppercase',
                          color: 'rgba(76, 78, 100, 0.87)'
                        }}
                      >
                        Jumlah Uang Muka
                      </TableCell>
                      <TableCell
                        sx={{
                          padding: '12px 16px',
                          fontWeight: 500,
                          fontSize: '12px',
                          textTransform: 'uppercase',
                          color: 'rgba(76, 78, 100, 0.87)'
                        }}
                      >
                        Action
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {isLoadingDownPayments ? (
                      <TableRow>
                        <TableCell colSpan={4} sx={{ textAlign: 'center', padding: '20px' }}>
                          <Typography color='textSecondary'>Loading down payment invoices...</Typography>
                        </TableCell>
                      </TableRow>
                    ) : downPaymentInvoices.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={4} sx={{ textAlign: 'center', padding: '20px' }}>
                          <Typography color='textSecondary'>No down payment invoices found</Typography>
                        </TableCell>
                      </TableRow>
                    ) : (
                      downPaymentInvoices.map((invoice, index) => (
                        <TableRow key={index} sx={{ borderBottom: '1px solid rgba(76, 78, 100, 0.12)' }}>
                          <TableCell sx={{ padding: '15px 16px', fontSize: '14px', color: 'rgba(76, 78, 100, 0.87)' }}>
                            {invoice.purchaseInvoice?.number || 'N/A'}
                          </TableCell>
                          <TableCell sx={{ padding: '15px 16px', fontSize: '14px', color: 'rgba(76, 78, 100, 0.87)' }}>
                            {invoice.purchaseInvoice?.invoiceDate
                              ? formatDate(invoice.purchaseInvoice.invoiceDate, 'dd/MM/yyyy', { locale: id })
                              : 'N/A'}
                          </TableCell>
                          <TableCell sx={{ padding: '15px 16px', fontSize: '14px', color: 'rgba(76, 78, 100, 0.87)' }}>
                            {toCurrency(invoice.downPaymentAmount || 0)}
                          </TableCell>
                          <TableCell sx={{ padding: '16px', textAlign: 'center' }}>
                            <IconButton size='small'>
                              <i className='ri-eye-line' />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </Box>
            </Box>

            {/* Summary Section */}
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '12px 16px',
                  backgroundColor: 'rgba(76, 78, 100, 0.04)',
                  borderRadius: '8px'
                }}
              >
                <Typography sx={{ fontWeight: 500, fontSize: '14px', color: 'rgba(76, 78, 100, 0.87)' }}>
                  Sub Total
                </Typography>
                <Typography sx={{ fontWeight: 600, fontSize: '14px', color: 'rgba(76, 78, 100, 0.87)' }}>
                  {toCurrency(purchaseInvoiceData.subTotalAmount || 0)}
                </Typography>
              </Box>

              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '12px 16px',
                  backgroundColor: 'rgba(76, 78, 100, 0.04)',
                  borderRadius: '8px'
                }}
              >
                <Typography sx={{ fontWeight: 500, fontSize: '14px', color: 'rgba(76, 78, 100, 0.87)' }}>
                  Total Uang Muka
                </Typography>
                <Typography sx={{ fontWeight: 600, fontSize: '14px', color: 'rgba(76, 78, 100, 0.87)' }}>
                  {toCurrency(
                    downPaymentInvoices.reduce((total, invoice) => total + (invoice.downPaymentAmount || 0), 0)
                  )}
                </Typography>
              </Box>

              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '12px 16px',
                  backgroundColor: 'rgba(76, 175, 80, 0.1)',
                  borderRadius: '8px'
                }}
              >
                <Typography sx={{ fontWeight: 600, fontSize: '16px', color: 'rgba(76, 175, 80, 1)' }}>
                  Total Bayar
                </Typography>
                <Typography sx={{ fontWeight: 700, fontSize: '16px', color: 'rgba(76, 175, 80, 1)' }}>
                  {toCurrency(
                    (purchaseInvoiceData.subTotalAmount || 0) -
                      downPaymentInvoices.reduce((total, invoice) => total + (invoice.downPaymentAmount || 0), 0)
                  )}
                </Typography>
              </Box>
            </Box>
          </Box>
        ))}
      </CardContent>
    </Card>
  )
}

export default PurchaseOrderCard
