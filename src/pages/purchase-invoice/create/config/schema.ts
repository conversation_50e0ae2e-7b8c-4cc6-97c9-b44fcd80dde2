import { PurchaseInvoiceDiscountType } from '@/types/purchaseInvoiceTypes'
import { z } from 'zod'

export const createPurchaseInvoiceSchemaDto = z.object({
  vendorId: z.string().min(1, 'Vendor wajib dipilih'),
  paymentTerms: z.string(),
  paymentDueDays: z.number(),
  vendorNumber: z.string().nullable().optional(),
  vendorName: z.string().nullable().optional(),
  invoiceDate: z.string().min(1, 'Tanggal Faktur wajib diisi'),
  note: z.string().optional().nullable(),
  isDownPayment: z.boolean().optional().nullable(),
  currencyId: z.string().nullable().optional(),
  exchangeRate: z.number().nullable().optional(),
  documentUploadId: z.string().optional().nullable(),
  documentContent: z.string().optional().nullable(),
  documentName: z.string().optional().nullable(),
  isGeneralPurchase: z.boolean().optional().nullable(),
  discountType: z.nativeEnum(PurchaseInvoiceDiscountType).optional().nullable(),
  discountValue: z.number().optional().nullable(),
  orders: z.array(
    z.object({
      purchaseOrderId: z.string(),
      incomingMaterialId: z.string().nullable().optional(),
      items: z
        .array(
          z.object({
            incomingMaterialItemId: z.number().nullable().optional(),
            pricePerUnit: z.number(),
            quantity: z.number()
          })
        )
        .nullable()
        .optional(),
      downPaymentAmount: z.number().nullable().optional(),
      downPaymentIds: z.array(z.number()).nullable().optional()
    })
  ),
  otherExpenses: z
    .array(
      z.object({
        accountId: z.string(),
        amount: z.number(),
        note: z.string().optional()
      })
    )
    .nullable()
    .optional(),
  departmentId: z.string(),
  siteId: z.string(),
  projectId: z.string().nullable().optional(),
  otherExpensesTotal: z.number().optional().nullable(),
  discountAmount: z.number().optional().nullable(),
  subtotal: z.number().optional().nullable(),
  grandTotal: z.number().optional().nullable()
})

export type PurchaseInvoiceDtoType = z.infer<typeof createPurchaseInvoiceSchemaDto>
