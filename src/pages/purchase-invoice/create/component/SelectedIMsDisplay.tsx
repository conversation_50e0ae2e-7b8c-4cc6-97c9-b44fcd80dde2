import {
  Box,
  Typography,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  IconButton,
  Grid,
  TextField,
  Button,
  Checkbox
} from '@mui/material'
import { format } from 'date-fns'
import { id } from 'date-fns/locale'
import { useQuery } from '@tanstack/react-query'
import { useMemo } from 'react'
import { useFormContext, useFieldArray, Controller } from 'react-hook-form'

import { ImType } from '@/types/mgTypes'
import { toCurrency } from '@/utils/helper'
import PurchaseInvoiceQueryMethods, {
  PURCHASE_INVOICE_DOWN_PAYMENTS_QUERY_KEY
} from '@/api/services/purchase-invoice/query'
import { PurchaseInvoiceDtoType } from '../config/schema'
import Currency<PERSON>ield from '@/components/numeric/CurrencyField'

interface SelectedIMsDisplayProps {
  selectedIMs: ImType[]
  vendorId: string
  onRemoveIM: (imId: string) => void
  orderArray: any
}

const SelectedIMsDisplay = ({ selectedIMs, vendorId, onRemoveIM, orderArray }: SelectedIMsDisplayProps) => {
  const { control } = useFormContext<PurchaseInvoiceDtoType>()

  // Use field array for orders
  const { fields: orderFields, update: updateOrder, remove: removeOrder } = orderArray

  const selectedPo = selectedIMs[0]?.purchaseOrder

  const {
    data: downPaymentInvoices = [],
    isLoading: isLoadingDownPayments,
    error: downPaymentError
  } = useQuery({
    queryKey: [PURCHASE_INVOICE_DOWN_PAYMENTS_QUERY_KEY, selectedPo?.id],
    queryFn: () => PurchaseInvoiceQueryMethods.getPurchaseInvoiceDownPayments(selectedPo!.id),
    enabled: !!selectedPo?.id // Only fetch when a purchase order is selected
  })

  // Calculate totals using useMemo for performance
  const calculations = useMemo(() => {
    // Calculate Sub Total from form data (orders)
    const subTotal = orderFields.reduce((total, order) => {
      const orderItems = order.items || []
      const orderTotal = orderItems.reduce((itemTotal, item) => {
        return itemTotal + (item.pricePerUnit || 0) * (item.quantity || 0)
      }, 0)
      return total + orderTotal
    }, 0)

    // Get Total Uang Muka from form state
    const totalDownPayment = orderFields[0]?.downPaymentAmount || 0

    // Calculate Total Bayar (Sub Total - Total Uang Muka)
    const totalPayable = subTotal - totalDownPayment

    return {
      subTotal,
      totalDownPayment,
      totalPayable
    }
  }, [orderFields])

  // Helper function to update item in form
  const updateOrderItem = (
    orderIndex: number,
    itemIndex: number,
    field: 'pricePerUnit' | 'quantity',
    value: number
  ) => {
    const currentOrder = orderFields[orderIndex]
    const updatedItems = [...(currentOrder.items || [])]
    updatedItems[itemIndex] = {
      ...updatedItems[itemIndex],
      [field]: value
    }

    updateOrder(orderIndex, {
      ...currentOrder,
      items: updatedItems
    })
  }

  // Helper function to remove item
  const removeOrderItem = (orderIndex: number, itemIndex: number) => {
    const currentOrder = orderFields[orderIndex]
    const updatedItems = [...(currentOrder.items || [])]
    updatedItems.splice(itemIndex, 1)

    if (updatedItems.length === 0) {
      // Remove entire order if no items left
      removeOrder(orderIndex)
    } else {
      updateOrder(orderIndex, {
        ...currentOrder,
        items: updatedItems
      })
    }
  }

  if (selectedIMs.length === 0) {
    return null
  }

  return (
    <Box
      sx={{
        backgroundColor: 'white',
        borderRadius: '10px',
        boxShadow: '0px 2px 10px 0px rgba(76, 78, 100, 0.22)',
        padding: '20px',
        display: 'flex',
        flexDirection: 'column',
        gap: '16px'
      }}
    >
      {/* Purchase Order Header */}
      <Typography variant='h6' sx={{ fontWeight: 500, fontSize: '20px', color: 'rgba(76, 78, 100, 0.87)' }}>
        Purchase Order
      </Typography>

      <Box
        sx={{
          backgroundColor: 'rgba(76, 78, 100, 0.05)',
          borderRadius: '8px',
          padding: '16px',
          display: 'flex',
          flexDirection: 'column',
          gap: '24px'
        }}
      >
        {/* Header with PO info and delete button */}
        <div className='flex justify-between items-start'>
          <div className='flex flex-col gap-2'>
            <Typography
              sx={{
                fontWeight: 500,
                fontSize: '20px',
                lineHeight: '1.334em',
                color: 'rgba(76, 78, 100, 0.87)'
              }}
            >
              No. PO: {selectedPo?.number || 'N/A'}
            </Typography>
            <Typography
              sx={{
                fontWeight: 400,
                fontSize: '14px',
                lineHeight: '1.5em',
                color: 'rgba(76, 78, 100, 0.6)'
              }}
            >
              {selectedIMs[0]?.createdAt
                ? format(new Date(selectedIMs[0].createdAt), 'eeee dd/MM/yyyy, HH:mm', { locale: id })
                : 'N/A'}
            </Typography>
            <Typography
              sx={{
                fontWeight: 700,
                fontSize: '16px',
                lineHeight: '1.334em',
                color: '#4BD88B'
              }}
            >
              Total Purchase {toCurrency(selectedIMs?.[0]?.purchaseOrder?.grandTotal)}
            </Typography>
          </div>
          <div className='flex items-center' style={{ height: '38px' }}>
            <Button
              variant='outlined'
              color='error'
              size='medium'
              onClick={() => onRemoveIM(selectedIMs[0]?.id)}
              sx={{
                borderRadius: '8px',
                borderColor: 'rgba(255, 77, 73, 0.5)',
                color: '#FF4D49',
                '&:hover': {
                  backgroundColor: 'rgba(255, 77, 73, 0.04)'
                }
              }}
            >
              Hapus PO
            </Button>
          </div>
        </div>

        {/* Barang Section */}
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
          <Typography
            sx={{
              fontWeight: 700,
              fontSize: '14px',
              lineHeight: '1.5em',
              color: 'rgba(76, 78, 100, 0.6)'
            }}
          >
            Barang
          </Typography>

          {/* Barang Table */}
          <Box
            sx={{
              backgroundColor: 'white',
              borderRadius: '10px',
              boxShadow: '0px 2px 10px 0px rgba(76, 78, 100, 0.22)',
              overflow: 'hidden'
            }}
          >
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: '#DBF7E8' }}>
                  <TableCell
                    sx={{
                      padding: '16px 16px 16px 20px',
                      width: '160px',
                      borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '2em',
                        letterSpacing: '1.42%',
                        textTransform: 'uppercase',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      Kode Barang
                    </Typography>
                  </TableCell>
                  <TableCell
                    sx={{
                      padding: '16px 16px 16px 20px',
                      borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '2em',
                        letterSpacing: '1.42%',
                        textTransform: 'uppercase',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      Nama Item
                    </Typography>
                  </TableCell>
                  <TableCell
                    sx={{
                      padding: '16px 16px 16px 20px',
                      borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '2em',
                        letterSpacing: '1.42%',
                        textTransform: 'uppercase',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      Harga Satuan
                    </Typography>
                  </TableCell>
                  <TableCell
                    sx={{
                      padding: '16px 16px 16px 20px',
                      borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '2em',
                        letterSpacing: '1.42%',
                        textTransform: 'uppercase',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      Qty
                    </Typography>
                  </TableCell>
                  <TableCell
                    sx={{
                      padding: '16px 16px 16px 20px',
                      width: '128px',
                      borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '2em',
                        letterSpacing: '1.42%',
                        textTransform: 'uppercase',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      Sisa Qty
                    </Typography>
                  </TableCell>
                  <TableCell
                    sx={{
                      padding: '16px 16px 16px 20px',
                      borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '2em',
                        letterSpacing: '1.42%',
                        textTransform: 'uppercase',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      Total
                    </Typography>
                  </TableCell>
                  {/* <TableCell
                    sx={{
                      padding: '16px 16px 16px 20px',
                      width: '128px',
                      borderBottom: '1px solid rgba(76, 78, 100, 0.12)',
                      textAlign: 'center'
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '2em',
                        letterSpacing: '1.42%',
                        textTransform: 'uppercase',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      Action
                    </Typography>
                  </TableCell> */}
                </TableRow>
              </TableHead>
              <TableBody>
                {orderFields.map((order, orderIndex) =>
                  order.items?.map((item, itemIndex) => {
                    // Find corresponding IM item for display data
                    const imItem = selectedIMs
                      .flatMap(im => im.items || [])
                      .find(imItem => imItem.id === item.incomingMaterialItemId)

                    return (
                      <TableRow
                        key={`${orderIndex}-${itemIndex}`}
                        sx={{ borderBottom: '1px solid rgba(76, 78, 100, 0.12)' }}
                      >
                        {/* Kode Barang */}
                        <TableCell
                          sx={{
                            padding: '15px 16px 15px 20px',
                            width: '160px'
                          }}
                        >
                          <Typography
                            sx={{
                              fontWeight: 400,
                              fontSize: '14px',
                              lineHeight: '1.5em',
                              color: 'rgba(76, 78, 100, 0.87)'
                            }}
                          >
                            {imItem?.item?.number || 'N/A'}
                          </Typography>
                        </TableCell>

                        {/* Nama Item */}
                        <TableCell sx={{ padding: '15px 16px 15px 20px' }}>
                          <Typography
                            sx={{
                              minWidth: 300,
                              fontWeight: 400,
                              fontSize: '14px',
                              lineHeight: '1.5em',
                              color: 'rgba(76, 78, 100, 0.87)'
                            }}
                          >
                            {imItem?.item?.name || 'N/A'}
                          </Typography>
                        </TableCell>

                        {/* Harga Satuan - Editable */}
                        <TableCell sx={{ padding: '15px 16px 15px 20px' }}>
                          <Controller
                            name={`orders.${orderIndex}.items.${itemIndex}.pricePerUnit`}
                            control={control}
                            render={({ field }) => (
                              <TextField
                                {...field}
                                size='small'
                                variant='outlined'
                                onChange={e => {
                                  const value = parseFloat(e.target.value) || 0
                                  field.onChange(value)
                                  updateOrderItem(orderIndex, itemIndex, 'pricePerUnit', value)
                                }}
                                InputProps={{
                                  inputComponent: CurrencyField as any,
                                  inputProps: {
                                    prefix: 'Rp',
                                    name: `orders.${orderIndex}.items.${itemIndex}.pricePerUnit`,
                                    onChange: (e: any) => {
                                      field.onChange(e.target.value)
                                    },
                                    value: field.value,
                                    allowLeadingZeros: false,
                                    isAllowed: ({ floatValue }) => floatValue >= 0 || floatValue === undefined
                                  },
                                  className: 'bg-white'
                                }}
                              />
                            )}
                          />
                        </TableCell>

                        {/* Qty - Editable */}
                        <TableCell sx={{ padding: '15px 16px 15px 20px' }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Controller
                              name={`orders.${orderIndex}.items.${itemIndex}.quantity`}
                              control={control}
                              render={({ field }) => (
                                <TextField
                                  {...field}
                                  type='number'
                                  size='small'
                                  variant='outlined'
                                  placeholder='0'
                                  onChange={e => {
                                    const value = parseFloat(e.target.value) || 0
                                    field.onChange(value)
                                    updateOrderItem(orderIndex, itemIndex, 'quantity', value)
                                  }}
                                  sx={{
                                    width: '80px',
                                    '& .MuiOutlinedInput-root': {
                                      fontSize: '14px',
                                      '& fieldset': {
                                        borderColor: 'rgba(76, 78, 100, 0.23)'
                                      }
                                    }
                                  }}
                                />
                              )}
                            />
                            <Typography
                              sx={{
                                fontWeight: 400,
                                fontSize: '14px',
                                color: 'rgba(76, 78, 100, 0.87)'
                              }}
                            >
                              {imItem?.quantityUnit || 'pcs'}
                            </Typography>
                          </Box>
                        </TableCell>

                        {/* Sisa Qty */}
                        <TableCell
                          sx={{
                            padding: '15px 16px 15px 20px',
                            width: '128px'
                          }}
                        >
                          <Typography
                            sx={{
                              fontWeight: 400,
                              fontSize: '14px',
                              lineHeight: '1.5em',
                              color: 'rgba(76, 78, 100, 0.87)'
                            }}
                          >
                            {Math.max(0, (imItem?.unbilledQuantity || 0) - (item.quantity || 0))}{' '}
                            {imItem?.quantityUnit || 'PCS'}
                          </Typography>
                        </TableCell>

                        {/* Total */}
                        <TableCell sx={{ padding: '15px 16px 15px 20px' }}>
                          <Typography
                            sx={{
                              fontWeight: 400,
                              fontSize: '14px',
                              lineHeight: '1.5em',
                              color: 'rgba(76, 78, 100, 0.87)'
                            }}
                          >
                            {toCurrency((item.pricePerUnit || 0) * (item.quantity || 0))}
                          </Typography>
                        </TableCell>

                        {/* Action */}
                        {/* <TableCell
                          sx={{
                            padding: '16px',
                            width: '128px',
                            textAlign: 'center'
                          }}
                        >
                          <IconButton onClick={() => removeOrderItem(orderIndex, itemIndex)}>
                            <i className='ri-delete-bin-line' />
                          </IconButton>
                          <IconButton>
                            <i className='ri-eye-line' />
                          </IconButton>
                        </TableCell> */}
                      </TableRow>
                    )
                  })
                )}
              </TableBody>
            </Table>
          </Box>
        </Box>

        {/* Uang Muka Section */}
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
          <Typography
            sx={{
              fontWeight: 700,
              fontSize: '14px',
              lineHeight: '1.5em',
              color: 'rgba(76, 78, 100, 0.6)'
            }}
          >
            Uang Muka
          </Typography>

          {/* Uang Muka Table */}
          <Box
            sx={{
              backgroundColor: 'white',
              borderRadius: '10px',
              boxShadow: '0px 2px 10px 0px rgba(76, 78, 100, 0.22)',
              overflow: 'hidden'
            }}
          >
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: '#DBF7E8' }}>
                  {/* Checkbox Header */}
                  <TableCell
                    sx={{
                      padding: '16px 8px 16px 20px',
                      width: '50px',
                      borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                    }}
                  >
                    <Checkbox
                      checked={
                        orderFields[0]?.downPaymentIds?.length === downPaymentInvoices.length &&
                        downPaymentInvoices.length > 0
                      }
                      indeterminate={
                        orderFields[0]?.downPaymentIds?.length > 0 &&
                        orderFields[0]?.downPaymentIds?.length < downPaymentInvoices.length
                      }
                      onChange={e => {
                        if (orderFields.length > 0) {
                          const currentOrder = orderFields[0]
                          const newDownPaymentIds = e.target.checked
                            ? downPaymentInvoices.map(inv => Number(inv.id))
                            : []

                          const totalDownPayment = downPaymentInvoices
                            .filter(invoice => newDownPaymentIds.includes(Number(invoice.id)))
                            .reduce((total, invoice) => total + (invoice.downPaymentAmount || 0), 0)

                          updateOrder(0, {
                            ...currentOrder,
                            downPaymentIds: newDownPaymentIds,
                            downPaymentAmount: totalDownPayment
                          })
                        }
                      }}
                      disabled={isLoadingDownPayments || downPaymentInvoices.length === 0 || orderFields.length === 0}
                    />
                  </TableCell>
                  <TableCell
                    sx={{
                      padding: '16px 16px 16px 20px',
                      borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '2em',
                        letterSpacing: '1.42%',
                        textTransform: 'uppercase',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      No. Faktur
                    </Typography>
                  </TableCell>
                  <TableCell
                    sx={{
                      padding: '16px 16px 16px 20px',
                      borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '2em',
                        letterSpacing: '1.42%',
                        textTransform: 'uppercase',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      Tanggal Faktur
                    </Typography>
                  </TableCell>
                  <TableCell
                    sx={{
                      padding: '16px 16px 16px 20px',
                      borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '2em',
                        letterSpacing: '1.42%',
                        textTransform: 'uppercase',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      Jumlah Uang Muka
                    </Typography>
                  </TableCell>
                  <TableCell
                    sx={{
                      padding: '16px 16px 16px 20px',
                      width: '128px',
                      borderBottom: '1px solid rgba(76, 78, 100, 0.12)',
                      textAlign: 'center'
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '2em',
                        letterSpacing: '1.42%',
                        textTransform: 'uppercase',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      Action
                    </Typography>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {isLoadingDownPayments ? (
                  <TableRow>
                    <TableCell colSpan={5} sx={{ textAlign: 'center', padding: '20px' }}>
                      <Typography color='textSecondary'>Loading down payment invoices...</Typography>
                    </TableCell>
                  </TableRow>
                ) : downPaymentError ? (
                  <TableRow>
                    <TableCell colSpan={5} sx={{ textAlign: 'center', padding: '20px' }}>
                      <Typography color='error'>Failed to load down payment invoices</Typography>
                    </TableCell>
                  </TableRow>
                ) : downPaymentInvoices.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} sx={{ textAlign: 'center', padding: '20px' }}>
                      <Typography color='textSecondary'>No down payment invoices found</Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  downPaymentInvoices.map(invoice => (
                    <TableRow key={invoice.id} sx={{ borderBottom: '1px solid rgba(76, 78, 100, 0.12)' }}>
                      {/* Checkbox Cell */}
                      <TableCell sx={{ padding: '8px 8px 8px 20px', width: '50px' }}>
                        <Checkbox
                          checked={orderFields[0]?.downPaymentIds?.includes(Number(invoice.id)) || false}
                          onChange={e => {
                            if (orderFields.length > 0) {
                              const currentOrder = orderFields[0]
                              const currentIds = currentOrder.downPaymentIds || []
                              const invoiceId = Number(invoice.id)

                              const newDownPaymentIds = e.target.checked
                                ? Array.from(new Set([...currentIds, invoiceId]))
                                : currentIds.filter(id => id !== invoiceId)

                              const totalDownPayment = downPaymentInvoices
                                .filter(inv => newDownPaymentIds.includes(Number(inv.id)))
                                .reduce((total, inv) => total + (inv.downPaymentAmount || 0), 0)

                              updateOrder(0, {
                                ...currentOrder,
                                downPaymentIds: newDownPaymentIds,
                                downPaymentAmount: totalDownPayment
                              })
                            }
                          }}
                          disabled={orderFields.length === 0}
                        />
                      </TableCell>
                      {/* No. Faktur */}
                      <TableCell sx={{ padding: '15px 16px 15px 20px' }}>
                        <Typography
                          sx={{
                            fontWeight: 400,
                            fontSize: '14px',
                            lineHeight: '1.5em',
                            color: '#4BD88B'
                          }}
                        >
                          {invoice.purchaseInvoiceId}
                        </Typography>
                      </TableCell>

                      {/* Tanggal Faktur */}
                      <TableCell sx={{ padding: '15px 16px 15px 20px' }}>
                        <Typography
                          sx={{
                            fontWeight: 400,
                            fontSize: '14px',
                            lineHeight: '1.5em',
                            color: 'rgba(76, 78, 100, 0.87)'
                          }}
                        >
                          {format(new Date(invoice.purchaseInvoice?.invoiceDate), 'dd/MM/yyyy')}
                        </Typography>
                      </TableCell>

                      {/* Jumlah Uang Muka */}
                      <TableCell sx={{ padding: '15px 16px 15px 20px' }}>
                        <Typography
                          sx={{
                            fontWeight: 400,
                            fontSize: '14px',
                            lineHeight: '1.5em',
                            color: 'rgba(76, 78, 100, 0.87)'
                          }}
                        >
                          {toCurrency(invoice.downPaymentAmount)}
                        </Typography>
                      </TableCell>

                      {/* Action */}
                      <TableCell
                        sx={{
                          padding: '16px',
                          width: '128px',
                          textAlign: 'center'
                        }}
                      >
                        <IconButton>
                          <i className='ri-eye-line' />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </Box>
        </Box>

        {/* Summary Section */}
        <Grid container spacing={2}>
          {/* Sub Total */}
          <Grid item xs={12} md={4}>
            <Box
              sx={{
                backgroundColor: 'rgba(76, 78, 100, 0.05)',
                borderRadius: '8px',
                padding: '8px 16px',
                display: 'flex',
                flexDirection: 'column',
                gap: '4px'
              }}
            >
              <Typography
                sx={{
                  fontWeight: 400,
                  fontSize: '12px',
                  lineHeight: '1em',
                  letterSpacing: '1.25%',
                  color: 'rgba(76, 78, 100, 0.6)'
                }}
              >
                Sub Total
              </Typography>
              <Typography
                sx={{
                  fontWeight: 600,
                  fontSize: '16px',
                  lineHeight: '1.5em',
                  letterSpacing: '0.94%',
                  color: 'rgba(76, 78, 100, 0.87)'
                }}
              >
                {toCurrency(calculations.subTotal)}
              </Typography>
            </Box>
          </Grid>

          {/* Total Uang Muka */}
          <Grid item xs={12} md={4}>
            <Box
              sx={{
                backgroundColor: 'rgba(76, 78, 100, 0.05)',
                borderRadius: '8px',
                padding: '8px 16px',
                display: 'flex',
                flexDirection: 'column',
                gap: '4px'
              }}
            >
              <Typography
                sx={{
                  fontWeight: 400,
                  fontSize: '12px',
                  lineHeight: '1em',
                  letterSpacing: '1.25%',
                  color: 'rgba(76, 78, 100, 0.6)'
                }}
              >
                Total Uang Muka
              </Typography>
              <Typography
                sx={{
                  fontWeight: 600,
                  fontSize: '16px',
                  lineHeight: '1.5em',
                  letterSpacing: '0.94%',
                  color: 'rgba(76, 78, 100, 0.87)'
                }}
              >
                {toCurrency(calculations.totalDownPayment)}
              </Typography>
            </Box>
          </Grid>

          {/* Total Bayar */}
          <Grid item xs={12} md={4}>
            <Box
              sx={{
                backgroundColor: '#DBF7E8',
                borderRadius: '8px',
                padding: '8px 16px',
                display: 'flex',
                flexDirection: 'column',
                gap: '4px'
              }}
            >
              <Typography
                sx={{
                  fontWeight: 400,
                  fontSize: '12px',
                  lineHeight: '1em',
                  letterSpacing: '1.25%',
                  color: 'rgba(76, 78, 100, 0.6)'
                }}
              >
                Total Bayar
              </Typography>
              <Typography
                sx={{
                  fontWeight: 600,
                  fontSize: '16px',
                  lineHeight: '1.5em',
                  letterSpacing: '0.94%',
                  color: '#4BD88B'
                }}
              >
                {toCurrency(calculations.totalPayable)}
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Box>
    </Box>
  )
}

export default SelectedIMsDisplay
