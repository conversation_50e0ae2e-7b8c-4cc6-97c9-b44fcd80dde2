import { Box, Typography, Table, TableHead, TableBody, TableRow, TableCell, IconButton, Grid } from '@mui/material'
import { format } from 'date-fns'
import { id } from 'date-fns/locale'
import { useQuery } from '@tanstack/react-query'
import { useMemo } from 'react'

import { PurchaseInvoiceType, PurchaseInvoiceDownPayment } from '@/types/purchaseInvoiceTypes'
import { toCurrency } from '@/utils/helper'
import PurchaseInvoiceQueryMethods from '@/api/services/purchase-invoice/query'

interface PurchaseInvoiceDisplayProps {
  purchaseInvoice: PurchaseInvoiceType
}

const PurchaseInvoiceDisplay = ({ purchaseInvoice }: PurchaseInvoiceDisplayProps) => {
  // Fetch down payment invoices for the same PO
  const {
    data: downPaymentInvoices = [],
    isLoading: isLoadingDownPayments,
    error: downPaymentError
  } = useQuery({
    queryKey: ['purchase-invoice-down-payments', purchaseInvoice.orders?.[0]?.purchaseOrderId],
    queryFn: () =>
      PurchaseInvoiceQueryMethods.getPurchaseInvoiceDownPayments(purchaseInvoice.orders?.[0]?.purchaseOrderId || '', {
        limit: Number.MAX_SAFE_INTEGER
      }),
    enabled: !!purchaseInvoice.orders?.[0]?.purchaseOrderId,
    select: data => data || []
  })

  // Calculate totals
  const calculations = useMemo(() => {
    // Calculate Sub Total from invoice items
    const subTotal =
      purchaseInvoice.orders?.reduce((total, order) => {
        const orderItems = order.items || []
        const orderTotal = orderItems.reduce((itemTotal, item) => {
          return itemTotal + (item.pricePerUnit || 0) * (item.quantity || 0)
        }, 0)
        return total + orderTotal
      }, 0) || 0

    // Calculate Total Uang Muka from down payment invoices
    const totalDownPayment = downPaymentInvoices.reduce((total, invoice) => {
      return total + (invoice.totalAmount || 0)
    }, 0)

    // Calculate Total Bayar (Sub Total - Total Uang Muka)
    const totalPayable = subTotal - totalDownPayment

    return {
      subTotal,
      totalDownPayment,
      totalPayable
    }
  }, [purchaseInvoice.orders, downPaymentInvoices])

  const selectedPo = purchaseInvoice.orders?.[0]?.purchaseOrder

  if (!purchaseInvoice.orders || purchaseInvoice.orders.length === 0) {
    return null
  }

  return (
    <Box
      sx={{
        backgroundColor: 'rgba(76, 78, 100, 0.05)',
        borderRadius: '8px',
        padding: '16px',
        display: 'flex',
        flexDirection: 'column',
        gap: '24px'
      }}
    >
      {/* Header with PO info */}
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
        <Typography
          sx={{
            fontWeight: 500,
            fontSize: '20px',
            lineHeight: '1.334em',
            color: 'rgba(76, 78, 100, 0.87)'
          }}
        >
          No. PO: {selectedPo?.number || 'N/A'}
        </Typography>
        <Typography
          sx={{
            fontWeight: 400,
            fontSize: '14px',
            lineHeight: '1.5em',
            color: 'rgba(76, 78, 100, 0.6)'
          }}
        >
          {purchaseInvoice.createdAt
            ? format(new Date(purchaseInvoice.createdAt), 'eeee dd/MM/yyyy, HH:mm', { locale: id })
            : 'N/A'}
        </Typography>
        <Typography
          sx={{
            fontWeight: 700,
            fontSize: '16px',
            lineHeight: '1.334em',
            color: '#4BD88B'
          }}
        >
          Total Purchase {toCurrency(selectedPo?.grandTotal || 0)}
        </Typography>
      </Box>

      {/* Barang Section */}
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
        <Typography
          sx={{
            fontWeight: 700,
            fontSize: '14px',
            lineHeight: '1.5em',
            color: 'rgba(76, 78, 100, 0.6)'
          }}
        >
          Barang
        </Typography>

        {/* Barang Table */}
        <Box
          sx={{
            backgroundColor: 'white',
            borderRadius: '10px',
            boxShadow: '0px 2px 10px 0px rgba(76, 78, 100, 0.22)',
            overflow: 'hidden'
          }}
        >
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: '1px' }}>
            <Table sx={{ minWidth: 650 }}>
              <TableHead>
                <TableRow sx={{ backgroundColor: '#DBF7E8', borderBottom: '1px solid rgba(76, 78, 100, 0.12)' }}>
                  <TableCell
                    sx={{
                      padding: '16px 16px 16px 20px',
                      width: '160px',
                      borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '2em',
                        letterSpacing: '1.42%',
                        textTransform: 'uppercase',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      Kode Barang
                    </Typography>
                  </TableCell>
                  <TableCell
                    sx={{
                      padding: '16px 16px 16px 20px',
                      borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '2em',
                        letterSpacing: '1.42%',
                        textTransform: 'uppercase',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      Nama Item
                    </Typography>
                  </TableCell>
                  <TableCell
                    sx={{
                      padding: '16px 16px 16px 20px',
                      borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '2em',
                        letterSpacing: '1.42%',
                        textTransform: 'uppercase',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      Harga Satuan
                    </Typography>
                  </TableCell>
                  <TableCell
                    sx={{
                      padding: '16px 16px 16px 20px',
                      borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '2em',
                        letterSpacing: '1.42%',
                        textTransform: 'uppercase',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      Qty
                    </Typography>
                  </TableCell>
                  <TableCell
                    sx={{
                      padding: '16px 16px 16px 20px',
                      width: '128px',
                      borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '2em',
                        letterSpacing: '1.42%',
                        textTransform: 'uppercase',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      Sisa Qty
                    </Typography>
                  </TableCell>
                  <TableCell
                    sx={{
                      padding: '16px 16px 16px 20px',
                      borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '2em',
                        letterSpacing: '1.42%',
                        textTransform: 'uppercase',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      Total
                    </Typography>
                  </TableCell>
                  <TableCell
                    sx={{
                      padding: '16px 16px 16px 20px',
                      width: '128px',
                      borderBottom: '1px solid rgba(76, 78, 100, 0.12)',
                      textAlign: 'center'
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '2em',
                        letterSpacing: '1.42%',
                        textTransform: 'uppercase',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      Action
                    </Typography>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {purchaseInvoice.orders?.map((order, orderIndex) =>
                  order.items?.map((item, itemIndex) => {
                    return (
                      <TableRow
                        key={`${orderIndex}-${itemIndex}`}
                        sx={{ borderBottom: '1px solid rgba(76, 78, 100, 0.12)' }}
                      >
                        {/* Kode Barang */}
                        <TableCell
                          sx={{
                            padding: '15px 16px 15px 20px',
                            width: '160px'
                          }}
                        >
                          <Typography
                            sx={{
                              fontWeight: 400,
                              fontSize: '14px',
                              lineHeight: '1.5em',
                              color: 'rgba(76, 78, 100, 0.87)'
                            }}
                          >
                            {item.incomingMaterialItem?.item?.number || 'N/A'}
                          </Typography>
                        </TableCell>

                        {/* Nama Item */}
                        <TableCell sx={{ padding: '15px 16px 15px 20px' }}>
                          <Typography
                            sx={{
                              fontWeight: 400,
                              fontSize: '14px',
                              lineHeight: '1.5em',
                              color: 'rgba(76, 78, 100, 0.87)'
                            }}
                          >
                            {item.incomingMaterialItem?.item?.name || 'N/A'}
                          </Typography>
                        </TableCell>

                        {/* Harga Satuan */}
                        <TableCell sx={{ padding: '15px 16px 15px 20px' }}>
                          <Typography
                            sx={{
                              fontWeight: 400,
                              fontSize: '14px',
                              lineHeight: '1.5em',
                              color: 'rgba(76, 78, 100, 0.87)'
                            }}
                          >
                            {toCurrency(item.pricePerUnit || 0)}
                          </Typography>
                        </TableCell>

                        {/* Qty */}
                        <TableCell sx={{ padding: '15px 16px 15px 20px' }}>
                          <Typography
                            sx={{
                              fontWeight: 400,
                              fontSize: '14px',
                              lineHeight: '1.5em',
                              color: 'rgba(76, 78, 100, 0.87)'
                            }}
                          >
                            {item.quantity} {item.quantityUnit || 'pcs'}
                          </Typography>
                        </TableCell>

                        {/* Sisa Qty */}
                        <TableCell
                          sx={{
                            padding: '15px 16px 15px 20px',
                            width: '128px'
                          }}
                        >
                          <Typography
                            sx={{
                              fontWeight: 400,
                              fontSize: '14px',
                              lineHeight: '1.5em',
                              color: 'rgba(76, 78, 100, 0.87)'
                            }}
                          >
                            {item.incomingMaterialItem?.remainingQuantity || 0} {item.quantityUnit || 'pcs'}
                          </Typography>
                        </TableCell>

                        {/* Total */}
                        <TableCell sx={{ padding: '15px 16px 15px 20px' }}>
                          <Typography
                            sx={{
                              fontWeight: 400,
                              fontSize: '14px',
                              lineHeight: '1.5em',
                              color: 'rgba(76, 78, 100, 0.87)'
                            }}
                          >
                            {toCurrency((item.pricePerUnit || 0) * (item.quantity || 0))}
                          </Typography>
                        </TableCell>

                        {/* Action */}
                        <TableCell
                          sx={{
                            padding: '16px',
                            width: '128px',
                            textAlign: 'center'
                          }}
                        >
                          <IconButton>
                            <i className='ri-eye-line' />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    )
                  })
                )}
              </TableBody>
            </Table>
          </Box>
        </Box>
      </Box>

      {/* Uang Muka Section */}
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
        <Typography
          sx={{
            fontWeight: 700,
            fontSize: '14px',
            lineHeight: '1.5em',
            color: 'rgba(76, 78, 100, 0.6)'
          }}
        >
          Uang Muka
        </Typography>

        {/* Uang Muka Table */}
        <Box
          sx={{
            backgroundColor: 'white',
            borderRadius: '10px',
            boxShadow: '0px 2px 10px 0px rgba(76, 78, 100, 0.22)',
            overflow: 'hidden'
          }}
        >
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: '1px' }}>
            <Table sx={{ minWidth: 650 }}>
              <TableHead>
                <TableRow sx={{ backgroundColor: '#DBF7E8', borderBottom: '1px solid rgba(76, 78, 100, 0.12)' }}>
                  <TableCell
                    sx={{
                      padding: '16px 16px 16px 20px',
                      borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '2em',
                        letterSpacing: '1.42%',
                        textTransform: 'uppercase',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      No. Faktur
                    </Typography>
                  </TableCell>
                  <TableCell
                    sx={{
                      padding: '16px 16px 16px 20px',
                      borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '2em',
                        letterSpacing: '1.42%',
                        textTransform: 'uppercase',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      Tanggal Faktur
                    </Typography>
                  </TableCell>
                  <TableCell
                    sx={{
                      padding: '16px 16px 16px 20px',
                      borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '2em',
                        letterSpacing: '1.42%',
                        textTransform: 'uppercase',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      Jumlah Uang Muka
                    </Typography>
                  </TableCell>
                  <TableCell
                    sx={{
                      padding: '16px 16px 16px 20px',
                      width: '128px',
                      borderBottom: '1px solid rgba(76, 78, 100, 0.12)',
                      textAlign: 'center'
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: 500,
                        fontSize: '12px',
                        lineHeight: '2em',
                        letterSpacing: '1.42%',
                        textTransform: 'uppercase',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      Action
                    </Typography>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {downPaymentInvoices.length > 0 ? (
                  downPaymentInvoices.map((invoice, index) => (
                    <TableRow key={index} sx={{ borderBottom: '1px solid rgba(76, 78, 100, 0.12)' }}>
                      {/* No. Faktur */}
                      <TableCell sx={{ padding: '15px 16px 15px 20px' }}>
                        <Typography
                          sx={{
                            fontWeight: 400,
                            fontSize: '14px',
                            lineHeight: '1.5em',
                            color: 'rgba(76, 78, 100, 0.87)'
                          }}
                        >
                          {invoice.number || 'N/A'}
                        </Typography>
                      </TableCell>

                      {/* Tanggal Faktur */}
                      <TableCell sx={{ padding: '15px 16px 15px 20px' }}>
                        <Typography
                          sx={{
                            fontWeight: 400,
                            fontSize: '14px',
                            lineHeight: '1.5em',
                            color: 'rgba(76, 78, 100, 0.87)'
                          }}
                        >
                          {invoice.invoiceDate
                            ? format(new Date(invoice.invoiceDate), 'dd/MM/yyyy', { locale: id })
                            : 'N/A'}
                        </Typography>
                      </TableCell>

                      {/* Jumlah Uang Muka */}
                      <TableCell sx={{ padding: '15px 16px 15px 20px' }}>
                        <Typography
                          sx={{
                            fontWeight: 400,
                            fontSize: '14px',
                            lineHeight: '1.5em',
                            color: 'rgba(76, 78, 100, 0.87)'
                          }}
                        >
                          {toCurrency(invoice.totalAmount || 0)}
                        </Typography>
                      </TableCell>

                      {/* Action */}
                      <TableCell
                        sx={{
                          padding: '16px',
                          width: '128px',
                          textAlign: 'center'
                        }}
                      >
                        <IconButton>
                          <i className='ri-eye-line' />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={4} sx={{ textAlign: 'center', padding: '20px' }}>
                      <Typography
                        sx={{
                          fontWeight: 400,
                          fontSize: '14px',
                          lineHeight: '1.5em',
                          color: 'rgba(76, 78, 100, 0.6)'
                        }}
                      >
                        Tidak ada data uang muka
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </Box>
        </Box>
      </Box>

      {/* Summary Section */}
      <Box
        sx={{
          backgroundColor: 'white',
          borderRadius: '10px',
          boxShadow: '0px 2px 10px 0px rgba(76, 78, 100, 0.22)',
          padding: '20px'
        }}
      >
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            {/* Left side - empty for layout */}
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              {/* Sub Total */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography
                  sx={{
                    fontWeight: 400,
                    fontSize: '14px',
                    lineHeight: '1.5em',
                    color: 'rgba(76, 78, 100, 0.87)'
                  }}
                >
                  Sub Total:
                </Typography>
                <Typography
                  sx={{
                    fontWeight: 500,
                    fontSize: '14px',
                    lineHeight: '1.5em',
                    color: 'rgba(76, 78, 100, 0.87)'
                  }}
                >
                  {toCurrency(calculations.subTotal)}
                </Typography>
              </Box>

              {/* Total Uang Muka */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography
                  sx={{
                    fontWeight: 400,
                    fontSize: '14px',
                    lineHeight: '1.5em',
                    color: 'rgba(76, 78, 100, 0.87)'
                  }}
                >
                  Total Uang Muka:
                </Typography>
                <Typography
                  sx={{
                    fontWeight: 500,
                    fontSize: '14px',
                    lineHeight: '1.5em',
                    color: 'rgba(76, 78, 100, 0.87)'
                  }}
                >
                  {toCurrency(calculations.totalDownPayment)}
                </Typography>
              </Box>

              {/* Divider */}
              <Box sx={{ height: '1px', backgroundColor: 'rgba(76, 78, 100, 0.12)' }} />

              {/* Total Bayar */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography
                  sx={{
                    fontWeight: 700,
                    fontSize: '16px',
                    lineHeight: '1.334em',
                    color: 'rgba(76, 78, 100, 0.87)'
                  }}
                >
                  Total Bayar:
                </Typography>
                <Typography
                  sx={{
                    fontWeight: 700,
                    fontSize: '16px',
                    lineHeight: '1.334em',
                    color: '#4BD88B'
                  }}
                >
                  {toCurrency(calculations.totalPayable)}
                </Typography>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Box>
    </Box>
  )
}

export default PurchaseInvoiceDisplay
