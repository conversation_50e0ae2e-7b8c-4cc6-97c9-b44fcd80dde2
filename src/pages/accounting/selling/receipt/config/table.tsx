import { createColumnHelper } from '@tanstack/react-table'
import { SellingReceiptType } from './types'
import { Chip, IconButton, Typography } from '@mui/material'
import { formatDate } from 'date-fns'

const columnHelper = createColumnHelper<SellingReceiptType>()

type RowAction = {
  onDetail: (row: SellingReceiptType) => void
}

export const tableColumns = (rowAction: RowAction) => [
  columnHelper.accessor('invoiceNumber', {
    header: 'No. Faktur'
  }),
  columnHelper.accessor('status', {
    header: 'Status',
    cell: ({ row }) => <Chip label={row.original.status} color='primary' variant='tonal' size='small' />
  }),
  columnHelper.accessor('invoiceDate', {
    header: 'Tanggal Faktur',
    cell: ({ row }) => formatDate(new Date(row.original.invoiceDate), 'eeee, dd/MM/yyyy')
  }),
  columnHelper.accessor('customer.name', {
    header: 'Customer/Pelanggan'
  }),
  columnHelper.accessor('memo', {
    header: 'Memo'
  }),
  columnHelper.accessor('totalAmount', {
    header: 'Total Faktur',
    cell: ({ row }) => (
      <Typography color='primary' className='font-semibold'>
        {row.original.totalAmount}
      </Typography>
    )
  }),
  columnHelper.display({
    id: 'actions',
    header: 'Aksi',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => rowAction.onDetail(row.original)}>
          <i className='ri-eye-line text-secondary' />
        </IconButton>
      )
    }
  })
]
