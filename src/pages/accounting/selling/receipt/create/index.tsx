import { Grid, Typography, Breadcrumbs, Button } from '@mui/material'
import { Link } from 'react-router-dom'
import { useRouter } from '@/routes/hooks'
import { FormProvider, useForm } from 'react-hook-form'
import ReceiptDetailCard from './components/PaymentDetailCard'
import ApprovalListCard from './components/ApprovalListCard'
import InvoiceCard from './components/InvoiceCard'
import { useMenu } from '@/components/menu/contexts/menuContext'

const CreateSellingReceipt = () => {
  const router = useRouter()
  const { setConfirmState } = useMenu()
  const methods = useForm()
  const { handleSubmit } = methods

  const onSubmit = (data: any) => {
    setConfirmState({
      open: true,
      title: 'Buat Penerimaan Penjualan',
      content:
        'Apakah kamu yakin akan membuat penerimaan penjualan untuk faktur ini? Pastikan semua data sudah benar, action ini tidak bisa diubah',
      confirmText: 'Buat Penerimaan Penjualan',
      onConfirm: () => {
        console.log(data)
      }
    })
  }

  return (
    <FormProvider {...methods}>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs>
            <Link to='#' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Penjualan</Typography>
            </Link>
            <Link to='/selling/invoice' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Faktur</Typography>
            </Link>
            <Link to='/selling/invoice/123' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Detil Faktur</Typography>
            </Link>
            <Typography>Buat Penerimaan</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end'>
            <div className='flex flex-col'>
              <Typography variant='h4'>Tambah Penerimaan</Typography>
              <Typography>Lengkapi data dan tambahkan pencatatan Penerimaan</Typography>
            </div>
            <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
              <Button
                // disabled={loadingMutate}
                onClick={() => router.back()}
                color='secondary'
                variant='outlined'
                className='is-full sm:is-auto'
              >
                Batalkan
              </Button>
              <Button
                variant='contained'
                // disabled={loadingMutate}
                onClick={handleSubmit(onSubmit)}
                className='is-full sm:is-auto'
              >
                Buat Penerimaan
              </Button>
            </div>
          </div>
        </Grid>
        <Grid item xs={12}>
          <InvoiceCard />
        </Grid>
        <Grid item xs={12} md={6}>
          <ReceiptDetailCard />
        </Grid>
        <Grid item xs={12} md={6}>
          <ApprovalListCard approverList={[]} />
        </Grid>
      </Grid>
    </FormProvider>
  )
}

export default CreateSellingReceipt
