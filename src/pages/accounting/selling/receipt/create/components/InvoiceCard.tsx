import { toCurrency } from '@/utils/helper'
import { Card, CardContent, Typography } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

const InvoiceCard = () => {
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Peneri<PERSON><PERSON> <PERSON>ri <PERSON></Typography>
        </div>
        <div className='flex p-4 flex-col gap-3 w-full rounded-md bg-[#4C4E640D]'>
          <Typography variant='h4'>No. Faktur {'123456'}</Typography>
          <Typography>{formatDate(new Date(), 'eeee, dd/MM/yyyy', { locale: id })}</Typography>
        </div>
        <div className='grid grid-cols-2 md:grid-cols-4 gap-4 w-full'>
          <div className='p-3 flex flex-col gap-2 w-full rounded-md bg-[#4C4E640D]'>
            <small>Sub Total Faktur</small>
            <Typography>{toCurrency(0)}</Typography>
          </div>
          <div className='p-3 flex flex-col gap-2 w-full rounded-md bg-[#4C4E640D]'>
            <small>Biaya Lain lain</small>
            <Typography>{toCurrency(0)}</Typography>
          </div>
          <div className='p-3 flex flex-col gap-2 w-full rounded-md bg-[#4C4E640D]'>
            <small>Diskon Faktur</small>
            <Typography>{toCurrency(0)}</Typography>
          </div>
          <div className='p-3 flex flex-col gap-2 w-full rounded-md bg-[#DBF7E8]'>
            <small>Total Faktur</small>
            <Typography variant='h5' color='primary' className='font-semibold'>
              {toCurrency(0)}
            </Typography>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default InvoiceCard
