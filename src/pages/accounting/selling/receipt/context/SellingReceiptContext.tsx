import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import usePartialState from '@/core/hooks/usePartialState'
import { ChildrenType } from '@/core/types'
import { CustomerType } from '@/types/customerTypes'
import { ListParams } from '@/types/payload'
import React from 'react'

type SellingReceiptContextProps = {
  isMobile: boolean
  sellingReceiptParams: ListParams
  setSellingReceiptParams: React.Dispatch<React.SetStateAction<ListParams>>
  setPartialSellingReceiptParams: (key: keyof ListParams, value: any) => void
}

const SellingReceiptContext = React.createContext<SellingReceiptContextProps>({} as SellingReceiptContextProps)

export const useSellingReceipt = () => {
  const context = React.useContext(SellingReceiptContext)

  if (!context) {
    throw new Error('useSellingReceipt must be used within SellingReceiptContext')
  }

  return context
}

export const SellingReceiptProvider = ({ children }: ChildrenType) => {
  const { isMobile } = useMobileScreen()
  const [sellingReceiptParams, setPartialSellingReceiptParams, setSellingReceiptParams] = usePartialState<ListParams>({
    page: 1,
    limit: 10
  })

  const value = {
    isMobile,
    sellingReceiptParams,
    setSellingReceiptParams,
    setPartialSellingReceiptParams
  }

  return <SellingReceiptContext.Provider value={value}>{children}</SellingReceiptContext.Provider>
}
