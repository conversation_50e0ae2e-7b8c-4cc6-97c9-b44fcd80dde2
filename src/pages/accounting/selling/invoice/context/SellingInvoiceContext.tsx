import usePartialState from '@/core/hooks/usePartialState'
import { ChildrenType } from '@/core/types'
import { CustomerType } from '@/types/customerTypes'
import { ListParams } from '@/types/payload'
import React, { createContext, useState } from 'react'

type SellingInvoiceContextProps = {
  invoicesParams: ListParams
  setInvoicesParams: React.Dispatch<React.SetStateAction<ListParams>>
  setPartialInvoicesParams: (key: keyof ListParams, value: any) => void
  selectedId: string
  setSelectedId: React.Dispatch<React.SetStateAction<string>>
}

export type SellingInvoicesType = {
  number: string
  status: string
  invoiceDate: string
  customer: CustomerType
  memo: string
  totalAmount: number
  id: string
  createdAt: string
}

const SellingInvoiceContext = createContext<SellingInvoiceContextProps>({} as SellingInvoiceContextProps)

export const useSellingInvoice = () => {
  const context = React.useContext(SellingInvoiceContext)
  if (context === undefined) {
    throw new Error('useSellingInvoice must be used within SellingInvoiceContext')
  }
  return context
}

export const SellingInvoiceProvider = ({ children }: ChildrenType) => {
  const [invoicesParams, setPartialInvoicesParams, setInvoicesParams] = usePartialState<ListParams>({
    page: 1,
    limit: 10
  })
  const [selectedId, setSelectedId] = useState<string>()

  const value = {
    invoicesParams,
    setInvoicesParams,
    setPartialInvoicesParams,
    selectedId,
    setSelectedId
  }
  return <SellingInvoiceContext.Provider value={value}>{children}</SellingInvoiceContext.Provider>
}
