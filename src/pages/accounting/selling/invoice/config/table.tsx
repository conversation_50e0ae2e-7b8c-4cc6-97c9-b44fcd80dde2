import { createColumnHelper } from '@tanstack/react-table'
import { SellingInvoicesType } from '../context/SellingInvoiceContext'
import { Chip, IconButton, Typography } from '@mui/material'
import { formatDate } from 'date-fns'

const columnHelper = createColumnHelper<SellingInvoicesType>()

type RowAction = {
  showDetail: (invoice: SellingInvoicesType) => void
}

export const tableColumns = (rowAction: RowAction) => [
  columnHelper.accessor('number', {
    header: 'No. Faktur'
  }),
  columnHelper.accessor('status', {
    header: 'Status',
    cell: ({ row }) => <Chip label={row.original.status} color='primary' />
  }),
  columnHelper.accessor('invoiceDate', {
    header: 'Tanggal Faktur',
    cell: ({ row }) => formatDate(new Date(row.original.invoiceDate), 'dd/MM/yyyy')
  }),
  columnHelper.accessor('customer.id', {
    header: 'Customer/Pelanggan',
    cell: ({ row }) => row.original.customer?.name
  }),
  columnHelper.accessor('memo', {
    header: 'Memo',
    cell: ({ row }) => row.original.memo
  }),
  columnHelper.accessor('totalAmount', {
    header: 'Total Faktur',
    cell: ({ row }) => (
      <Typography align='right' color='primary'>
        {row.original.totalAmount}
      </Typography>
    )
  }),
  columnHelper.display({
    id: 'actions',
    header: 'Action',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => rowAction.showDetail(row.original)}>
          <i className='ri-eye-line' />
        </IconButton>
      )
    }
  })
]
