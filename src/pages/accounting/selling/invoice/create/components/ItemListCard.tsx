import { <PERSON><PERSON>, <PERSON>, CardContent, Typography } from '@mui/material'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { tableColumns } from '../config/table'
import { useMemo, useState } from 'react'
import Table from '@/components/table'
import { toCurrency } from '@/utils/helper'
import { ItemType } from '@/types/companyTypes'
import AddItemInvoiceDialog from './dialogs/dialog-add-item'

const ItemListCard = () => {
  const [itemState, setItemState] = useState<{ open: boolean; item?: ItemType }>({ open: false })
  const tableOptions: any = useMemo(
    () => ({
      data: [],
      columns: tableColumns({ delete: () => {} }),
      initialState: {
        pagination: {
          pageSize: 10,
          pageIndex: 0
        }
      },
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    []
  )
  const table = useReactTable(tableOptions)
  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-4'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>List Barang</Typography>
            <Button variant='contained' onClick={() => setItemState(curr => ({ ...curr, open: true }))}>
              Tambah Barang
            </Button>
          </div>
          <div className='rounded-[8px] shadow-md'>
            <Table
              headerColor='green'
              table={table}
              emptyLabel={
                <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                  <Typography>Belum ada Barang</Typography>
                  <Typography className='text-sm text-gray-400'>
                    Semua Barang yang telah kamu buat akan ditampilkan di sini
                  </Typography>
                </td>
              }
            />
          </div>
          <div className='flex flex-col p-4 bg-[#DBF7E8] rounded-[8px]'>
            <small>Total Harga</small>
            <Typography color='primary'>{toCurrency(0)}</Typography>
          </div>
        </CardContent>
      </Card>
      {itemState?.open && (
        <AddItemInvoiceDialog
          open={itemState.open}
          setOpen={open => setItemState({ open, ...(!open && { item: undefined }) })}
        />
      )}
    </>
  )
}

export default ItemListCard
