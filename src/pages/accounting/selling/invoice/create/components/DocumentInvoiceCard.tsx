import { But<PERSON>, Card, CardContent, TextField, Typography } from '@mui/material'
import { useFilePicker } from 'use-file-picker'

const DocumentInvoiceCard = () => {
  const { filesContent, openFilePicker } = useFilePicker({
    readAs: 'DataURL',
    multiple: false,
    accept: 'image/png, image/jpeg, image/jpg'
  })
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-end'>
          <Typography variant='h5'><PERSON>ku<PERSON> Penyerta (Opsional)</Typography>
        </div>
        <div className='flex flex-col gap-3 flex-1'>
          <Typography className='font-semibold'>Unggah Do<PERSON>men</Typography>
          <div className='flex items-center gap-4'>
            <TextField
              // key={JSON.stringify(filesContent)}
              size='small'
              fullWidth
              value={filesContent?.[0]?.name}
              placeholder='Tidak ada file dipilih'
              aria-readonly
              className='flex-1'
            />
            <Button variant='contained' onClick={() => openFilePicker()}>
              Pilih File
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default DocumentInvoiceCard
