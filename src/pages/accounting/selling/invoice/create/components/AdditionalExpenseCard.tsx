import { But<PERSON>, Card, CardContent, Typography } from '@mui/material'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { useMemo, useState } from 'react'
import { additionalTableColumns } from '../config/table'
import Table from '@/components/table'
import DialogAddExpenses from './dialogs/dialog-add-additional-expense'

const AdditionalExpenses = () => {
  const [dialogExpenses, setDialogExpenses] = useState(false)
  const tableOptions: any = useMemo(
    () => ({
      data: [],
      columns: additionalTableColumns({ delete: () => {} }),
      initialState: {
        pagination: {
          pageSize: 10,
          pageIndex: 0
        }
      },
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    []
  )
  const table = useReactTable(tableOptions)
  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-4'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Biaya Lain-lain (Opsional)</Typography>
            <Button variant='contained' onClick={() => setDialogExpenses(true)}>
              Tambah
            </Button>
          </div>
          <div className='rounded-[8px] shadow-md'>
            <Table
              headerColor='green'
              table={table}
              emptyLabel={
                <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                  <Typography>Belum ada Data</Typography>
                  <Typography className='text-sm text-gray-400'>
                    Semua Biaya Lain-lain akan ditampilkan di sini
                  </Typography>
                </td>
              }
            />
          </div>
        </CardContent>
      </Card>
      {dialogExpenses && <DialogAddExpenses open={dialogExpenses} setOpen={setDialogExpenses} />}
    </>
  )
}

export default AdditionalExpenses
