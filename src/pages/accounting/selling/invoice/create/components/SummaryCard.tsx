import { toCurrency } from '@/utils/helper'
import { Card, CardContent, Typography } from '@mui/material'

const SummaryCard = () => {
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Ringkasan Pembayaran</Typography>
        </div>
        <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
          <div className='flex flex-col p-4 bg-[#4C4E640D] rounded-[8px]'>
            <small>Sub Total Faktur</small>
            <Typography color='primary'>{toCurrency(0)}</Typography>
          </div>
          <div className='flex flex-col p-4 bg-[#4C4E640D] rounded-[8px]'>
            <small>Biaya Lain-lain</small>
            <Typography color='primary'>{toCurrency(0)}</Typography>
          </div>
          <div className='flex flex-col p-4 bg-[#4C4E640D] rounded-[8px]'>
            <small>Diskon Faktur</small>
            <Typography color='primary'>{toCurrency(0)}</Typography>
          </div>
          <div className='flex flex-col p-4 bg-[#DBF7E8] rounded-[8px]'>
            <small>Total Faktur</small>
            <Typography color='primary'>{toCurrency(0)}</Typography>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default SummaryCard
