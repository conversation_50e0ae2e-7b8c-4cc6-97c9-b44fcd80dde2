import NumberField from '@/components/numeric/NumberField'
import { discountTypeOptions } from '@/pages/purchase-order/config/options'
import { toCurrency } from '@/utils/helper'
import { Card, CardContent, FormControl, InputLabel, MenuItem, Select, TextField, Typography } from '@mui/material'

const DiscountCard = () => {
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Diskon Faktur</Typography>
        </div>
        <div className='flex flex-col md:flex-row justify-between gap-4'>
          <FormControl fullWidth className='is-full'>
            <InputLabel id='discount-type'>Tipe Diskon</InputLabel>
            <Select labelId='discount-type' id='discount-type' label='Tipe Diskon' fullWidth>
              {discountTypeOptions.map(disc => (
                <MenuItem key={disc.value} value={disc.value}>
                  {disc.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <TextField fullWidth label='Jumlah Diskon' InputProps={{ inputComponent: NumberField as any }} />
        </div>
        <div className='flex flex-col p-4 bg-[#DBF7E8] rounded-[8px]'>
          <small>Diskon Faktur</small>
          <Typography color='primary'>{toCurrency(0)}</Typography>
        </div>
      </CardContent>
    </Card>
  )
}

export default DiscountCard
