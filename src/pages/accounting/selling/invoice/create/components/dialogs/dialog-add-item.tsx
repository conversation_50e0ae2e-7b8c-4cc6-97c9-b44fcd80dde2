import React from 'react'
import {
  Autocomplete,
  Button,
  CircularProgress,
  debounce,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import CompanyQueryMethods, { ITEM_LIST_QUERY_KEY } from '@/api/services/company/query'
import { ItemType } from '@/types/companyTypes'
import { Controller, useForm } from 'react-hook-form'
import NumberField from '@/components/numeric/NumberField'
import LoadingButton from '@mui/lab/LoadingButton'
import { discountTypeOptions } from '@/pages/purchase-order/config/options'
import CurrencyField from '@/components/numeric/CurrencyField'
import { toCurrency } from '@/utils/helper'

type DialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
}

const AddItemInvoiceDialog = ({ open, setOpen }: DialogProps) => {
  const [itemSearchQuery, setItemSearchQuery] = React.useState<string>('')
  const [selectedItem, setSelectedItem] = React.useState<ItemType | null>(null)
  const handleClose = () => {
    setOpen(false)
  }
  const { control } = useForm()

  const {
    data: itemList,
    isLoading: fetchItemsLoading,
    remove: removeItemList
  } = useQuery({
    enabled: !!itemSearchQuery,
    queryKey: [ITEM_LIST_QUERY_KEY, itemSearchQuery],
    queryFn: async () => {
      const res = await CompanyQueryMethods.getItemList({
        page: 1,
        limit: Number.MAX_SAFE_INTEGER,
        search: itemSearchQuery,
        isInStock: true
      })
      return res.items
    },
    placeholderData: [] as ItemType[]
  })

  return (
    <Dialog fullWidth maxWidth='md' scroll='body' open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-16'>
        Tambah Barang
        <Typography component='span' className='flex flex-col text-center'>
          Masukkan data barang yang akan ditambahkan ke faktur
        </Typography>
      </DialogTitle>
      <form onSubmit={e => e.preventDefault()}>
        <DialogContent className='overflow-visible pbs-0 sm:pbe-6 sm:px-16'>
          <IconButton onClick={handleClose} className='absolute block-start-4 inline-end-4'>
            <i className='ri-close-line text-textSecondary' />
          </IconButton>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <Typography className='font-semibold'>Detil Barang</Typography>
            </Grid>
            <Grid item xs={12}>
              <Autocomplete
                value={selectedItem}
                onInputChange={debounce((e, newValue, reason) => {
                  if (reason === 'input') {
                    setItemSearchQuery(newValue)
                  }
                }, 700)}
                options={itemList || []}
                getOptionLabel={(option: ItemType) => `${option.number} | ${option.name} | ${option.brandName}`}
                freeSolo={!itemSearchQuery}
                noOptionsText='Barang tidak ditemukan'
                onChange={(e, newValue: ItemType) => {
                  // onChange(newValue?.id)
                  setSelectedItem(newValue)
                  // reset({
                  //   ...getValues(),
                  //   parentCode: newValue?.parentCode || '',
                  //   number: newValue?.number || '',
                  //   externalCode: newValue?.vendorNumber || '',
                  //   name: newValue?.name || '',
                  //   category: newValue?.category?.name || '',
                  //   brandName: newValue?.brandName || '',
                  //   largeUnitQuantity: newValue?.largeUnitQuantity || 0,
                  //   ...(getValues('serialNumber') !== '' && { quantityUnit: newValue?.smallUnit })
                  // })
                  removeItemList()
                }}
                renderInput={params => (
                  <TextField
                    {...params}
                    InputProps={{
                      ...params.InputProps,
                      startAdornment: <i className='ri-search-line text-textSecondary size-5 mx-2' />,
                      endAdornment: <>{fetchItemsLoading ? <CircularProgress /> : null}</>,
                      onKeyDown: e => {
                        if (e.key === 'Enter') {
                          e.stopPropagation()
                        }
                      }
                    }}
                    placeholder='Cari Kode barang, kode eksternal, nama barang, atau merk barang'
                    // error={!!error}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Controller
                control={control}
                name='parentCode'
                render={({ field }) => <TextField disabled value={field.value} fullWidth label='Kode Induk' />}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Controller
                control={control}
                name='number'
                render={({ field }) => <TextField disabled value={field.value} fullWidth label='Kode Barang' />}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Controller
                control={control}
                name='externalCode'
                render={({ field }) => <TextField disabled value={field.value} fullWidth label='Kode Eksternal' />}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Controller
                control={control}
                name='name'
                render={({ field }) => <TextField disabled value={field.value} fullWidth label='Nama Item' />}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Controller
                control={control}
                name='brandName'
                render={({ field }) => <TextField disabled value={field.value} fullWidth label='Merk Item' />}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Controller
                control={control}
                name='category'
                render={({ field }) => <TextField disabled value={field.value} fullWidth label='Kategori Item' />}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                control={control}
                name='quantity'
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label='Quantity'
                    // disabled={watchSn?.length > 1}
                    InputProps={{ inputComponent: NumberField as any }}
                    error={!!error}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                control={control}
                name='quantityUnit'
                render={({ field, fieldState: { error } }) => (
                  <FormControl fullWidth>
                    <InputLabel>Satuan</InputLabel>
                    <Select
                      {...field}
                      label='Satuan'
                      // disabled={selectedItem && watchSn?.length > 1}
                      className='bg-white'
                      error={!!error}
                    >
                      {[selectedItem?.largeUnit, selectedItem?.smallUnit].map(role => (
                        <MenuItem key={role} value={role}>
                          {role}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography className='font-semibold'>Diskon Item</Typography>
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl className='flex-1' fullWidth>
                <InputLabel id='taxType'>Jenis Diskon</InputLabel>
                <Select
                  label='Jenis Diskon'
                  // {...field}
                  // value={discountTypeValue}
                  placeholder='Pilih Jenis Diskon'
                  className='bg-white'
                  // error={Boolean(errors.discountType)}
                  onChange={e => {
                    // field.onChange((e.target as HTMLInputElement).value)
                    // resetField('discountValue')
                  }}
                >
                  {discountTypeOptions.map(role => (
                    <MenuItem key={role.value} value={role.value}>
                      {role.label}
                    </MenuItem>
                  ))}
                </Select>
                {/* {errors.discountType && <FormHelperText error>Wajib dipilih.</FormHelperText>} */}
              </FormControl>
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                // {...field}
                label='Jumlah Diskon'
                fullWidth
                InputProps={{
                  className: 'bg-white'
                  // endAdornment: isPercentage ? '%' : '',
                  // inputComponent: (!isPercentage ? CurrencyField : NumberField) as any,
                  // inputProps: {
                  //   isAllowed: ({ floatValue }) =>
                  //     floatValue <= (isPercentage ? 100 : subTotalItems) || floatValue === undefined
                  // }
                }}
                // {...(errors.discountValue && { error: true, helperText: 'Wajib diisi.' })}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel id='isDiscountAfterTax'>Pajak Diskon</InputLabel>
                <Select
                  // {...field}
                  // onChange={e => onChange((e.target as HTMLInputElement).value === '1')}
                  // value={!isNullOrUndefined(value) ? (value ? 1 : 2) : undefined}
                  label='Pajak Diskon'
                  placeholder='Pilih Pajak Diskon'
                  className='bg-white'
                  // error={Boolean(errors.items?.[index]?.isDiscountAfterTax)}
                >
                  <MenuItem key={2} value={2}>
                    Sebelum Pajak
                  </MenuItem>
                  <MenuItem key={1} value={1}>
                    Sesudah Pajak
                  </MenuItem>
                </Select>
                {/* {errors.items?.[index]?.isDiscountAfterTax && <FormHelperText error>Wajib dipilih.</FormHelperText>} */}
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField fullWidth InputProps={{ inputComponent: CurrencyField as any }} label='Sub Total Diskon' />
            </Grid>
            <Grid item xs={12}>
              <div className='p-4 rounded-md bg-[#4BD88B40] flex gap-8 justify-center items-center'>
                <Typography variant='h6'>TOTAL HARGA</Typography>
                <Typography variant='h6' className='font-semibold'>
                  {toCurrency(0)}
                </Typography>
              </div>
            </Grid>
          </Grid>
        </DialogContent>
      </form>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button
          onClick={() => {
            setOpen(false)
          }}
          variant='outlined'
          className='is-full sm:is-auto'
        >
          BATALKAN
        </Button>
        <LoadingButton
          startIcon={<></>}
          // loading={uploadLoading}
          // disabled={segments?.length > 0 && !selectedSegmentId}
          loadingPosition='start'
          variant='contained'
          // onClick={handleSubmit(onSubmitItem, errors => {
          //   Sentry.captureException(errors)
          //   Object.entries(errors).forEach(([field, error]) => {
          //     toast.error(`${field}: ${error?.message}`, {
          //       autoClose: 5000
          //     })
          //   })
          // })}
          className='px-8 is-full !ml-0 sm:is-auto'
        >
          TAMBAHKAN
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default AddItemInvoiceDialog
