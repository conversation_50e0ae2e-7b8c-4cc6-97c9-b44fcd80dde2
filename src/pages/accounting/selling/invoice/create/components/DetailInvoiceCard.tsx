import { Autocomplete, Card, CardContent, debounce, Grid, TextField, Typography } from '@mui/material'
import { useState } from 'react'

const DetailInvoiceCard = () => {
  const [searchQuery, setSearchQuery] = useState('')
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-end'>
          <Typography variant='h5'>Detil Faktur</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <Grid container spacing={4}>
              <Grid item xs={12}>
                <Autocomplete
                  filterOptions={x => x}
                  isOptionEqualToValue={(option, value) => option.id === value.id}
                  onInputChange={debounce((e, newValue, reason) => {
                    if (reason === 'input') {
                      setSearchQuery(newValue)
                    }
                  }, 700)}
                  options={[]}
                  freeSolo
                  onChange={(e, newValue) => {
                    if (newValue) {
                    }
                  }}
                  noOptionsText='Pelanggan tidak ditemukan'
                  // loading={fetchVendorsLoading}
                  renderInput={params => (
                    <TextField
                      {...params}
                      label='Customer/Pelanggan'
                      placeholder='Masukkan Pelanggan'
                      variant='outlined'
                      InputProps={{
                        ...params.InputProps,
                        // endAdornment: <>{fetchVendorsLoading ? <CircularProgress /> : null}</>,
                        onKeyDown: e => {
                          if (e.key === 'Enter') {
                            e.stopPropagation()
                          }
                        }
                      }}
                    />
                  )}
                  getOptionLabel={(option: any) => option?.code}
                  className='flex-1'
                  renderOption={(props, option) => {
                    const { key, ...optionProps } = props
                    return (
                      <li key={key} {...optionProps}>
                        <Typography>
                          {option.code} - {option.name}, {option.address}
                        </Typography>
                      </li>
                    )
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField label='Memo' fullWidth />
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default DetailInvoiceCard
