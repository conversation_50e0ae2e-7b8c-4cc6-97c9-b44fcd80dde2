import { useRouter } from '@/routes/hooks'
import { Breadcrumbs, Button, Grid, Typography } from '@mui/material'
import { Link } from 'react-router-dom'
import DetailInvoiceCard from './components/DetailInvoiceCard'
import DocumentInvoiceCard from './components/DocumentInvoiceCard'
import ItemListCard from './components/ItemListCard'
import AdditionalExpenses from './components/AdditionalExpenseCard'
import DiscountCard from './components/DiscountCard'
import SummaryCard from './components/SummaryCard'
import { useMenu } from '@/components/menu/contexts/menuContext'

const SellingInvoiceCreate = () => {
  const { setConfirmState } = useMenu()

  const handleSubmit = () => {
    setConfirmState({
      open: true,
      title: 'Buat Faktur Pembelian',
      content:
        'Apakah kamu yakin akan membuat faktur pembelian ini? Pastikan semua data sudah benar, action ini tidak bisa diubah',
      confirmText: 'Buat Faktur Pembelian',
      onConfirm: () => {}
    })
  }
  const router = useRouter()
  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs>
          <Link to='#' replace>
            <Typography color='var(--color-text-disabled)'>Penjualan</Typography>
          </Link>
          <Link to='/selling/invoice' replace>
            <Typography color='var(--color-text-disabled)'>Faktur</Typography>
          </Link>
          <Typography>Buat Faktur</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end'>
          <div className='flex flex-col'>
            <Typography variant='h4'>Buat Faktur Penjualan</Typography>
            <Typography>Buat Faktur penjualan Baru</Typography>
          </div>
          <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
            <Button color='secondary' onClick={() => router.back()} variant='outlined'>
              Batalkan
            </Button>
            <Button onClick={() => handleSubmit()} variant='contained'>
              Buat Faktur
            </Button>
          </div>
        </div>
      </Grid>
      <Grid item xs={12} md={6}>
        <DetailInvoiceCard />
      </Grid>
      <Grid item xs={12} md={6}>
        <DocumentInvoiceCard />
      </Grid>
      <Grid item xs={12}>
        <ItemListCard />
      </Grid>
      <Grid item xs={12}>
        <AdditionalExpenses />
      </Grid>
      <Grid item xs={12}>
        <DiscountCard />
      </Grid>
      <Grid item xs={12}>
        <SummaryCard />
      </Grid>
    </Grid>
  )
}

export default SellingInvoiceCreate
