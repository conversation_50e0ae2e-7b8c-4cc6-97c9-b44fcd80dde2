import { GeneralLedgerType } from '@/types/payload'

export const getGeneralLedgerType = (type: string) => {
  switch (type) {
    case GeneralLedgerType.GENERAL:
      return 'Jurnal Umum'
      break
    case GeneralLedgerType.SALES_TRANSACTION:
      return 'Transaksi <PERSON>'
      break
    case GeneralLedgerType.PURCHASE_TRANSACTION:
      return 'Transaksi Pembelian'
      break
    case GeneralLedgerType.CASH_RECEIPT:
      return 'Transaksi Cetak'
      break
    case GeneralLedgerType.CASH_PAYMENT:
      return 'Pembayaran Tunai'
      break
    case GeneralLedgerType.ADJUSTING_ENTRIES:
      return 'Entri Penyesuaian'
      break
    case GeneralLedgerType.OTHER_TRANSACTION:
      return 'Transaksi Lain'
      break
    default:
      return 'Transaksi Lain'
  }
}

export const generalLedgerOptions = [
  {
    value: GeneralLedgerType.GENERAL,
    label: 'Jurnal Umum'
  },
  {
    value: GeneralLedgerType.SALES_TRANSACTION,
    label: 'Transaksi Pen<PERSON>alan'
  },
  {
    value: GeneralLedgerType.PURCHASE_TRANSACTION,
    label: 'Transaksi Pembelian'
  },
  {
    value: GeneralLedgerType.CASH_RECEIPT,
    label: 'Transaksi Cetak'
  },
  {
    value: GeneralLedgerType.CASH_PAYMENT,
    label: 'Transaksi Cetak'
  },
  {
    value: GeneralLedgerType.ADJUSTING_ENTRIES,
    label: 'Entri Penyesuaian'
  },
  {
    value: GeneralLedgerType.OTHER_TRANSACTION,
    label: 'Transaksi Lain'
  }
]
