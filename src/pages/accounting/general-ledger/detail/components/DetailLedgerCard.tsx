import {
  Card,
  CardContent,
  FormControl,
  FormHelperText,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { generalLedgerOptions, getGeneralLedgerType } from '../../config/utils'
import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { Controller, useFormContext } from 'react-hook-form'
import { GeneralLedgerPayload } from '@/types/payload'
import { formatDate, toDate } from 'date-fns'
import { useGeneralLedger } from '../../context/GeneralLedgerContext'
import { id } from 'date-fns/locale'

const DetailLedgerCard = () => {
  const { generalLedgerData } = useGeneralLedger()

  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Pencatatan</Typography>
        </div>
        <div className='grid grid-cols-1 gap-4'>
          <div className='flex flex-col gap-2'>
            <small>Tipe Transaksi</small>
            <Typography>{getGeneralLedgerType(generalLedgerData?.type)}</Typography>
          </div>
          <div className='flex flex-col gap-2'>
            <small>Tanggal Pencatatan</small>
            <Typography>
              {generalLedgerData?.transactionDate
                ? formatDate(generalLedgerData.transactionDate, 'eeee, dd/MM/yyyy', { locale: id })
                : '-'}
            </Typography>
          </div>
          <div className='flex flex-col gap-2'>
            <small>Keterangan</small>
            <Typography>{generalLedgerData?.description ?? '-'}</Typography>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default DetailLedgerCard
