import { PaymentProvider } from '@/pages/cash-bank/payments/context/PaymentContext'
import { ReceiptProvider } from '@/pages/cash-bank/receipt/context/ReceiptContext'
import { retryDynamicImport } from '@/utils/retryDynamicImport'
import { Outlet, RouteObject } from 'react-router-dom'

const CashBankListPage = retryDynamicImport(() => import('@/pages/cash-bank/payments'))
const PaymentCreatePage = retryDynamicImport(() => import('@/pages/cash-bank/payments/create'))
const PaymentDetailPage = retryDynamicImport(() => import('@/pages/cash-bank/payments/detail'))
const PaymentApprovalPage = retryDynamicImport(() => import('@/pages/cash-bank/payments/approval'))
const PaymentApprovalDetailPage = retryDynamicImport(() => import('@/pages/cash-bank/payments/approval-detail'))

const ReceiptListPage = retryDynamicImport(() => import('@/pages/cash-bank/receipt'))
const ReceiptCreatePage = retryDynamicImport(() => import('@/pages/cash-bank/receipt/create'))
const ReceiptDetailPage = retryDynamicImport(() => import('@/pages/cash-bank/receipt/detail'))
const ReceiptApprovalPage = retryDynamicImport(() => import('@/pages/cash-bank/receipt/approval'))
const ReceiptApprovalDetailPage = retryDynamicImport(() => import('@/pages/cash-bank/receipt/approval-detail'))

const ReconsiliationPage = retryDynamicImport(() => import('@/pages/cash-bank/reconsiliation'))

export const CashBankRoutes = [
  {
    path: '/cash-bank',
    children: [
      {
        path: 'receipt',
        element: (
          <ReceiptProvider>
            <Outlet />
          </ReceiptProvider>
        ),
        children: [
          {
            index: true,
            element: <ReceiptListPage />
          },
          {
            path: 'create',
            element: <ReceiptCreatePage />
          },
          {
            path: ':cashReceiptId',
            element: <ReceiptDetailPage />
          }
        ]
      },
      {
        path: 'payments',
        element: (
          <PaymentProvider>
            <Outlet />
          </PaymentProvider>
        ),
        children: [
          {
            index: true,
            element: <CashBankListPage />
          },
          {
            path: 'create',
            element: <PaymentCreatePage />
          },
          {
            path: ':paymentId',
            element: <PaymentDetailPage />
          }
        ]
      },
      {
        path: 'payment-approval',
        element: (
          <PaymentProvider>
            <Outlet />
          </PaymentProvider>
        ),
        children: [
          {
            index: true,
            element: <PaymentApprovalPage />
          },
          {
            path: ':paymentId',
            element: <PaymentApprovalDetailPage />
          }
        ]
      },
      {
        path: 'receipts-approval',
        element: (
          <ReceiptProvider>
            <Outlet />
          </ReceiptProvider>
        ),
        children: [
          {
            index: true,
            element: <ReceiptApprovalPage />
          },
          {
            path: ':cashReceiptId',
            element: <ReceiptApprovalDetailPage />
          }
        ]
      }
    ]
  }
  // {
  //   path: '/reconciliation',
  //   element: <Outlet />,
  //   children: [
  //     {
  //       index: true,
  //       element: <ReconsiliationPage />
  //     }
  //   ]
  // }
] as RouteObject[]
