// MUI Imports

import Dialog, { DialogProps } from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import DialogActions from '@mui/material/DialogActions'
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import { FormControl, FormHelperText, Grid, InputLabel, MenuItem, Select } from '@mui/material'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'

import { coerce, object, string, TypeOf } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import LoadingButton from '@mui/lab/LoadingButton'
import UserQueryMethods, { APPROVER_USER_LIST_QUERY_KEY } from '@/api/services/user/query'
import { useQuery } from '@tanstack/react-query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { ApproverType, UserType } from '@/types/userTypes'
import { PartSwapApproval } from '@/types/partSwapTypes'

type EditApproverDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  isLoading?: boolean
  selectedApproval?: ApproverType
  onSubmit: (value: EditApproverInput) => void
  scope: string
  approvalList: (ApproverType | PartSwapApproval)[]
}

const editApproverSchema = object({
  userId: string({ message: 'Wajib diisi' }),
  note: string({ message: 'Wajib diisi' }).min(1, 'Wajib diisi'),
  approvalId: coerce.number().optional().nullable()
})

export type EditApproverInput = Required<TypeOf<typeof editApproverSchema>>

const EditApproverDialog = ({
  open,
  setOpen,
  isLoading,
  onSubmit,
  scope,
  selectedApproval,
  approvalList
}: EditApproverDialogProps) => {
  const { control, handleSubmit } = useForm<EditApproverInput>({
    resolver: zodResolver(editApproverSchema)
  })

  const {
    data: { items: approverList }
  } = useQuery({
    queryKey: [APPROVER_USER_LIST_QUERY_KEY, scope, 'siteApprover'],
    queryFn: () => {
      return UserQueryMethods.getUserList({
        limit: 1000,
        hasPermission: `${scope}.approve`
      })
    },
    placeholderData: defaultListData as ListResponse<UserType>,
    cacheTime: 0
  })

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  const onSubmitHandler: SubmitHandler<EditApproverInput> = (inputValues: EditApproverInput) => {
    onSubmit(inputValues)
  }

  return (
    <Dialog open={open} onClose={handleClose} fullWidth>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Ganti Penerima Pengajuan
        <Typography component='span' className='flex flex-col text-center'>
          Ganti penerima pengajuan dengan orang lain
        </Typography>
      </DialogTitle>
      <DialogContent className='overflow-visible pbs-0 sm:pbe-16 sm:px-12'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          <Grid item xs={12}>
            <TextField
              value={selectedApproval?.user?.fullName}
              disabled
              fullWidth
              label='Penerima Pengajuan'
              InputLabelProps={{ shrink: true }}
              inputProps={{ disabled: true }}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              name='userId'
              control={control}
              rules={{ required: true }}
              render={({ field: { value, onChange }, formState: { errors } }) => (
                <FormControl fullWidth {...(errors.userId && { error: true })}>
                  <InputLabel>Ganti Dengan</InputLabel>
                  <Select
                    label='Ganti Dengan'
                    value={value}
                    defaultValue=''
                    disabled={isLoading}
                    onChange={e => {
                      onChange(e.target.value)
                    }}
                    className='flex-1 bg-white'
                  >
                    {approverList
                      .filter(item => !approvalList.find(approval => approval.userId === item.id))
                      .map(approver => (
                        <MenuItem key={approver.id} value={approver.id}>
                          {approver.fullName}
                        </MenuItem>
                      ))}
                  </Select>
                  {errors.userId && <FormHelperText error>{errors.userId?.message}</FormHelperText>}
                </FormControl>
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              name='note'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Alasan Diganti'
                  required
                  variant='outlined'
                  placeholder='Masukkkan Alasan Diganti'
                  disabled={isLoading}
                  {...(errors.note && { error: true, helperText: errors.note?.message })}
                />
              )}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className='max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16 flex-col sm:flex-row max-sm:gap-2'>
        <Button onClick={() => setOpen(false)} variant='outlined' disabled={isLoading} className='is-full sm:is-auto'>
          BATAL
        </Button>
        <LoadingButton
          startIcon={<></>}
          loading={isLoading}
          loadingPosition='start'
          variant='contained'
          onClick={handleSubmit(onSubmitHandler)}
          className='px-8 max-sm:!ml-0 is-full sm:is-auto'
        >
          GANTI PENERIMA
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default EditApproverDialog
