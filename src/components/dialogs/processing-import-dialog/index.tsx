import { Button } from '@mui/material'
import MessageDialog from '../message-dialog'
import { Link } from 'react-router-dom'

interface ProcessingImportDialogProps {
  open: boolean
  onClose?: () => void
  onConfirm?: () => void
  contentScope: string
}

const ProcessingImportDialog = ({ open, onClose, onConfirm, contentScope }: ProcessingImportDialogProps) => {
  const handleConfirm = () => {
    onConfirm && onConfirm()
    onClose && onClose()
  }

  return (
    <MessageDialog
      open={open}
      onClose={onClose}
      onConfirm={handleConfirm}
      title='Impor Data'
      content={
        <span>
          Mohon menunggu, sistem sedang memproses impor data <span className='font-bold'>{contentScope}</span>. Cek
          status impor kamu di halaman{' '}
          <Link to='/data-import'>
            <Button variant='text' className='p-0'>
              List Impor Data
            </Button>
          </Link>
          .
        </span>
      }
      confirmText='<PERSON><PERSON>, <PERSON><PERSON><PERSON>'
    />
  )
}

export default ProcessingImportDialog
