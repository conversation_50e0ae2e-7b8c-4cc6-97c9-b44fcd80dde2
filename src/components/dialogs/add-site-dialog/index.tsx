// MUI Imports

import Dialog, { DialogProps } from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import DialogActions from '@mui/material/DialogActions'
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import { FormControl, Grid, InputLabel, MenuItem, Select } from '@mui/material'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'

import { object, string, TypeOf } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from 'react-toastify'
import { useAddSite, useUpdateSite } from '@/api/services/company/mutation'
import LoadingButton from '@mui/lab/LoadingButton'
import { useSite } from '@/pages/company-data/site/context/SiteContext'
import { siteTypeOptions } from './config'

type AddSiteDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
}

const addSiteSchema = object({
  type: string({ message: 'Wajib diisi' }),
  code: string({ message: 'Wajib diisi' }),
  name: string({ message: 'Wajib diisi' }),
  address: string({ message: 'Wajib diisi' }),
  phoneNumber: string().nullable().optional(),
  faxNumber: string().nullable().optional(),
  email: string().email({ message: 'Alamat email tidak valid' }).nullable().optional()
})

type AddSiteInput = Required<TypeOf<typeof addSiteSchema>>

const AddSiteDialog = ({ open, setOpen }: AddSiteDialogProps) => {
  const { siteData, fetchSiteList, fetchSiteData } = useSite()
  const { control, handleSubmit } = useForm<AddSiteInput>({
    resolver: zodResolver(addSiteSchema),
    defaultValues: {
      type: siteData?.type,
      code: siteData?.code,
      name: siteData?.name,
      address: siteData?.address,
      phoneNumber: siteData?.phoneNumber,
      faxNumber: siteData?.faxNumber,
      email: siteData?.email
    }
  })

  const { mutate: addMutate, isLoading: addLoading } = useAddSite()
  const { mutate: updateMutate, isLoading: updateLoading } = useUpdateSite()

  const isLoading = addLoading || updateLoading

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  const onSubmitHandler: SubmitHandler<AddSiteInput> = (inputValues: AddSiteInput) => {
    if (siteData) {
      updateMutate(
        {
          siteId: siteData.id,
          ...inputValues
        },
        {
          onSuccess: () => {
            toast.success('Data site berhasil diubah')
            fetchSiteData()
            fetchSiteList()
            setOpen(false)
          }
        }
      )
    } else {
      addMutate(inputValues, {
        onSuccess: () => {
          toast.success('Data site berhasil ditambahkan')
          fetchSiteList()
          setOpen(false)
        }
      })
    }
  }

  return (
    <Dialog PaperProps={{ className: 'rounded-t-[16px] md:rounded-t-none' }} open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        {siteData ? 'Ubah' : 'Tambah'} Site
        {!siteData && (
          <Typography component='span' className='flex flex-col text-center'>
            Tambahkan site untuk didaftarkan ke list
          </Typography>
        )}
      </DialogTitle>
      <DialogContent className='overflow-visible pbs-0 sm:pbe-16 sm:px-12'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          <Grid item xs={12}>
            <Controller
              name='type'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <FormControl fullWidth>
                  <InputLabel required id='permissionGroupId'>
                    Jenis Site
                  </InputLabel>
                  <Select
                    label='Jenis Site'
                    {...field}
                    placeholder='Pilih Jenis Site'
                    className='bg-white dark:bg-inherit'
                    error={Boolean(errors.type)}
                  >
                    {siteTypeOptions.map(type => (
                      <MenuItem key={type.value} value={type.value}>
                        {type.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.type && (
                    <Typography color='error' variant='caption' marginLeft={4}>
                      {errors.type?.message}
                    </Typography>
                  )}
                </FormControl>
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              name='code'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Kode Site'
                  required
                  variant='outlined'
                  placeholder='Masukkkan Kode Site'
                  disabled={isLoading}
                  {...(errors.code && { error: true, helperText: errors.code?.message })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              name='name'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Nama Site'
                  required
                  variant='outlined'
                  disabled={isLoading}
                  placeholder='Masukkkan Nama Site'
                  {...(errors.name && { error: true, helperText: errors.name?.message })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              name='address'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  multiline
                  rows={4}
                  label='Alamat Site'
                  required
                  variant='outlined'
                  disabled={isLoading}
                  placeholder='Masukkkan Alamat Site'
                  {...(errors.address && { error: true, helperText: errors.address?.message })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              name='phoneNumber'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Nomor Telepon Site'
                  variant='outlined'
                  type='tel'
                  disabled={isLoading}
                  placeholder='Masukkkan Nomor Telepon Site'
                  {...(errors.phoneNumber && { error: true, helperText: errors.phoneNumber?.message })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              name='faxNumber'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Fax Site'
                  variant='outlined'
                  disabled={isLoading}
                  type='number'
                  placeholder='Masukkkan Fax Site'
                  {...(errors.faxNumber && { error: true, helperText: errors.faxNumber?.message })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              name='email'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Email Site'
                  variant='outlined'
                  type='email'
                  disabled={isLoading}
                  placeholder='Masukkkan Email Site'
                  {...(errors.email && { error: true, helperText: errors.email?.message })}
                />
              )}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button onClick={() => setOpen(false)} variant='outlined' disabled={isLoading} className='is-full sm:is-auto'>
          BATAL
        </Button>
        <LoadingButton
          startIcon={<></>}
          loading={isLoading}
          loadingPosition='start'
          variant='contained'
          onClick={handleSubmit(onSubmitHandler)}
          className='px-8 is-full !ml-0 sm:is-auto'
        >
          {siteData ? 'UBAH DATA' : 'TAMBAHKAN'}
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default AddSiteDialog
