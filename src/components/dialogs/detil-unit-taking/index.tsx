import { useUploadImage } from '@/api/services/file/mutation'
import { useUnitRelease } from '@/api/services/pre-release/mutation'
import { DEFAULT_CATEGORY } from '@/data/default/category'
import { UnitType } from '@/types/companyTypes'
import { ReleaseDtoType } from '@/types/preReleaseTypes'
import { zodResolver } from '@hookform/resolvers/zod'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogProps,
  DialogTitle,
  Grid,
  IconButton,
  TextField,
  Typography
} from '@mui/material'
import { useEffect } from 'react'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import { useFilePicker } from 'use-file-picker'
import { takeCoverage } from 'v8'
import { object, string } from 'zod'

type DetailUnitTakingProps = {
  open: boolean
  setOpen: (open: boolean) => void
  onClose?: () => void
  unit: UnitType
  woId: string
  onSuccessReleaseCb?: () => void
}

const DetailUnitTaking = (props: DetailUnitTakingProps) => {
  const { open, setOpen, unit, woId } = props

  const { filesContent, openFilePicker } = useFilePicker({
    accept: 'image/*',
    multiple: false,
    readAs: 'DataURL'
  })

  const { isLoading: isLoadingUploadImage, mutateAsync: uploadImage } = useUploadImage()
  const { isLoading: isLoadingUnitRelease, mutate: unitRelease } = useUnitRelease()

  const isLoading = isLoadingUploadImage || isLoadingUnitRelease

  const { control, handleSubmit, resetField } = useForm<ReleaseDtoType>({
    resolver: zodResolver(
      object({
        takenBy: string(),
        takenImageName: string(),
        takenImageUploadId: string().optional().nullable()
      })
    )
  })

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    props?.onClose?.()
    setOpen(false)
  }

  const onSubmitHandler: SubmitHandler<ReleaseDtoType> = (inputValues: ReleaseDtoType) => {
    uploadImage({
      file: filesContent?.[0]?.content,
      fileName: filesContent?.[0]?.name,
      fieldName: 'takenImageUploadId',
      scope: 'public-image'
    }).then(res => {
      unitRelease(
        {
          takenImageUploadId: res?.data?.id,
          takenBy: inputValues.takenBy,
          workOrderId: woId
        },
        {
          onSuccess: () => {
            setOpen(false)
            props?.onSuccessReleaseCb?.()
          }
        }
      )
    })
  }

  useEffect(() => {
    if (filesContent && filesContent.length > 0) {
      resetField('takenImageName', { defaultValue: filesContent[0].name })
    }
  }, [filesContent])

  return (
    <Dialog maxWidth='lg' open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Proses Pengambilan Unit
        <Typography component='span' className='flex flex-col text-center'>
          Masukkan data pengambilan Unit
        </Typography>
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-8 !py-4'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <div className='flex flex-col gap-4 rounded-[10px] bg-[#4C4E640D] p-4'>
              <Typography variant='h5'>Detil Unit</Typography>
              <div className='grid grid-cols-3 gap-2'>
                <div className='flex flex-col gap-2'>
                  <Typography variant='caption'>Kode Unit</Typography>
                  <Typography>{unit?.number ?? '-'}</Typography>
                </div>
                <div className='flex flex-col gap-2'>
                  <Typography variant='caption'>Kode Activa</Typography>
                  <Typography>{unit?.asset?.code ?? '-'}</Typography>
                </div>
                <div className='flex flex-col gap-2'>
                  <Typography variant='caption'>Kategori Unit</Typography>
                  <Typography>{unit?.category?.name ?? DEFAULT_CATEGORY.name}</Typography>
                </div>
                <div className='flex flex-col gap-2'>
                  <Typography variant='caption'>Jenis Unit</Typography>
                  <Typography>{unit?.subCategory?.name}</Typography>
                </div>
                <div className='flex flex-col gap-2'>
                  <Typography variant='caption'>Type Equipment</Typography>
                  <Typography>{unit?.equipmentType ?? '-'}</Typography>
                </div>
                <div className='flex flex-col gap-2'>
                  <Typography variant='caption'>Merk Unit</Typography>
                  <Typography>{unit?.brandName}</Typography>
                </div>
                <div className='flex flex-col gap-2'>
                  <Typography variant='caption'>Tipe Unit</Typography>
                  <Typography>{unit?.type}</Typography>
                </div>
                <div className='flex flex-col gap-2'>
                  <Typography variant='caption'>Nomor Rangka</Typography>
                  <Typography>{unit?.hullNumber}</Typography>
                </div>
                <div className='flex flex-col gap-2'>
                  <Typography variant='caption'>Nomor Mesin</Typography>
                  <Typography>{unit?.engineNumber ?? '-'}</Typography>
                </div>
                <div className='flex flex-col gap-2'>
                  <Typography variant='caption'>Plat Nomor</Typography>
                  <Typography>{unit?.plateNumber ?? '-'}</Typography>
                </div>
              </div>
              <Typography variant='h5'>Data Unit</Typography>
              <div className='grid grid-cols-3 gap-2'>
                <div className='flex flex-col'>
                  <Typography variant='caption'>KM</Typography>
                  <Typography>{unit?.km ?? '-'} KM</Typography>
                </div>
                <div className='flex flex-col'>
                  <Typography variant='caption'>HM</Typography>
                  <Typography>{unit?.hm ?? '-'} Jam</Typography>
                </div>
              </div>
            </div>
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='takenBy'
              render={({ field, fieldState: { error } }) => (
                <TextField {...field} error={!!error} fullWidth label='Diambil oleh' />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col'>
              <Typography variant='subtitle2' marginBottom={2}>
                Unggah Foto Bukti Pengambilan
              </Typography>
              <div className='flex items-center gap-4'>
                <Controller
                  control={control}
                  name='takenImageName'
                  render={({ fieldState }) => (
                    <TextField
                      size='small'
                      value={filesContent?.[0]?.name}
                      fullWidth
                      placeholder='Tidak ada file dipilih'
                      aria-readonly
                      className='flex-1'
                      error={!!fieldState.error}
                    />
                  )}
                />
                <Button variant='contained' onClick={openFilePicker}>
                  Unggah
                </Button>
              </div>
            </div>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button
          variant='outlined'
          disabled={isLoading}
          color='secondary'
          onClick={() => setOpen(false)}
          className='is-full sm:is-auto'
        >
          BATALKAN
        </Button>
        <LoadingButton
          loading={isLoading}
          startIcon={<></>}
          variant='contained'
          onClick={handleSubmit(onSubmitHandler)}
          className='px-8 is-full !ml-0 sm:is-auto'
        >
          PROSES PENGAMBILAN
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default DetailUnitTaking
