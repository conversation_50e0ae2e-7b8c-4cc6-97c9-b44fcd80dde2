import CompanyQueryMethods, { ITEM_QUERY_KEY, SERIAL_NUMBER_LIST_QUERY_KEY } from '@/api/services/company/query'
import { ItemType } from '@/types/companyTypes'
import { SerialNumberType } from '@/types/serialNumber'
import {
  Dialog,
  DialogProps,
  DialogTitle,
  Typography,
  IconButton,
  DialogContent,
  Grid,
  InputLabel,
  Autocomplete,
  FormControl,
  TextField,
  CircularProgress,
  debounce
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useEffect, useState } from 'react'

export type ItemTypeWithSn = {
  serialNumber?: string
} & ItemType

type SearchDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  onClose?: () => void
  onAddItemClick: (item?: ItemTypeWithSn) => void
}

const SearchSnDialog = (props: SearchDialogProps) => {
  const { setOpen, open, onAddItemClick } = props

  const [serialNumberSearchQuery, setSerialNumberSearchQuery] = useState('')
  const [selectedItemId, setSelectedItemId] = useState<string>()
  const [selectedSerialNumber, setSelectedSerialNumber] = useState<string>()
  const [isSearching, setIsSearching] = useState<boolean>(false)
  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    props?.onClose?.()
    setOpen(false)
  }

  const { data: snList, isLoading: fetchItemsLoading } = useQuery({
    enabled: !!serialNumberSearchQuery,
    queryKey: [SERIAL_NUMBER_LIST_QUERY_KEY, serialNumberSearchQuery],
    queryFn: async () => {
      const res = await CompanyQueryMethods.getSerialNumberList({
        page: 1,
        limit: Number.MAX_SAFE_INTEGER,
        search: serialNumberSearchQuery
      })
      return res.items ?? []
    },
    placeholderData: [] as SerialNumberType[]
  })

  const { data: itemDetail } = useQuery({
    enabled: !!selectedItemId,
    queryKey: [ITEM_QUERY_KEY, selectedItemId],
    queryFn: () => CompanyQueryMethods.getItem(selectedItemId)
  })

  useEffect(() => {
    if (itemDetail) {
      onAddItemClick({ ...itemDetail, serialNumber: selectedSerialNumber })
    }
  }, [itemDetail])

  useEffect(() => {
    if (serialNumberSearchQuery === '') {
      setIsSearching(false)
    }
  }, [serialNumberSearchQuery])

  return (
    <Dialog maxWidth='lg' open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Tambah Part
        <Typography component='span' className='flex flex-col text-center'>
          Masukkan data barang yang akan ditambahkan ke Segment
        </Typography>
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        <IconButton
          onClick={() => {
            setOpen(false)
            props?.onClose?.()
          }}
          className='absolute block-start-4 inline-end-4'
        >
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={4}>
          <Grid item xs={12} className='space-y-2'>
            <InputLabel id='serial-number-input'>Nomor Serial</InputLabel>
            <FormControl fullWidth>
              <Autocomplete
                freeSolo={!serialNumberSearchQuery}
                onInputChange={debounce((e, newValue, reason) => {
                  if (reason === 'input') {
                    setIsSearching(true)
                    setSerialNumberSearchQuery(newValue)
                  }
                }, 700)}
                onChange={(e, newValue: SerialNumberType) => {
                  setSelectedItemId(newValue?.itemId)
                  setSelectedSerialNumber(newValue?.number)
                }}
                key='serial-number-input'
                id='serial-number-input'
                options={snList ?? []}
                noOptionsText='No Serial tidak ditemukan'
                getOptionLabel={(option: SerialNumberType) =>
                  `${option.number} | ${option.item?.name} | ${option.item?.brandName} | ${option.item?.number}`
                }
                renderInput={params => (
                  <TextField
                    {...params}
                    InputProps={{
                      ...params.InputProps,
                      startAdornment: <i className='ri-search-line text-textSecondary size-5 mx-2' />,
                      endAdornment: <>{fetchItemsLoading ? <CircularProgress /> : null}</>,
                      onKeyDown: e => {
                        if (e.key === 'Enter') {
                          e.stopPropagation()
                        }
                      }
                    }}
                    placeholder={serialNumberSearchQuery === '' ? 'Cari No Serial' : serialNumberSearchQuery}
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            {isSearching && snList?.length === 0 ? (
              <div className='flex flex-col gap-4 min-h-[150px] items-center justify-center'>
                <i className='searching-icon-state size-24 text-textSecondary' />
                <div className='flex flex-col items-center justify-center'>
                  <Typography>Nomor Serial tidak ditemukan</Typography>
                  <Typography
                    role='button'
                    sx={{ cursor: 'pointer' }}
                    onClick={() => onAddItemClick({ serialNumber: serialNumberSearchQuery } as ItemTypeWithSn)}
                    color='primary'
                  >
                    Tambahkan Nomor Serial
                  </Typography>
                </div>
              </div>
            ) : (
              <Typography align='center'>
                Serial Number tidak ditemukan?{' '}
                <Typography
                  sx={{ cursor: 'pointer' }}
                  component='span'
                  role='button'
                  color='primary'
                  onClick={() => onAddItemClick()}
                >
                  Cari Barang
                </Typography>
              </Typography>
            )}
          </Grid>
        </Grid>
      </DialogContent>
    </Dialog>
  )
}

export default SearchSnDialog
