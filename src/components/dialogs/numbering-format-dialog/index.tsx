import { useCallback, useEffect } from 'react'
import {
  FormControl,
  FormControlLabel,
  Checkbox,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography,
  IconButton,
  TextField,
  FormHelperText
} from '@mui/material'
import { format } from 'date-fns'
import { Controller, useForm, useWatch } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import LoadingButton from '@mui/lab/LoadingButton'
import { numberingDateFormat, numberingDivider, numberingTypeList } from '@/pages/setting/numbering/utils'
import { NumberingList, NumberingType, UpsertNumberingFormatDto } from '@/pages/setting/numbering/enum'
import { numberingScheme } from '@/pages/setting/numbering/utils'
import { useQuery } from '@tanstack/react-query'
import NumberFormatQueryMethods, { NumberFormat_QUERY_KEY } from '@/api/services/setting/numbering/query'
import { useUpsertNumberFormat } from '@/api/services/setting/numbering/mutation'
import { toast } from 'react-toastify'
import NumberField from '@/components/numeric/NumberField'

interface NumberingDialogForm {
  open: boolean
  handleClose: () => void
  title: string
  alias: string
  selected?: NumberingList
}

const NumberingFormatFormDialog = ({ open, handleClose, title, alias, selected }: NumberingDialogForm) => {
  const { control, handleSubmit, reset, getValues, setValue } = useForm<UpsertNumberingFormatDto>({
    resolver: zodResolver(numberingScheme)
  })
  const sampleNumber = useWatch({
    control,
    name: 'sampleFormattedNumber',
    defaultValue: ''
  })

  const {
    data,
    isLoading: loadingGetDetail,
    refetch
  } = useQuery({
    queryKey: [NumberFormat_QUERY_KEY, selected.scope],
    queryFn: () => NumberFormatQueryMethods.getNumberFormat(selected.scope),
    enabled: open,
    refetchOnWindowFocus: false
  })

  const { mutate: upsertNumberFormat, isLoading: isLoadingUpsert } = useUpsertNumberFormat()

  const isLoading = loadingGetDetail || isLoadingUpsert

  const onSubmitHandler = (data: UpsertNumberingFormatDto) => {
    upsertNumberFormat(
      { scope: selected.scope, payload: data },
      {
        onSuccess: () => {
          toast.success('Penomoran berhasil disimpan')
          handleClose()
        }
      }
    )
  }

  const generateNumberBasedLength = useCallback((length: number): string => {
    const result = '0'.repeat(length - 1)

    const randomLastDigit = Math.floor(Math.random() * 10)

    return result + randomLastDigit
  }, [])

  const onChangeForm = (e: any, onChange: (_e: any) => void, type?: string) => {
    if (type === 'number') {
      onChange(+(e.target as HTMLInputElement).value)
    } else {
      onChange(e)
    }
    const { prefix, separator, isUseDepartmentCode, isUseSiteCode, timeFormat, counterPadStartLength } = getValues()
    setValue(
      'sampleFormattedNumber',
      `${prefix}${separator}${isUseDepartmentCode ? `DEP${separator}` : ''}${isUseSiteCode ? `SIT${separator}` : ''}${timeFormat
        ?.replace('YYYY', format(new Date(), 'yyyy'))
        ?.replace('MM', format(new Date(), 'MM'))
        ?.replace('DD', format(new Date(), 'dd'))
        ?.replace(
          'YY',
          format(new Date(), 'yy')
        )}${separator}${counterPadStartLength ? generateNumberBasedLength(counterPadStartLength as number) : ''}`
    )
  }

  useEffect(() => {
    if (data) reset(data)
  }, [data])

  if (data) {
    return (
      <Dialog PaperProps={{ className: 'rounded-t-[16px] md:rounded-t-none' }} open={open} onClose={handleClose}>
        <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-14'>
          Penomoran {title}
          <Typography component='span' className='flex flex-col text-center'>
            Atur format penomoran dokumen {alias}
          </Typography>
        </DialogTitle>
        <DialogContent className='md:min-w-[560px] pbs-0 sm:pbe-16 sm:px-12 !py-4'>
          <IconButton onClick={handleClose} className='absolute block-start-4 inline-end-4'>
            <i className='ri-close-line text-textSecondary' />
          </IconButton>
          <Grid component='form' container spacing={5} onSubmit={e => e.preventDefault()}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel required id='country'>
                  Tipe Penomoran
                </InputLabel>
                <Controller
                  control={control}
                  name='type'
                  render={({ field: { value, onChange }, fieldState: { error } }) => (
                    <Select
                      label='Tipe Penomoran'
                      placeholder='Pilih Penomoran'
                      inputProps={{
                        className: 'bg-white dark:bg-inherit'
                      }}
                      value={value}
                      onChange={e => {
                        onChangeForm(e, onChange)
                        if (['RESET_EVERY_DAY', 'RESET_EVERY_MONTH'].includes((e.target as HTMLInputElement).value)) {
                          setValue('timeFormat', null)
                        }
                      }}
                    >
                      {numberingTypeList.map((item, idx) => (
                        <MenuItem key={`${idx}${item.value}`} value={item.value}>
                          {item.label}
                        </MenuItem>
                      ))}
                    </Select>
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel required id='numbering-divider-label'>
                  Format Pemisah
                </InputLabel>
                <Controller
                  control={control}
                  name='separator'
                  render={({ field: { value, onChange }, fieldState: { error } }) => (
                    <Select
                      id='numbering-divider'
                      value={value}
                      onChange={e => onChangeForm(e, onChange)}
                      placeholder='Pilih Pemisah'
                      label='Format Pemisah'
                      inputProps={{
                        className: 'bg-white dark:bg-inherit'
                      }}
                    >
                      {numberingDivider.map((item, idx) => (
                        <MenuItem key={`${idx}${item}`} value={item}>
                          {item}
                        </MenuItem>
                      ))}
                    </Select>
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <Controller
                  control={control}
                  name='prefix'
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      onChange={e => onChangeForm(e, field.onChange)}
                      error={Boolean(error)}
                      helperText={error?.message}
                      required
                      label='Prefiks Teks'
                      id='numbering-prefix'
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid className='flex flex-col' item xs={12}>
              <Controller
                control={control}
                name='isUseSiteCode'
                render={({ field: { onChange, value } }) => (
                  <FormControlLabel
                    control={<Checkbox onChange={e => onChangeForm(e, onChange)} checked={value} />}
                    label='Gunakan Code Site'
                  />
                )}
              />
              <Controller
                control={control}
                name='isUseDepartmentCode'
                render={({ field: { onChange, value } }) => (
                  <FormControlLabel
                    control={<Checkbox onChange={e => onChangeForm(e, onChange)} checked={value} />}
                    label='Gunakan Code Departemen'
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel required id='numbering-date-format-label'>
                  Format Bulan / Tahun
                </InputLabel>
                <Controller
                  control={control}
                  name='timeFormat'
                  render={({ field: { value, onChange }, fieldState: { error } }) => (
                    <>
                      <Select
                        required
                        placeholder='Pilih Format Bulan / Tahun'
                        inputProps={{
                          className: 'bg-white dark:bg-inherit'
                        }}
                        label='Format Bulan / Tahun'
                        value={value}
                        onChange={e => onChangeForm(e, onChange)}
                        error={!!error}
                      >
                        {numberingDateFormat
                          .filter(x =>
                            getValues().type === NumberingType.RESET_EVERY_DAY
                              ? x.includes('DD')
                              : getValues().type === NumberingType.RESET_EVERY_MONTH
                                ? x.includes('MM')
                                : true
                          )
                          .map(format => (
                            <MenuItem key={format} value={format}>
                              {format}
                            </MenuItem>
                          ))}
                      </Select>
                      {error && <FormHelperText error>{error?.message}</FormHelperText>}
                    </>
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <Controller
                  control={control}
                  name='counterPadStartLength'
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      onChange={e => onChangeForm(e, field.onChange, 'number')}
                      type='number'
                      required
                      label='Jumlah Digit Counter'
                      id='numbering-prefix'
                      error={!!error}
                      helperText={error?.message}
                      InputProps={{
                        inputComponent: NumberField as any,
                        inputProps: {
                          isAllowed: ({ floatValue }) => floatValue <= 7 || floatValue === undefined
                        }
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <Box className='flex flex-col bg-[#4C4E640D]/5 dark:bg-inherit p-[20px_8px_20px_20px]'>
                <Typography variant='body1'>Hasil Format Penomoran</Typography>
                <Typography variant='h4' className='text-[#4C4E64DE] dark:text-inherit'>
                  {sampleNumber}
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
          <Button onClick={handleClose} variant='outlined' disabled={isLoading} className='is-full sm:is-auto'>
            BATALKAN
          </Button>
          <LoadingButton
            startIcon={<></>}
            loading={isLoading}
            loadingPosition='start'
            variant='contained'
            onClick={handleSubmit(onSubmitHandler)}
            className='px-8 is-full !ml-0 sm:is-auto'
          >
            SIMPAN
          </LoadingButton>
        </DialogActions>
      </Dialog>
    )
  }
  return null
}

export default NumberingFormatFormDialog
