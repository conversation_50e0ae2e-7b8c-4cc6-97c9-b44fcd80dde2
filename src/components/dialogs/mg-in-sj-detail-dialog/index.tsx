import React from 'react'
import { Box, Button, Dialog, DialogContent, DialogTitle, Divider, IconButton, Typography } from '@mui/material'
import { ImType } from '@/types/mgTypes'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { getCoreRowModel, useReactTable } from '@tanstack/react-table'
import { tableItemsColumns } from '@/pages/material-goods/list-in/detail/components/SjListCard/table'
import Table from '@/components/table'

interface SjDetailDialogProps {
  open: boolean
  setOpen: React.Dispatch<React.SetStateAction<ImType>>
  handleClose: () => void
  imData: ImType
}

const SjDetailDialog = (props: SjDetailDialogProps) => {
  const { open, handleClose, setOpen, imData } = props

  const table = useReactTable({
    data: imData.items ?? [],
    columns: tableItemsColumns(),
    getCoreRowModel: getCoreRowModel()
  })

  return (
    <Dialog open={open} onClose={handleClose} fullWidth maxWidth='md' scroll='body'>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Detil Surat Jalan
      </DialogTitle>
      <DialogContent className='overflow-visible pbs-0 p-8'>
        <IconButton onClick={() => setOpen(null)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <div className='space-y-4'>
          <div className='rounded-[8px] flex flex-col bg-[#4C4E640D] p-4 gap-4'>
            <Box className='flex justify-between items-center'>
              <Box className='flex gap-2 flex-col'>
                <Typography sx={{ fontSize: '16px', lineHeight: '20px', fontWeight: 500 }}>
                  Nomor SJ: {imData.deliveryNoteNumber}
                </Typography>
                <Typography sx={{ fontWeight: 400 }}>
                  {formatDate(imData?.updatedAt, 'eeee, dd/MM/yyyy', { locale: id })}
                </Typography>
              </Box>
              {!!imData?.deliveryNoteUrl && (
                <Button variant='outlined' target='_blank' href={imData?.deliveryNoteUrl} download>
                  Lihat File
                </Button>
              )}
            </Box>
            <Divider />
            <Box>
              <Typography sx={{ fontWeight: 500 }}>Diterima di {imData?.site?.name}</Typography>
              <Typography color='primary'>Diterima oleh {imData?.createdByUser?.fullName}</Typography>
              <Typography sx={{ fontWeight: 400 }}>Catatan: {imData?.note ?? '-'}</Typography>
            </Box>
          </div>
          <div className='shadow-sm rounded-lg select-none'>
            <Table
              table={table}
              emptyLabel={
                <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                  <Typography variant='subtitle1'>Tidak ada Barang</Typography>
                </td>
              }
              disablePagination
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default SjDetailDialog
