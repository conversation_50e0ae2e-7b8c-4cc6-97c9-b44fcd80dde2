// MUI Imports

import Dialog from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import Card from '@mui/material/Card'
import Box from '@mui/material/Box'
import { getCoreRowModel, useReactTable } from '@tanstack/react-table'
import Table from '@/components/table'
import { createdDocumentTableColumns, itemReceiptTableColumns } from '@/pages/material-request/mr-in/detail/table'
import { useRouter } from '@/routes/hooks'
import { WarehouseDataType, WarehouseItemType } from '@/types/appTypes'
import { useMr } from '@/pages/material-request/context/MrContext'
import { useEffect, useState } from 'react'
import PoQueryMethods, { PO_LIST_QUERY_KEY } from '@/api/services/po/query'
import { useQuery } from '@tanstack/react-query'
import PrQueryMethods, { PR_LIST_QUERY_KEY } from '@/api/services/pr/query'
import { PurchaseRequisitionPurpose } from '@/pages/purchase-requisition/config/enum'
import { MaterialTransferType } from '@/pages/material-transfer/config/enum'
import MtQueryMethods, { MT_LIST_QUERY_KEY } from '@/api/services/mt/query'
import { CardHeader } from '@mui/material'

export type ItemCreatedDocumentDialogProps = {
  open: boolean
  selectedItem?: WarehouseItemType
}

const ItemCreatedDocumentDialog = () => {
  const router = useRouter()

  const {
    mrData,
    itemCreatedDocumentState: { open, selectedItem },
    setItemCreatedDocumentModalState
  } = useMr()

  const [docList, setDocList] = useState([])

  const handleClose = () => {
    setItemCreatedDocumentModalState({ open: false, selectedItem: undefined })
  }

  const { data: prList } = useQuery({
    enabled: !!selectedItem?.id,
    queryKey: [PR_LIST_QUERY_KEY, selectedItem?.id],
    queryFn: () => {
      return PrQueryMethods.getPrList({
        materialRequestItemId: selectedItem?.id,
        limit: Number.MAX_SAFE_INTEGER,
        purpose: PurchaseRequisitionPurpose.USED
      }).then(res =>
        res?.items?.map(item => ({
          ...item,
          docType: 'PR'
        }))
      ) as Promise<WarehouseDataType[]>
    },
    placeholderData: [],
    cacheTime: 0
  })

  const { data: mtList } = useQuery({
    enabled: !!selectedItem?.id,
    queryKey: [MT_LIST_QUERY_KEY, selectedItem?.id],
    queryFn: () => {
      return MtQueryMethods.getMtList({
        materialRequestItemId: selectedItem?.id,
        limit: Number.MAX_SAFE_INTEGER
      }).then(res =>
        res?.items?.map(item => ({
          ...item,
          docType: item.type === MaterialTransferType.S2S_BORROW ? 'MB' : 'MT'
        }))
      ) as Promise<WarehouseDataType[]>
    },
    placeholderData: [],
    cacheTime: 0
  })

  const { data: poList } = useQuery({
    enabled: !!selectedItem?.id,
    queryKey: [PO_LIST_QUERY_KEY, selectedItem?.id],
    queryFn: () => {
      return PoQueryMethods.getPoList({
        materialRequestItemId: selectedItem?.id,
        limit: Number.MAX_SAFE_INTEGER
      }).then(res =>
        res?.items?.map(item => ({
          ...item,
          docType: 'PO'
        }))
      ) as Promise<WarehouseDataType[]>
    },
    placeholderData: [],
    cacheTime: 0
  })

  const createdDocumentTable = useReactTable({
    data: docList,
    columns: createdDocumentTableColumns({
      showDetail: docData => {
        if (docData.docType === 'PR') {
          router.push(`/pr/list/${docData?.id}`)
        }
        if (docData.docType === 'MT') {
          router.push(`/mt/created/${docData?.id}`)
        }
        if (docData.docType === 'MB') {
          router.push(`/mb/created/${docData?.id}`)
        }
        if (docData.docType === 'PO') {
          router.push(`/po/list/${docData?.id}`)
        }
      }
    }),
    initialState: {
      pagination: {
        pageSize: 5
      }
    },
    getCoreRowModel: getCoreRowModel()
  })

  const itemReceiptsTable = useReactTable({
    data: mrData.items?.find(item => item.id === selectedItem?.id)?.receipts,
    columns: itemReceiptTableColumns(),
    initialState: {
      pagination: {
        pageSize: 5
      }
    },
    getCoreRowModel: getCoreRowModel()
  })

  useEffect(() => {
    if (prList.length > 0 || mtList.length > 0 || poList.length > 0) {
      setDocList([...prList, ...mtList, ...poList])
    }
  }, [prList, mtList, poList])

  return (
    <>
      <Dialog fullWidth maxWidth='md' open={open} onClose={handleClose}>
        <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-4 sm:px-12'>
          Lacak Barang
          <Typography component='span' className='flex flex-col text-center'>
            Lacak dokumen terbuat dan barang masuk untuk barang ini
          </Typography>
        </DialogTitle>
        <IconButton onClick={handleClose} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <DialogContent>
          <Box className='space-y-4'>
            <Box className='p-4 w-full min-h-[100px] rounded-[8px] bg-[#4C4E640D]/5'>
              <Typography component='span'>
                {selectedItem?.item?.parentCode ? `Kode Induk ${selectedItem.item.parentCode} | ` : ''}Kode Barang{' '}
                {selectedItem?.item?.number || '-'}
              </Typography>
              <Typography variant='h6'>
                {selectedItem?.item?.name} - {selectedItem?.item?.brandName}
              </Typography>
              <Typography variant='h6' color='primary'>
                {selectedItem?.quantity} {selectedItem?.quantityUnit}
              </Typography>
            </Box>
            <Box className='flex flex-col gap-2'>
              <Typography variant='button'>Dokumen Terbuat</Typography>
              <Card>
                <Table
                  table={createdDocumentTable}
                  emptyLabel={
                    <td colSpan={createdDocumentTable.getVisibleFlatColumns().length} className='text-center h-60'>
                      <Typography>Belum ada Dokumen</Typography>
                      <Typography className='text-sm text-gray-400'>
                        Semua dokumen yang telah dibuat untuk barang ini akan ditampilkan di sini
                      </Typography>
                    </td>
                  }
                />
              </Card>
            </Box>
            <Box className='flex flex-col gap-2'>
              <Typography variant='button'>Barang Masuk</Typography>
              <Card>
                <Table
                  table={itemReceiptsTable}
                  emptyLabel={
                    <td colSpan={itemReceiptsTable.getVisibleFlatColumns().length} className='text-center h-60'>
                      <Typography>Belum ada Barang Masuk</Typography>
                      <Typography className='text-sm text-gray-400'>
                        Semua barang masuk untuk barang ini akan ditampilkan di sini
                      </Typography>
                    </td>
                  }
                />
              </Card>
            </Box>
          </Box>
        </DialogContent>
      </Dialog>
    </>
  )
}

export default ItemCreatedDocumentDialog
