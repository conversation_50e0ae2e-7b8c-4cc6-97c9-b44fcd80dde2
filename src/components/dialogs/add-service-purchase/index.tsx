// React Imports

// MUI Imports
import Button from '@mui/material/Button'
import Dialog, { DialogProps } from '@mui/material/Dialog'
import DialogActions from '@mui/material/DialogActions'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import IconButton from '@mui/material/IconButton'
import Typography from '@mui/material/Typography'

import { FormProvider, useForm } from 'react-hook-form'

import ItemDetails from './ItemDetails'
import PaymentDetails from './PaymentDetails'
import TotalSection from './TotalSection'
import Separator from '@/components/Separator'
import DiscountDetails from './DiscountDetails'
import { ServiceOrderPayload } from '@/types/serviceOrderTypes'
import { WoSegmentType } from '@/types/woTypes'
import SegmentDetail from './SegmentDetail'

type AddServicePurchaseDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  onSubmit: (itemData?: ServiceOrderPayload) => void
  purchaseData?: ServiceOrderPayload
  woSegment?: WoSegmentType
}

const AddServicePurchaseDialog = ({
  open,
  setOpen,
  purchaseData,
  onSubmit,
  woSegment
}: AddServicePurchaseDialogProps) => {
  const methods = useForm<ServiceOrderPayload>({
    defaultValues: {
      shippingCost: 0,
      discountAmount: 0,
      discountValue: 0,
      grandTotal: 0,
      totalPrice: 0,
      totalTax: 0,
      totalDiscount: 0,
      ...purchaseData,
      items: purchaseData.items?.map(purchaseItem => ({
        discountType: purchaseItem.item?.discountType,
        taxType: purchaseItem.item?.taxType,
        taxPercentage: purchaseItem.item?.taxPercentage,
        pricePerUnit: purchaseItem.item?.pricePerUnit,
        discountValue: purchaseItem.item?.discountValue,
        isDiscountAfterTax: purchaseItem.item?.isDiscountAfterTax,
        ...purchaseItem
      }))
    }
  })

  const { handleSubmit } = methods
  const { paymentMethod } = purchaseData

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  return (
    <FormProvider {...methods}>
      <Dialog fullWidth open={open} onClose={handleClose} maxWidth='md'>
        <DialogTitle variant='h4' className='flex gap-2 flex-col items-center sm:pbs-16 sm:pbe-6 sm:px-16'>
          <div className='max-sm:is-[80%] max-sm:text-center'>
            {paymentMethod ? 'Ubah Data Procurement' : 'Masukkan Data Procurement'}
          </div>
          <Typography component='span' className='flex flex-col text-center'>
            Masukkan dan lengkapi data pendukung service order
          </Typography>
        </DialogTitle>
        <DialogContent className='overflow-auto pbs-0 sm:pbe-10 sm:px-16'>
          <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
            <i className='ri-close-line text-textSecondary' />
          </IconButton>
          <SegmentDetail segment={woSegment} />
          <ItemDetails />
          <Separator />
          <DiscountDetails />
          <TotalSection />
          <PaymentDetails />
        </DialogContent>
        <DialogActions className='justify-center pbs-0 sm:pbe-10 sm:px-16 flex-col sm:flex-row max-sm:gap-2'>
          <Button
            variant='outlined'
            color='secondary'
            type='reset'
            onClick={() => setOpen(false)}
            className='is-full sm:is-auto'
          >
            BATAL
          </Button>
          <Button
            variant='contained'
            onClick={handleSubmit(onSubmit)}
            type='submit'
            className='is-full sm:is-auto max-sm:!ml-0'
          >
            {paymentMethod ? 'UBAH DATA' : 'MASUKKAN DATA'}
          </Button>
        </DialogActions>
      </Dialog>
    </FormProvider>
  )
}

export default AddServicePurchaseDialog
