import React, { useEffect, useState } from 'react'

import { Autocomplete, Button, CircularProgress, debounce, Grid, TextField, Typography } from '@mui/material'
import { Controller, useFormContext, useWatch } from 'react-hook-form'

import type { Vend<PERSON> } from '@/types/apps/vendorTypes'
import { VendorType } from '@/types/companyTypes'
import CompanyQueryMethods, { VENDOR_LIST_QUERY_KEY } from '@/api/services/company/query'
import { useQuery } from '@tanstack/react-query'
import { ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import Separator from '@/components/Separator'
import { PurchasePayload } from '@/pages/purchase-order/config/types'
import { useFilePicker } from 'use-file-picker'
import { useUpdateEffect } from 'react-use'

const VendorInfo: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('')
  const { control, getValues, setValue } = useFormContext<PurchasePayload>()

  const itemsWatch = useWatch({
    control,
    name: `items`,
    defaultValue: []
  })

  const {
    data: { items: vendorList },
    isLoading: fetchVendorsLoading
  } = useQuery({
    enabled: !!searchQuery,
    queryKey: [VENDOR_LIST_QUERY_KEY, searchQuery],
    queryFn: () => {
      return CompanyQueryMethods.getVendorList({
        ...(searchQuery && { search: searchQuery }),
        limit: 100000
      })
    },
    placeholderData: defaultListData as ListResponse<VendorType>
  })

  const { openFilePicker, filesContent, clear } = useFilePicker({
    multiple: false,
    accept: ['.pdf', '.docx', '.doc', 'xls', 'xlsx', 'image/*'],
    readAs: 'DataURL'
  })

  useUpdateEffect(() => {
    if ((filesContent?.length ?? 0) > 0) {
      setValue('tenderDocumentContent', filesContent[0]?.content)
      setValue('tenderDocumentName', filesContent[0]?.name)
    }
  }, [filesContent])

  return (
    <div className='flex flex-col mt-8 w-full max-w-[780px] max-md:max-w-full'>
      <h2 className='self-start text-sm tracking-normal text-center text-gray-600 text-opacity-60'>
        <Typography color='GrayText' className='font-bold'>
          Informasi Vendor
        </Typography>
      </h2>
      <Grid container spacing={3} className='mt-2'>
        <Grid item xs={12} md={6}>
          <Controller
            control={control}
            name='vendorId'
            rules={{ required: true }}
            render={({ field: { onChange, value }, formState, fieldState }) => (
              <Autocomplete
                filterOptions={x => x}
                isOptionEqualToValue={(option, value) => option.id === value.id}
                onInputChange={debounce((e, newValue, reason) => {
                  if (reason === 'input') {
                    setSearchQuery(newValue)
                  }
                }, 700)}
                options={vendorList || []}
                freeSolo
                onChange={(e, newValue: VendorType) => {
                  if (newValue) {
                    setValue('vendor', newValue)
                    setValue('vendorId', newValue?.id)
                  }
                }}
                value={getValues('vendor')}
                noOptionsText='Vendor tidak ditemukan'
                loading={fetchVendorsLoading}
                disabled={itemsWatch.some(prItem => !!prItem.item?.vendor)}
                renderInput={params => (
                  <TextField
                    {...params}
                    label='Kode Vendor'
                    placeholder='Masukkan kode vendor'
                    variant='outlined'
                    InputProps={{
                      ...params.InputProps,
                      endAdornment: <>{fetchVendorsLoading ? <CircularProgress /> : null}</>,
                      onKeyDown: e => {
                        if (e.key === 'Enter') {
                          e.stopPropagation()
                        }
                      }
                    }}
                  />
                )}
                getOptionLabel={(option: VendorType) => option?.code}
                className='flex-1'
                renderOption={(props, option) => {
                  const { key, ...optionProps } = props
                  return (
                    <li key={key} {...optionProps}>
                      <Typography>
                        {option.code} - {option.name}, {option.address}
                      </Typography>
                    </li>
                  )
                }}
              />
            )}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <Controller
            name='vendor'
            control={control}
            render={({ field, formState: { errors } }) => (
              <TextField
                {...field}
                value={field.value?.name}
                fullWidth
                label='Nama Vendor'
                placeholder='Masukkan nama vendor'
                className='flex-1'
                disabled
                InputLabelProps={{ shrink: !!field.value }}
              />
            )}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <Controller
            name='vendorPicName'
            control={control}
            rules={{ required: true }}
            render={({ field, formState: { errors } }) => (
              <TextField
                {...field}
                fullWidth
                label='PIC Vendor'
                placeholder='Masukkan PIC vendor'
                InputLabelProps={{ shrink: !!field.value }}
                {...(errors.vendorPicName && { error: true, helperText: 'Wajib diisi.' })}
              />
            )}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <Controller
            name='vendorPicPhoneNumber'
            control={control}
            rules={{ required: true }}
            render={({ field, formState: { errors } }) => (
              <TextField
                {...field}
                fullWidth
                label='Nomor Telepon Vendor'
                type='tel'
                placeholder='Masukkan nomor telepon vendor'
                InputLabelProps={{ shrink: !!field.value }}
                {...(errors.vendorPicPhoneNumber && { error: true, helperText: 'Wajib diisi.' })}
              />
            )}
          />
        </Grid>
        <Grid item xs={12}>
          <div className='flex flex-col gap-2 flex-1'>
            <Typography className='font-semibold'>Unggah Dokumen Penawaran Vendor (Opsional)</Typography>
            <div className='flex items-center gap-4'>
              <TextField
                key={JSON.stringify(filesContent)}
                size='small'
                fullWidth
                value={filesContent?.[0]?.name ?? getValues('tenderDocumentName')}
                placeholder='Tidak ada file dipilih'
                aria-readonly
                className='flex-1'
              />
              <Button variant='contained' onClick={() => openFilePicker()}>
                Pilih File
              </Button>
            </div>
          </div>
        </Grid>
      </Grid>
    </div>
  )
}

export default VendorInfo
