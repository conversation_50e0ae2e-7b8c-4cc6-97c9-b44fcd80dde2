import React from 'react'

import { FormControl, FormHelperText, InputLabel, MenuItem, Select, TextField } from '@mui/material'
import { Controller, useFormContext, useWatch } from 'react-hook-form'

import type { PurchaseType } from '@/types/apps/purchaseType'
import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { paymentMethodOptions } from '@/pages/purchase-order/config/options'
import { formatISO, toDate } from 'date-fns'
import { PurchaseOrderPaymentMethod } from '@/pages/purchase-order/config/enum'
import { ServiceOrderPayload, ServiceOrderPaymentMethod } from '@/types/serviceOrderTypes'

const PaymentDetails: React.FC = () => {
  const { control } = useFormContext<ServiceOrderPayload>()

  const paymentMethodWatch = useWatch({
    control,
    name: 'paymentMethod',
    defaultValue: undefined
  })

  return (
    <div className='flex flex-col gap-5'>
      <div className='flex gap-4 mt-4 w-full max-md:max-w-full flex-col sm:flex-row'>
        <Controller
          name='paymentMethod'
          control={control}
          rules={{ required: true }}
          render={({ field: { value, onChange }, formState: { errors } }) => (
            <FormControl fullWidth {...(errors.paymentMethod && { error: true })}>
              <InputLabel>Pembayaran</InputLabel>
              <Select
                label='Pembayaran'
                value={value}
                onChange={e => onChange((e.target as HTMLInputElement).value)}
                className='flex-1'
              >
                {paymentMethodOptions.map(option => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
              {errors.paymentMethod && <FormHelperText error>Wajib Diisi.</FormHelperText>}
            </FormControl>
          )}
        />
        {paymentMethodWatch === ServiceOrderPaymentMethod.TERM ? (
          <Controller
            name='paymentDueDate'
            control={control}
            rules={{ required: true }}
            render={({ field: { value, onChange }, formState: { errors } }) => (
              <AppReactDatepicker
                boxProps={{ className: 'is-full' }}
                selected={value ? toDate(value) : undefined}
                onChange={(date: Date) => onChange(formatISO(date))}
                dateFormat='eeee dd/MM/yyyy'
                minDate={new Date()}
                customInput={
                  <TextField
                    fullWidth
                    label='Tanggal Jatuh Tempo'
                    className='flex-1'
                    InputProps={{
                      readOnly: true
                    }}
                    {...(errors.paymentDueDate && { error: true, helperText: 'Wajib diisi.' })}
                  />
                }
              />
            )}
          />
        ) : null}
        <Controller
          name='estimateCompletedTime'
          control={control}
          render={({ field: { value, onChange }, formState: { errors } }) => (
            <AppReactDatepicker
              boxProps={{ className: 'is-full' }}
              selected={value ? toDate(value) : undefined}
              onChange={(date: Date) => onChange(formatISO(date))}
              dateFormat='eeee dd/MM/yyyy'
              minDate={new Date()}
              customInput={
                <TextField
                  fullWidth
                  label='Perkiraan Jasa Selesai (Opsional)'
                  className='flex-1'
                  InputProps={{
                    readOnly: true
                  }}
                />
              }
            />
          )}
        />
      </div>
      <Controller
        name='note'
        control={control}
        rules={{ required: false }}
        render={({ field, formState: { errors } }) => (
          <TextField
            {...field}
            label='Catatan (Opsional)'
            InputProps={{
              className: 'bg-white w-full'
            }}
            className='w-full'
          />
        )}
      />
    </div>
  )
}

export default PaymentDetails
