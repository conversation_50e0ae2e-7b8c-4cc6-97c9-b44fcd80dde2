import React, { useEffect, useState } from 'react'

import { Controller, useFormContext, useWatch } from 'react-hook-form'

import {
  Button,
  FormControl,
  FormHelperText,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'

import CurrencyField from '@/components/numeric/CurrencyField'
import { discountTypeOptions, taxTypeOptions } from '@/pages/purchase-order/config/options'
import { PurchaseOrderDiscountType, PurchaseOrderTaxType } from '@/pages/purchase-order/config/enum'
import NumberField from '@/components/numeric/NumberField'
import { TAX_PERCENTAGE } from '@/utils/constants'
import { useUpdateEffect } from 'react-use'
import { isNullOrUndefined } from '@/utils/helper'
import { ServiceOrderItem, ServiceOrderItemPayload, ServiceOrderPayload } from '@/types/serviceOrderTypes'

interface ItemDetailProps extends ServiceOrderItemPayload {
  index: number
}

const ItemDetail: React.FC<ItemDetailProps> = ({ index, item, unit, quantity, quantityUnit }) => {
  const { control, getValues, setValue, resetField } = useFormContext<ServiceOrderPayload>()

  const [pricePerUnit, taxType] = useWatch({
    control,
    name: [`items.${index}.pricePerUnit`, `items.${index}.taxType`],
    defaultValue: {}
  })

  // const [discountType, discountValue, isDiscountAfterTax] = useWatch({
  //   control,
  //   name: [`items.${index}.discountType`, `items.${index}.discountValue`, `items.${index}.isDiscountAfterTax`],
  //   defaultValue: {}
  // })

  // const [hasItemDiscount, setHasItemDiscount] = useState(!!discountValue)

  let subtotalPriceIncludeTax = 0
  let totalItemDiscount = 0
  let totalItemPrice = 0
  let taxAmount = 0
  if (pricePerUnit) {
    const subTotalPrice = pricePerUnit * quantity
    taxAmount = taxType === PurchaseOrderTaxType.EXCLUDE_TAX ? Math.round(subTotalPrice * TAX_PERCENTAGE) / 100 : 0
    subtotalPriceIncludeTax = subTotalPrice + taxAmount

    // if (discountType && discountValue) {
    //   // if (discountType === PurchaseOrderDiscountType.PERCENTAGE) {
    //   //   const amount = isDiscountAfterTax ? subtotalPriceIncludeTax : subTotalPrice
    //   //   totalItemDiscount = Math.round(amount * discountValue) / 100
    //   // } else if (discountType === PurchaseOrderDiscountType.FLAT) {
    //   //   totalItemDiscount = discountValue
    //   // }

    //   if (isDiscountAfterTax) {
    //     if (discountType === PurchaseOrderDiscountType.PERCENTAGE) {
    //       totalItemDiscount = Math.round(subtotalPriceIncludeTax * discountValue) / 100
    //     } else if (discountType === PurchaseOrderDiscountType.FLAT) totalItemDiscount = discountValue
    //   } else if (isDiscountAfterTax === false) {
    //     if (discountType === PurchaseOrderDiscountType.PERCENTAGE)
    //       totalItemDiscount = Math.round(subTotalPrice * discountValue) / 100
    //     else if (discountType === PurchaseOrderDiscountType.FLAT) totalItemDiscount = discountValue

    //     taxAmount = Math.round((subTotalPrice - totalItemDiscount) * TAX_PERCENTAGE) / 100
    //     subtotalPriceIncludeTax = subTotalPrice + taxAmount
    //   }
    // }

    totalItemPrice = Math.round((subtotalPriceIncludeTax - totalItemDiscount) * 100) / 100
  }

  // useUpdateEffect(() => {
  //   if (!hasItemDiscount) {
  //     setValue(`items.${index}.discountType`, undefined)
  //     setValue(`items.${index}.discountValue`, 0)
  //     setValue(`items.${index}.isDiscountAfterTax`, undefined)
  //   }
  // }, [hasItemDiscount])

  useEffect(() => {
    setValue(`items.${index}.totalTax`, taxAmount)
    setValue(`items.${index}.totalDiscount`, totalItemDiscount)
    setValue(`items.${index}.totalPrice`, totalItemPrice)
  }, [totalItemPrice])

  return (
    <div className='flex flex-col flex-1 shrink w-full basis-0 min-w-[240px] max-md:max-w-full'>
      <h3 className='self-start text-sm text-center text-gray-600 text-opacity-60'>
        <span className='font-bold'>Barang {index + 1}</span>
      </h3>
      <div className='flex gap-4 flex-col p-4 mt-4 w-full rounded-lg bg-gray-600 bg-opacity-10 max-md:max-w-full'>
        <div className='flex flex-col gap-1 w-full text-base leading-none text-gray-600 text-opacity-60 max-md:max-w-full'>
          <div className='font-medium text-gray-600 text-opacity-90'>
            {item.name} - {item.brandName} {item.number}
          </div>
          {unit ? (
            <>
              <div className='mt-1'>
                {unit.brandName} {unit.type}
              </div>
              <div className='mt-1 text-sm leading-none'>
                {unit.number} - No. Lambung {unit.hullNumber}
              </div>
            </>
          ) : null}
          <div className='mt-1 text-sm leading-none text-green-700'>
            {quantity} {quantityUnit}
          </div>
        </div>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Controller
              name={`items.${index}.serviceName`}
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  label='Jenis Perbaikan'
                  InputProps={{
                    className: 'bg-white w-full'
                  }}
                  className='w-full'
                  {...(errors.items?.[index]?.serviceName && { error: true, helperText: 'Wajib diisi.' })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              name={`items.${index}.serviceDescription`}
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  label='Detil Perbaikan'
                  InputProps={{
                    className: 'bg-white w-full'
                  }}
                  className='w-full'
                  {...(errors.items?.[index]?.serviceDescription && { error: true, helperText: 'Wajib diisi.' })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Controller
              name={`items.${index}.pricePerUnit`}
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  label='Harga Jasa'
                  InputProps={{
                    className: 'bg-white',
                    inputComponent: CurrencyField as any
                  }}
                  {...(errors.items?.[index]?.pricePerUnit && { error: true, helperText: 'Wajib diisi.' })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Controller
              name={`items.${index}.taxType`}
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <FormControl fullWidth>
                  <InputLabel id='permissionGroupId' error={Boolean(errors.items?.[index]?.taxType)}>
                    Pajak
                  </InputLabel>
                  <Select
                    label='Pajak'
                    {...field}
                    placeholder='Pilih Pajak'
                    className='bg-white'
                    error={Boolean(errors.items?.[index]?.taxType)}
                  >
                    {taxTypeOptions.map(role => (
                      <MenuItem key={role.value} value={role.value}>
                        {role.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.items?.[index]?.taxType && <FormHelperText error>Wajib dipilih.</FormHelperText>}
                </FormControl>
              )}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <TextField
              label='Sub Total Harga Jasa'
              disabled
              value={subtotalPriceIncludeTax}
              InputProps={{
                className: 'bg-white',
                inputComponent: CurrencyField as any
              }}
            />
          </Grid>
        </Grid>
        {/* {hasItemDiscount ? (
          <div className='flex flex-col gap-2'>
            <Typography className='font-medium'>Diskon Item</Typography>
            <Grid container spacing={2} rowSpacing={4}>
              <Controller
                name={`items.${index}.discountType`}
                control={control}
                rules={{ required: true }}
                render={({ field: { value: discountTypeValue, ...field }, formState: { errors } }) => (
                  <>
                    <Grid item xs={12} md={4}>
                      <FormControl className='flex-1' fullWidth>
                        <InputLabel id='discountType' error={Boolean(errors.items?.[index]?.discountType)}>
                          Jenis Diskon
                        </InputLabel>
                        <Select
                          label='Jenis Diskon'
                          {...field}
                          value={discountTypeValue}
                          placeholder='Pilih Jenis Diskon'
                          className='bg-white'
                          onChange={e => {
                            field.onChange((e.target as HTMLInputElement).value)
                            resetField(`items.${index}.discountValue`)
                          }}
                          error={Boolean(errors.items?.[index]?.discountType)}
                        >
                          {discountTypeOptions.map(role => (
                            <MenuItem key={role.value} value={role.value}>
                              {role.label}
                            </MenuItem>
                          ))}
                        </Select>
                        {errors.items?.[index]?.discountType && <FormHelperText error>Wajib dipilih.</FormHelperText>}
                      </FormControl>
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <Controller
                        name={`items.${index}.discountValue`}
                        control={control}
                        rules={{ required: true }}
                        render={({ field, formState: { errors } }) => {
                          const isPercentage = discountTypeValue === PurchaseOrderDiscountType.PERCENTAGE
                          return (
                            <TextField
                              {...field}
                              label='Jumlah Diskon'
                              InputProps={{
                                className: 'bg-white',
                                endAdornment: isPercentage ? '%' : '',
                                inputComponent: (!isPercentage ? CurrencyField : NumberField) as any,
                                inputProps: {
                                  isAllowed: ({ floatValue }) =>
                                    floatValue <= (isPercentage ? 100 : subtotalPriceIncludeTax) ||
                                    floatValue === undefined
                                }
                              }}
                              {...(errors.items?.[index]?.discountValue && { error: true, helperText: 'Wajib diisi.' })}
                            />
                          )
                        }}
                      />
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <Controller
                        name={`items.${index}.isDiscountAfterTax`}
                        control={control}
                        rules={{
                          validate: value => {
                            if (value == null) {
                              return 'Wajib dipilih.'
                            }
                            return true
                          }
                        }}
                        render={({ field: { onChange, value, ...field }, formState: { errors } }) => (
                          <FormControl fullWidth>
                            <InputLabel
                              id='isDiscountAfterTax'
                              error={Boolean(errors.items?.[index]?.isDiscountAfterTax)}
                            >
                              Pajak Diskon
                            </InputLabel>
                            <Select
                              {...field}
                              onChange={e => onChange((e.target as HTMLInputElement).value === '1')}
                              value={!isNullOrUndefined(value) ? (value ? 1 : 2) : undefined}
                              label='Pajak Diskon'
                              placeholder='Pilih Pajak Diskon'
                              className='bg-white'
                              error={Boolean(errors.items?.[index]?.isDiscountAfterTax)}
                            >
                              <MenuItem key={2} value={2}>
                                Sebelum Pajak
                              </MenuItem>
                              <MenuItem key={1} value={1}>
                                Sesudah Pajak
                              </MenuItem>
                            </Select>
                            {errors.items?.[index]?.isDiscountAfterTax && (
                              <FormHelperText error>Wajib dipilih.</FormHelperText>
                            )}
                          </FormControl>
                        )}
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <TextField
                        label='Sub Total Diskon'
                        disabled
                        fullWidth
                        value={totalItemDiscount}
                        InputProps={{
                          className: 'bg-white',
                          inputComponent: CurrencyField as any
                        }}
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <TextField
                        label='Sub Total Item Setelah Diskon'
                        disabled
                        fullWidth
                        value={totalItemPrice}
                        InputProps={{
                          className: 'bg-white',
                          inputComponent: CurrencyField as any
                        }}
                      />
                    </Grid>
                  </>
                )}
              />
            </Grid>
          </div>
        ) : null} */}
        {/* <Button
          variant='outlined'
          color={hasItemDiscount ? 'error' : 'primary'}
          onClick={() => setHasItemDiscount(current => !current)}
        >
          {hasItemDiscount ? 'Hapus Diskon Item' : 'Tambah Diskon Item'}
        </Button> */}
      </div>
    </div>
  )
}

const ItemDetails: React.FC = () => {
  const { control } = useFormContext<ServiceOrderPayload>()

  const itemsWatch = useWatch({
    control,
    name: `items`,
    defaultValue: []
  })

  return (
    <div className='flex flex-col gap-4 items-start mt-4 w-full tracking-normal max-md:max-w-full'>
      {itemsWatch.map((item, index) => (
        <ItemDetail key={item.itemId} index={index} {...item} />
      ))}
    </div>
  )
}

export default ItemDetails
