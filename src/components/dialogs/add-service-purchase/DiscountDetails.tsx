import React, { useEffect, useState } from 'react'
import { Controller, useFormContext, useWatch } from 'react-hook-form'
import {
  Button,
  FormControl,
  FormHelperText,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'

import { PurchasePayload } from '@/pages/purchase-order/config/types'
import { discountTypeOptions } from '@/pages/purchase-order/config/options'
import { PurchaseOrderDiscountType, PurchaseOrderTaxType } from '@/pages/purchase-order/config/enum'
import CurrencyField from '@/components/numeric/CurrencyField'
import NumberField from '@/components/numeric/NumberField'
import { isNullOrUndefined } from '@/utils/helper'
import { useUpdateEffect } from 'react-use'
import { TAX_PERCENTAGE } from '@/utils/constants'
import { dark } from '@mui/material/styles/createPalette'

const DiscountDetails: React.FC = () => {
  const { control, setValue, resetField } = useFormContext<PurchasePayload>()

  const itemsWatch = useWatch({
    control,
    name: `items`,
    defaultValue: []
  })

  const [totals, setTotals] = useState({
    subTotalItems: 0,
    totalPrice: 0,
    discountAmount: 0
  })

  const { subTotalItems, totalPrice, discountAmount } = totals

  const [discountType, discountValue] = useWatch({
    control,
    name: ['discountType', 'discountValue'],
    defaultValue: {}
  })

  const [hasPurchaseDiscount, setHasPurchaseDiscount] = useState(!!discountValue)

  useUpdateEffect(() => {
    if (!hasPurchaseDiscount) {
      setValue(`discountType`, undefined)
      setValue(`discountValue`, 0)
    }
  }, [hasPurchaseDiscount])

  useEffect(() => {
    let subtotal = 0
    let tp = 0
    let td = 0
    let tt = 0
    itemsWatch.forEach(item => {
      subtotal += Number(item.totalPrice ?? 0)
      td += item.totalDiscount
      tt += item.totalTax
    })

    let da = 0
    if (discountValue) {
      if (discountType === PurchaseOrderDiscountType.PERCENTAGE) {
        da = Math.round(subtotal * discountValue) / 100
      } else if (discountType === PurchaseOrderDiscountType.FLAT) {
        da = discountValue
      }
    }

    tp = subtotal - da
    td += da

    setTotals({
      discountAmount: da,
      subTotalItems: subtotal,
      totalPrice: tp
    })

    setValue('subTotalItems', subtotal)
    setValue('totalTax', tt)
    setValue('discountAmount', da)
    setValue('totalDiscount', td)
    setValue('totalPrice', tp)
  }, [itemsWatch, discountValue, discountType])

  return (
    <div className='flex gap-4 flex-col p-4 mt-4 w-full rounded-lg bg-gray-600 bg-opacity-10 max-md:max-w-full'>
      <h3 className='self-start text-sm text-center text-gray-600 text-opacity-60'>
        <span className='font-bold'>Diskon Jasa</span>
      </h3>
      {hasPurchaseDiscount ? (
        <div className='flex flex-col gap-2'>
          <Grid container spacing={2} rowSpacing={4}>
            <Controller
              name={`discountType`}
              control={control}
              rules={{ required: true }}
              render={({ field: { value: discountTypeValue, ...field }, formState: { errors } }) => (
                <>
                  <Grid item xs={12} md={6}>
                    <FormControl className='flex-1' fullWidth>
                      <InputLabel id='taxType' error={Boolean(errors.discountType)}>
                        Jenis Diskon
                      </InputLabel>
                      <Select
                        label='Jenis Diskon'
                        {...field}
                        value={discountTypeValue}
                        placeholder='Pilih Jenis Diskon'
                        className='bg-white'
                        error={Boolean(errors.discountType)}
                        onChange={e => {
                          field.onChange((e.target as HTMLInputElement).value)
                          resetField('discountValue')
                        }}
                      >
                        {discountTypeOptions.map(role => (
                          <MenuItem key={role.value} value={role.value}>
                            {role.label}
                          </MenuItem>
                        ))}
                      </Select>
                      {errors.discountType && <FormHelperText error>Wajib dipilih.</FormHelperText>}
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Controller
                      name={`discountValue`}
                      control={control}
                      rules={{ required: true }}
                      render={({ field, formState: { errors } }) => {
                        const isPercentage = discountTypeValue === PurchaseOrderDiscountType.PERCENTAGE
                        return (
                          <TextField
                            {...field}
                            label='Jumlah Diskon'
                            fullWidth
                            InputProps={{
                              className: 'bg-white',
                              endAdornment: isPercentage ? '%' : '',
                              inputComponent: (!isPercentage ? CurrencyField : NumberField) as any,
                              inputProps: {
                                isAllowed: ({ floatValue }) =>
                                  floatValue <= (isPercentage ? 100 : subTotalItems) || floatValue === undefined
                              }
                            }}
                            {...(errors.discountValue && { error: true, helperText: 'Wajib diisi.' })}
                          />
                        )
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label='Sub Total Diskon'
                      disabled
                      fullWidth
                      value={discountAmount}
                      InputProps={{
                        className: 'bg-white',
                        inputComponent: CurrencyField as any
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label='Sub Total Jasa Setelah Diskon'
                      disabled
                      fullWidth
                      value={totalPrice}
                      InputProps={{
                        className: 'bg-white',
                        inputComponent: CurrencyField as any
                      }}
                    />
                  </Grid>
                </>
              )}
            />
          </Grid>
        </div>
      ) : (
        <div className='flex justify-center items-center py-4'>
          <Typography variant='subtitle1'>Tidak ada diskon jasa</Typography>
        </div>
      )}
      <Button
        variant='outlined'
        color={hasPurchaseDiscount ? 'error' : 'primary'}
        onClick={() => setHasPurchaseDiscount(current => !current)}
      >
        {hasPurchaseDiscount ? 'Hapus Diskon Jasa' : 'Tambah Diskon Jasa'}
      </Button>
    </div>
  )
}

export default DiscountDetails
