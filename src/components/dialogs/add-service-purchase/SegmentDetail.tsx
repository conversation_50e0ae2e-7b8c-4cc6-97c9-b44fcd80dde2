import { Card, CardContent, Typography } from '@mui/material'
import {
  getCoreRowModel,
  useReactTable,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  getPaginationRowModel,
  getSortedRowModel,
  ColumnDef
} from '@tanstack/react-table'
import { useMemo, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import Table from '@/components/table'
import { WoSegmentType } from '@/types/woTypes'
import RnMQueryMethods from '@/api/services/rnm/query'
import DialogAddSegmentWo from '@/components/dialogs/add-segment-wo'
import { FormProvider, useForm } from 'react-hook-form'
import { SegmentType } from '@/pages/repair-and-maintenance/wo/create-wo/config/schema'
import { useSrList } from '@/pages/service-order/context/SrListContext'
import truncateString from '@/core/utils/truncate'

const workOrderSegmentColumns = (): ColumnDef<WoSegmentType>[] => [
  {
    header: 'No.',
    accessorKey: 'number',
    maxSize: 8,
    cell: ({ row }) => (
      <Typography color='primary' variant='subtitle2' sx={{ cursor: 'pointer' }}>
        {truncateString(row.original.number, 15)}
      </Typography>
    )
  },
  {
    header: 'Job Code',
    accessorKey: 'jobCode',
    cell: ({ row }) => (
      <Typography variant='subtitle2'>
        {row.original.jobCode
          ? `${row.original.jobCode.code} | ${truncateString(row.original.jobCode.description, 12)}`
          : '-'}
      </Typography>
    )
  },
  {
    header: 'SMCS',
    accessorKey: 'componentCode',
    cell: ({ row }) => (
      <Typography variant='subtitle2'>
        {row.original.componentCode
          ? `${row.original.componentCode.code} | ${truncateString(row.original.componentCode.description, 12)}`
          : '-'}
      </Typography>
    )
  },
  {
    header: 'Modifier',
    accessorKey: 'modifierCode',
    cell: ({ row }) => (
      <Typography variant='subtitle2'>
        {row.original.modifierCode
          ? `${row.original.modifierCode.code} | ${truncateString(row.original.modifierCode.description, 12)}`
          : '-'}
      </Typography>
    )
  }
]

const SegmentDetail = ({ segment }: { segment?: WoSegmentType }) => {
  const [dialogOpen, setDialogOpen] = useState(false)

  const methods = useForm<SegmentType>()

  const handleShowDetail = () => {
    setDialogOpen(true)
    methods.reset({
      jobCodeId: segment?.jobCodeId,
      componentCodeId: segment?.componentCodeId,
      modifierCodeId: segment?.modifierCodeId
    })
  }

  const tableOptions = useMemo(
    () => ({
      data: segment ? [segment] : [],
      columns: workOrderSegmentColumns(),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [segment]
  )

  const table = useReactTable<any>(tableOptions)

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-4'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Segment</Typography>
          </div>
          <div className='shadow-xs rounded-[8px]'>
            <Table
              table={table}
              emptyLabel={
                <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                  <Typography>Belum ada Segment</Typography>
                  <Typography className='text-sm text-gray-400'>
                    Segment dari Work Order akan ditampilkan di sini
                  </Typography>
                </td>
              }
              disablePagination
            />
          </div>
        </CardContent>
      </Card>

      {dialogOpen && (
        <FormProvider {...methods}>
          <DialogAddSegmentWo
            viewOnly
            open={dialogOpen}
            setOpen={setDialogOpen}
            onOpenDialogItem={() => {}}
            onOpenDialogMisc={() => {}}
            configOptions={{
              segment: { ...segment, workOrderId: segment?.workOrderId },
              componentCodeList: [],
              jobCodeList: [],
              modifierCodeList: [],
              fetchJobCodeLoading: false,
              fetchComponentCodeLoading: false,
              fetchModifierCodeLoading: false,
              jobCodeQuery: '',
              setJobCodeQuery: () => {},
              componentCodeQuery: '',
              setComponentCodeQuery: () => {},
              modifierCodeQuery: '',
              setModifierCodeQuery: () => {}
            }}
          />
        </FormProvider>
      )}
    </>
  )
}

export default SegmentDetail
