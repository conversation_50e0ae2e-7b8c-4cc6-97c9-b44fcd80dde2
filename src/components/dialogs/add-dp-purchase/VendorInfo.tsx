import React from 'react'

import { Grid, TextField, Typography } from '@mui/material'
import { Controller, useFormContext } from 'react-hook-form'

import { PurchasePayload } from '@/pages/purchase-order/config/types'

const VendorInfo: React.FC = () => {
  const { control } = useFormContext<PurchasePayload>()

  return (
    <div className='flex flex-col mt-8 w-full max-w-[780px] max-md:max-w-full'>
      <h2 className='self-start text-sm tracking-normal text-center text-gray-600 text-opacity-60'>
        <Typography color='GrayText' className='font-bold'>
          Informasi Vendor
        </Typography>
      </h2>
      <Grid container spacing={3} className='mt-2'>
        <Grid item xs={12}>
          <Controller
            name='vendorName'
            control={control}
            rules={{ required: true }}
            render={({ field, formState: { errors } }) => (
              <TextField
                {...field}
                value={field.value}
                fullWidth
                label='Nama Vendor'
                placeholder='Masukkan nama vendor'
                className='flex-1'
                InputLabelProps={{ shrink: !!field.value }}
                {...(errors.vendorName && { error: true, helperText: 'Wajib diisi.' })}
              />
            )}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <Controller
            name='vendorPicName'
            control={control}
            render={({ field, formState: { errors } }) => (
              <TextField
                {...field}
                fullWidth
                label='PIC Vendor'
                placeholder='Masukkan PIC vendor'
                InputLabelProps={{ shrink: !!field.value }}
                {...(errors.vendorPicName && { error: true, helperText: 'Wajib diisi.' })}
              />
            )}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <Controller
            name='vendorPicPhoneNumber'
            control={control}
            render={({ field, formState: { errors } }) => (
              <TextField
                {...field}
                fullWidth
                label='Nomor Telepon Vendor'
                type='tel'
                placeholder='Masukkan nomor telepon vendor'
                InputLabelProps={{ shrink: !!field.value }}
                {...(errors.vendorPicPhoneNumber && { error: true, helperText: 'Wajib diisi.' })}
              />
            )}
          />
        </Grid>
      </Grid>
    </div>
  )
}

export default VendorInfo
