// MUI Imports

import Dialog, { DialogProps } from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import DialogActions from '@mui/material/DialogActions'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import { FormHelperText, IconButton } from '@mui/material'
import LoadingButton from '@mui/lab/LoadingButton'
import { useFilePicker } from 'use-file-picker'
import { FileAmountLimitValidator, FileTypeValidator, FileSizeValidator } from 'use-file-picker/validators'
import { AvailableCategoryType, ExportImportScope } from '@/types/exportImportTypes'
import { useImportState } from '@/core/hooks/useExportImport'
import ProcessingImportDialog from '../processing-import-dialog'

type ImportDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  scope: ExportImportScope
  onSubmit: () => void
  type?: AvailableCategoryType // only needed for Category scope
  renderAdditional?: React.ReactNode
}

const ImportDialog = ({ open, setOpen, scope, onSubmit, type, renderAdditional = null }: ImportDialogProps) => {
  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    clear()
    setOpen(false)
  }

  const {
    importFn,
    getTemplate,
    isImportTemplateLoading,
    isLoading: isImportRequestLoading,
    openProcessingImportDialog,
    setOpenProcessingImportDialog
  } = useImportState(scope)

  const {
    openFilePicker,
    filesContent,
    clear,
    errors,
    loading: isLoading
  } = useFilePicker({
    multiple: false,
    accept: ['xls', 'xlsx'],
    readAs: 'DataURL',
    readFilesContent: true,
    validators: [
      new FileAmountLimitValidator({ max: 1 }),
      new FileTypeValidator(['xls', 'xlsx']),
      new FileSizeValidator({ maxFileSize: 50 * 1024 * 1024 /* 50 MB */ })
    ]
  })

  const handleSubmit = async () => {
    try {
      await importFn({
        file: filesContent?.[0]?.content,
        fileName: filesContent?.[0]?.name,
        type
      })
      onSubmit()
    } finally {
      clear()
    }
  }

  const handleDownload = () => {
    getTemplate().then(template => {
      const url = window.URL.createObjectURL(template)
      const link = document.createElement('a')
      link.style.display = 'none'
      link.href = url
      link.setAttribute('download', `template-${scope.label}`)
      document.body.appendChild(link)
      link.click()
      link.parentNode.removeChild(link)
      window.URL.revokeObjectURL(url)
    })
  }

  return (
    <>
      <Dialog open={open} onClose={handleClose}>
        <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
          Impor List {scope.label}
          <Typography component='span' className='flex flex-col text-center'>
            Impor {scope.label} dari dokumen untuk didaftarkan ke list
          </Typography>
        </DialogTitle>
        <DialogContent className='overflow-visible pbs-0 sm:pbe-8 sm:px-12'>
          <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
            <i className='ri-close-line text-textSecondary' />
          </IconButton>
          <div className='flex flex-col gap-2 flex-1'>
            <Typography className='font-semibold'>Unggah Dokumen</Typography>
            <Typography>Gunakan format yang sesuai dengan template Equalindo360</Typography>
            <div className='flex items-center gap-4 flex-col sm:flex-row'>
              <TextField
                key={JSON.stringify(filesContent)}
                size='small'
                fullWidth
                value={filesContent?.[0]?.name}
                placeholder='.xls atau .xlsx, maks. 50MB'
                aria-readonly
                className='flex-1'
              />
              <Button
                variant='contained'
                onClick={() => openFilePicker()}
                disabled={isLoading}
                className='is-full sm:is-auto'
              >
                Unggah
              </Button>
            </div>
            {!errors && (
              <FormHelperText className='m-0' error>
                Format tidak didukung atau tidak sesuai dengan template
              </FormHelperText>
            )}
          </div>
          <div className='flex flex-col gap-2 bg-bgGray dark:bg-inherit rounded-lg p-4 mt-6'>
            <Typography variant='caption'>Belum memiliki dokumen sesuai template Equalindo360?</Typography>
            <Button
              color='primary'
              variant='outlined'
              onClick={handleDownload}
              disabled={isImportTemplateLoading}
              className='is-full sm:is-auto bg-white dark:bg-inherit'
            >
              Unduh Template Dokumen
            </Button>
          </div>
          {renderAdditional}
        </DialogContent>
        <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
          <Button onClick={() => setOpen(false)} variant='outlined' disabled={isLoading} className='is-full sm:is-auto'>
            BATALKAN
          </Button>
          <LoadingButton
            startIcon={<></>}
            loading={isImportRequestLoading}
            loadingPosition='start'
            variant='contained'
            onClick={handleSubmit}
            className='px-8 is-full !ml-0 sm:is-auto'
            disabled={!filesContent.length}
          >
            IMPOR LIST
          </LoadingButton>
        </DialogActions>
      </Dialog>
      {openProcessingImportDialog && (
        <ProcessingImportDialog
          open={openProcessingImportDialog}
          onClose={() => setOpenProcessingImportDialog(false)}
          contentScope={scope.label}
        />
      )}
    </>
  )
}

export default ImportDialog
