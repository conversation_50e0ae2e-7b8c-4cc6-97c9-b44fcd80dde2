import Table from '@/components/table'
import * as Sentry from '@sentry/react'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogProps,
  DialogTitle,
  FormHelperText,
  Grid,
  IconButton,
  TextField,
  Typography
} from '@mui/material'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  Row,
  useReactTable
} from '@tanstack/react-table'
import { memo, useCallback, useEffect, useMemo, useState } from 'react'
import { toast } from 'react-toastify'
import { miscTableColumns, partTableColumns } from './config'
import { AddItemDto } from '../add-item-segment-wo'
import { Controller, useFieldArray, useForm, useFormContext } from 'react-hook-form'
import { SegmentType, ItemType } from '@/pages/repair-and-maintenance/wo/create-wo/config/schema'
import AddWarehouseItemDialog from '../add-warehouse-item'
import { useQuery } from '@tanstack/react-query'
import AccountsQueryMethods, { DIVISION_LIST_QUERY_KEY } from '@/api/services/account/query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { DivisionType } from '@/types/accountTypes'
import ApprovalListCard from '@/pages/material-request/create/components/ApprovalListCard'
import { UserType } from '@/types/userTypes'
import { StuffReqItemPayload, StuffReqPayload } from '@/types/payload'
import { useCreateStuffReq } from '@/api/services/wp/mutation'

type AddItemSegmentProps = {
  viewOnly?: boolean
  open: boolean
  setOpen: (open: boolean) => void
  onOpenDialogItem: () => void
  parts?: AddItemDto[]
  onAddSegment?: (s: StuffReqPayload) => void
  onEditItem?: (item: ItemType) => void
  loading?: boolean
  approverList: UserType[]
  successCb?: () => void
  type?: 'TAKE' | 'RETURN'
}

const DialogAddItemSegment = (props: AddItemSegmentProps) => {
  const { open, setOpen, onOpenDialogItem, approverList = [], successCb = () => {}, type = 'TAKE' } = props
  const [rowSelection, setRowSelection] = useState({})

  const {
    control,
    handleSubmit,
    reset,
    getValues,
    formState: { errors }
  } = useFormContext<StuffReqPayload>()
  const [selectedItem, setItem] = useState<ItemType | null>(null)

  const { fields, remove } = useFieldArray({ control, name: 'items' })

  const { mutate: createMutate, isLoading: createLoading } = useCreateStuffReq()

  const isLoading = createLoading || props.loading

  const { data: divisions } = useQuery({
    queryKey: [DIVISION_LIST_QUERY_KEY],
    queryFn: () => {
      return AccountsQueryMethods.getDivisionList({ limit: Number.MAX_SAFE_INTEGER })
    },
    placeholderData: defaultListData as ListResponse<DivisionType>
  })

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  const handleDeleteItem = useCallback(
    (id: string) => {
      remove(fields.findIndex(item => item.id === id))
    },
    [fields]
  )

  let selectedRows: Row<StuffReqItemPayload>[]

  const partTableOptions = useMemo(
    () => ({
      data: fields ?? [],
      columns: partTableColumns({
        viewOnly: props.viewOnly,
        detail: item => {
          setItem({ ...item, itemId: item.itemId })
        },
        delete: handleDeleteItem,
        woNote: true,
        return: type === 'RETURN',
        control,
        selectedRows
      }),
      state: {
        rowSelection
      },
      enableRowSelection: true,
      onRowSelectionChange: setRowSelection,
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [props, fields, selectedRows, rowSelection]
  )

  const tableParts = useReactTable<any>(partTableOptions)

  selectedRows = tableParts.getSelectedRowModel().flatRows

  const onSubmitSegment = (dto: StuffReqPayload) => {
    if ((approverList?.length ?? 0) <= 0) {
      toast.error('Default Approval belum tersedia. Silahkan hubungi admin terlebih dahulu.')
      return
    }
    Sentry.captureMessage(`Submit Stuff Request: ${JSON.stringify(dto)}`)

    const itemsRow = selectedRows.map(item => ({ ...item.original }))

    if (itemsRow?.length <= 0) {
      toast.error('Silahkan pilih item yang akan dikembalikan')
      return
    }

    const items =
      type === 'RETURN'
        ? itemsRow?.map((i, idx) => {
            const item = dto.items.find(item => item.itemId === i.itemId)
            return {
              itemId: item?.itemId,
              quantity: item?.returnQuantity,
              quantityUnit: item?.quantityUnit,
              note: item?.note,
              workOrderSegmentId: item?.workOrderSegmentId,
              ...(type === 'RETURN' && { stuffRequestItemId: item?.stuffRequestItemId }),
              largeUnitQuantity: item?.largeUnitQuantity,
              serialNumber: item?.serialNumber
            }
          })
        : dto.items?.map(item => ({
            itemId: item?.itemId,
            quantity: item?.quantity,
            quantityUnit: item?.quantityUnit,
            note: item?.note,
            workOrderSegmentId: item?.workOrderSegmentId,
            largeUnitQuantity: item?.largeUnitQuantity,
            serialNumber: item?.serialNumber
          }))

    createMutate(
      {
        approvals: approverList.map(app => ({ userId: app.id })),
        items,
        type: dto.type,
        workProcessId: dto.workProcessId,
        note: dto.note
      },
      {
        onSuccess: () => {
          toast.success(dto.type === 'TAKE' ? 'Pengambilan berhasil dibuat' : 'Pengembalian berhasil dibuat')
          setOpen(false)
          props?.onAddSegment?.(dto)
          successCb()
        }
      }
    )
  }

  useEffect(() => {
    if (open) {
      reset({
        ...getValues(),
        type
      })
    }
  }, [open])

  return (
    <>
      <Dialog maxWidth='lg' open={open} onClose={handleClose}>
        <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
          {type === 'TAKE' ? 'Tambah Pengambilan Barang' : 'Kembalikan Barang'}
          {type === 'TAKE' ? (
            <Typography component='span' className='flex flex-col text-center'>
              Tambahkan barang yang kamu butuhkan untuk pengerjaan Work Process ini
            </Typography>
          ) : (
            <Typography component='span' className='flex flex-col text-center'>
              Kembalikan barang yang tidak terpakai di pengerjaan Work Process ini
            </Typography>
          )}
        </DialogTitle>
        <DialogContent className='pbs-0 sm:pbe-16 sm:px-8 !py-4'>
          <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
            <i className='ri-close-line text-textSecondary' />
          </IconButton>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <div className='flex justify-between items-center'>
                <Typography>Part & Miscellaneous</Typography>
                {type === 'TAKE' && (
                  <Button onClick={onOpenDialogItem} variant='outlined' size='small'>
                    Tambah Barang
                  </Button>
                )}
              </div>
            </Grid>
            <Grid item xs={12} className='space-y-2'>
              <div className='shadow-sm rounded-lg'>
                <Table
                  headerColor='green'
                  table={tableParts}
                  emptyLabel={
                    <td colSpan={tableParts.getVisibleFlatColumns().length} className='text-center h-60 space-y-2'>
                      <Typography variant='h5'> Belum ada Barang</Typography>
                      <Typography>Barang yang dipilih untuk Segment ini akan ditampilkan di sini</Typography>
                    </td>
                  }
                />
              </div>
              {errors?.items && <FormHelperText error>Wajib dipilih</FormHelperText>}
            </Grid>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='note'
                render={({ field }) => <TextField label='Catatan (Opsional)' {...field} fullWidth />}
              />
            </Grid>
            <Grid item xs={12}>
              <ApprovalListCard approverList={approverList} />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
          {props.viewOnly ? (
            <Button
              variant='outlined'
              color='secondary'
              onClick={() => {
                setOpen(false)
                reset()
              }}
            >
              Tutup
            </Button>
          ) : (
            <>
              <Button
                disabled={props.loading}
                onClick={() => {
                  setOpen(false)
                  reset()
                }}
                variant='outlined'
                className='is-full sm:is-auto'
              >
                BATALKAN
              </Button>
              <LoadingButton
                startIcon={<></>}
                loading={isLoading}
                loadingPosition='start'
                variant='contained'
                onClick={handleSubmit(
                  data => onSubmitSegment(data),
                  errors => {
                    Sentry.captureException(getValues())
                    Object.entries(errors).forEach(([field, error]) => {
                      toast.error(`${field}: ${error?.message}`, {
                        autoClose: 5000
                      })
                    })
                  }
                )}
                className='px-8 is-full !ml-0 sm:is-auto'
              >
                BUAT PERMINTAAN
              </LoadingButton>
            </>
          )}
        </DialogActions>
      </Dialog>
      {!!selectedItem && (
        <AddWarehouseItemDialog
          withoutUnit
          currentItem={{ ...selectedItem, id: 0 }}
          viewOnly
          open={!!selectedItem}
          setOpen={open => setItem(!open && null)}
          onSubmit={() => {}}
        />
      )}
    </>
  )
}

export default memo(DialogAddItemSegment)
