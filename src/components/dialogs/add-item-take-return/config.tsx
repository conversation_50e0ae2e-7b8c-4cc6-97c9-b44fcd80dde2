import { z } from 'zod'
import { ItemType } from '@/pages/repair-and-maintenance/wo/create-wo/config/schema'
import { Checkbox, Grid, IconButton, TextField, Tooltip, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { AddItemDto } from '../add-item-segment-wo'
import truncateString from '@/core/utils/truncate'
import { Control, Controller } from 'react-hook-form'
import { StuffReqPayload } from '@/types/payload'
import NumberField from '@/components/numeric/NumberField'

type PartColumnType = {
  returnedQuantity: number
  smallQuantity: number
} & AddItemDto

const partColumnHelper = createColumnHelper<PartColumnType>()
const miscColumnHelper = createColumnHelper<ItemType>()

type RowActionType = {
  detail: (item: ItemType) => void
  edit?: (item: ItemType) => void
  delete: (id: string) => void
  viewOnly?: boolean
  woNote?: boolean
  return?: boolean
  control?: Control<StuffReqPayload, any>
  selectedRows?: any
}

export const partTableColumns = (rowAction: RowActionType) => {
  const { woNote = false, return: returnItem } = rowAction
  return [
    ...(returnItem
      ? [
          {
            id: 'selection',
            size: 20,
            header: ({ table }) => {
              return (
                <Checkbox
                  checked={table.getIsAllRowsSelected()}
                  indeterminate={table.getIsSomeRowsSelected()}
                  onChange={table.getToggleAllRowsSelectedHandler()} //or getToggleAllPageRowsSelectedHandler
                />
              )
            },
            cell: ({ row }) => {
              return <Checkbox checked={row.getIsSelected()} onChange={row.getToggleSelectedHandler()} />
            }
          }
        ]
      : []),
    partColumnHelper.accessor('number', {
      header: 'Kode Barang'
    }),
    partColumnHelper.accessor('serialNumber', {
      header: 'NO. SERIAL',
      cell: ({ row }) => row.original.serialNumber ?? '-'
    }),
    partColumnHelper.accessor('name', {
      header: 'NAMA ITEM'
    }),
    partColumnHelper.accessor('brandName', {
      header: 'MERK ITEM'
    }),
    partColumnHelper.accessor('largeUnitQuantity', {
      header: 'QTY',
      size: 100,
      cell: ({ row }) => row.original.quantity + ' ' + row.original.quantityUnit
    }),
    ...(returnItem
      ? [
          partColumnHelper.display({
            id: 'return-item',
            header: 'Kembalikan',
            cell: ({ row }) => {
              return (
                <Controller
                  control={rowAction.control}
                  name={`items.${row.index}.returnQuantity`}
                  render={({ field }) => (
                    <TextField
                      disabled={!row.getIsSelected()}
                      size='small'
                      {...field}
                      InputProps={{
                        endAdornment: row.original.quantityUnit,
                        inputComponent: NumberField as any,
                        inputProps: {
                          isAllowed: ({ floatValue }) =>
                            !floatValue || floatValue <= row.original.smallQuantity - row.original.returnedQuantity
                        }
                      }}
                    />
                  )}
                />
              )
            }
          })
        ]
      : []),
    ...(woNote
      ? []
      : [
          partColumnHelper.accessor('note', {
            header: 'KETERANGAN',
            cell: ({ row }) => (
              <Tooltip sx={{ cursor: 'default' }} title={row.original.note}>
                <Typography>{truncateString(row.original.note ?? '-', 10)}</Typography>
              </Tooltip>
            )
          })
        ]),
    partColumnHelper.display({
      id: 'actions',
      header: 'ACTION',
      cell: ({ row }) => {
        return (
          <Grid container>
            {!rowAction.viewOnly && (
              <>
                <Grid item>
                  <IconButton size='small' onClick={() => rowAction.delete(row.original.id)}>
                    <i className='x-delete-icon text-error' />
                  </IconButton>
                </Grid>
                {rowAction?.edit && (
                  <Grid item>
                    <IconButton size='small' onClick={() => rowAction.edit(row.original)}>
                      <i className='ri-pencil-fill text-textSecondary' />
                    </IconButton>
                  </Grid>
                )}
              </>
            )}
            <Grid item>
              <IconButton size='small' onClick={() => rowAction.detail(row.original)}>
                <i className='ri-eye-line text-textSecondary' />
              </IconButton>
            </Grid>
          </Grid>
        )
      }
    })
  ]
}

export const miscTableColumns = (rowAction: RowActionType) => {
  return [
    miscColumnHelper.accessor('number', {
      header: 'Kode Barang'
    }),
    miscColumnHelper.accessor('name', {
      header: 'NAMA ITEM'
    }),
    miscColumnHelper.accessor('brandName', {
      header: 'MERK ITEM'
    }),
    miscColumnHelper.accessor('largeUnitQuantity', {
      header: 'QTY',
      cell: ({ row }) => row.original.quantity + ' ' + row.original.quantityUnit
    }),
    miscColumnHelper.accessor('note', {
      header: 'KETERANGAN',
      cell: ({ row }) => (
        <Tooltip sx={{ cursor: 'default' }} title={row.original.note}>
          <Typography>{truncateString(row.original.note ?? '-', 10)}</Typography>
        </Tooltip>
      )
    }),
    miscColumnHelper.display({
      id: 'actions',
      header: 'ACTION',
      cell: ({ row }) => {
        return (
          <Grid container>
            {!rowAction.viewOnly && (
              <>
                <IconButton size='small' onClick={() => rowAction.delete(row.original.id)}>
                  <i className='x-delete-icon text-error' />
                </IconButton>
                <IconButton size='small' onClick={() => rowAction.edit(row.original)}>
                  <i className='ri-pencil-fill text-textSecondary' />
                </IconButton>
              </>
            )}
            <IconButton size='small' onClick={() => rowAction.detail(row.original)}>
              <i className='ri-eye-line text-textSecondary' />
            </IconButton>
          </Grid>
        )
      }
    })
  ]
}

export const StuffReqItemPayloadSchema = z.object({
  workOrderSegmentId: z.string(),
  itemId: z.string(),
  stuffRequestItemId: z.string().optional(),
  serialNumber: z.string().optional(),
  quantity: z.number(),
  quantityUnit: z.string(),
  largeUnitQuantity: z.number().optional(),
  note: z.string().optional()
})

export const StuffReqApprovalPayloadSchema = z.object({
  userId: z.string()
})

export const StuffReqPayloadSchema = z.object({
  workProcessId: z.string(),
  type: z.enum(['TAKE', 'RETURN']),
  items: z.array(StuffReqItemPayloadSchema).min(1),
  approvals: z.array(StuffReqApprovalPayloadSchema),
  note: z.string().optional()
})
