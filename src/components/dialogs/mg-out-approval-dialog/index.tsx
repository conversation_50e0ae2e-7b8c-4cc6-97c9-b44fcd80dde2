// React Imports
import { useEffect, useState } from 'react'

// MUI Imports
import Button from '@mui/material/Button'
import Dialog from '@mui/material/Dialog'
import DialogActions from '@mui/material/DialogActions'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import IconButton from '@mui/material/IconButton'
import Typography from '@mui/material/Typography'

import { Controller, FormProvider, useForm } from 'react-hook-form'

import { TextField } from '@mui/material'

import { useFilePicker } from 'use-file-picker'

import LoadingButton from '@mui/lab/LoadingButton'
import { ApprovalType } from '@/pages/material-goods/list-out-approval/config/type'
import { MrUserStatus } from '@/types/mrTypes'
import CameraModal from '@/components/camera/CameraModal'
import { FacingMode } from '@/components/camera/camera-types'
import { format } from 'date-fns'
import { useMg } from '@/pages/material-goods/list-out-approval/context/MgContext'

type MgOutApprovalProps = {
  open: boolean
  setOpen: (open: boolean) => void
  onSubmit: (formData?: ApprovalType) => void
  isLoading?: boolean
}

const MgOutApprovalDialog = ({ open, setOpen, onSubmit, isLoading }: MgOutApprovalProps) => {
  const { mgOutData } = useMg()
  const methods = useForm<ApprovalType>({
    defaultValues: {
      status: MrUserStatus.APPROVED
    }
  })

  const { setValue, handleSubmit, control } = methods

  const [cameraState, setCameraState] = useState({
    open: false,
    imageContent: ''
  })

  const handleClose = () => {
    setOpen(false)
  }

  useEffect(() => {
    if ((cameraState.imageContent?.length ?? 0) > 0) {
      setValue('takenImageFile', cameraState.imageContent)
      setValue('takenImageFileName', `BUKTI_PENGAMBILAN_${format(new Date(), 'ddMMyyyyHHmmss')}`)
    }
  }, [cameraState.imageContent])

  return (
    <FormProvider {...methods}>
      <Dialog fullWidth open={open} onClose={handleClose} maxWidth='md' scroll='paper'>
        <DialogTitle variant='h4' className='flex gap-2 flex-col items-center sm:pbs-16 sm:pbe-6 sm:px-16'>
          <div className='max-sm:is-[80%] max-sm:text-center'>Setujui Barang Keluar</div>
          <Typography component='span' className='flex flex-col text-center'>
            Masukkan data barang keluar dan pengambilan barang
          </Typography>
        </DialogTitle>
        <DialogContent className='overflow-visible pbs-0 sm:pbe-10 sm:px-16'>
          <IconButton onClick={handleClose} className='absolute block-start-4 inline-end-4'>
            <i className='ri-close-line text-textSecondary' />
          </IconButton>
          <div className='flex flex-col gap-4'>
            <Controller
              name='takenBy'
              rules={{ required: 'Nama pengambil barang wajib diisi' }}
              control={control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Diambil Oleh'
                  placeholder='Masukkan nama lengkap pengambil barang'
                  className='mt-2'
                  {...(error?.message && { error: true, helperText: error?.message })}
                />
              )}
            />
            <div className='flex flex-col'>
              <Typography variant='subtitle2' marginBottom={2}>
                Unggah Foto Bukti Pengambilan
              </Typography>
              <div className='flex items-center gap-4'>
                <TextField
                  size='small'
                  fullWidth
                  value={
                    cameraState.imageContent
                      ? `PENGAMBILAN_${mgOutData.number}_${format(new Date(), 'ddMMyyyyHHmmss')}.jpg`
                      : ''
                  }
                  placeholder='Tidak ada file dipilih'
                  aria-readonly
                  className='flex-1'
                />
                <Button
                  variant='contained'
                  onClick={() =>
                    setCameraState(current => ({
                      ...current,
                      open: true
                    }))
                  }
                >
                  Ambil Foto
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
        <DialogActions className='justify-center pbs-0 sm:pbe-10 sm:px-16'>
          <Button variant='outlined' color='secondary' type='reset' onClick={handleClose}>
            BATAL
          </Button>
          <LoadingButton
            startIcon={<></>}
            loadingPosition='start'
            variant='contained'
            loading={isLoading}
            onClick={handleSubmit(data => {
              if (!!data?.takenImageFile) {
                onSubmit(data)
              }
            })}
            type='submit'
            className='px-8'
          >
            SETUJUI
          </LoadingButton>
        </DialogActions>
      </Dialog>
      {cameraState.open && (
        <CameraModal
          open={cameraState.open}
          facingMode='environment'
          onClose={() =>
            setCameraState(current => ({
              ...current,
              open: false
            }))
          }
          onCaptured={imageData => {
            setCameraState(current => ({
              ...current,
              open: false,
              imageContent: imageData
            }))
          }}
        />
      )}
    </FormProvider>
  )
}

export default MgOutApprovalDialog
