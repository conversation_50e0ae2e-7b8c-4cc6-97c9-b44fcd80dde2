import Table from '@/components/table'
import { useWo, WoDocumentType } from '@/pages/repair-and-maintenance/wo/context/WoContext'
import { WoSegmentType } from '@/types/woTypes'
import { Button, Dialog, DialogActions, DialogContent, DialogTitle, Grid, IconButton, Typography } from '@mui/material'
import {
  createColumnHelper,
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { tableColumns } from './config'
import { useQuery } from '@tanstack/react-query'
import RnMQueryMethods from '@/api/services/rnm/query'
import { useEffect, useMemo, useState } from 'react'
import Mr<PERSON>ueryMethods, { MR_LIST_QUERY_KEY } from '@/api/services/mr/query'
import { MrType } from '@/types/mrTypes'
import { SR_LIST_QUERY_KEY } from '@/api/services/sr/query'
import { SrType } from '@/types/srTypes'
import PartSwapQueryMethods from '@/api/services/part-swap/query'
import { PART_SWAP_QUERY_LIST_KEY } from '@/api/services/part-swap/service'
import { PartSwapType } from '@/types/partSwapTypes'
import Permission from '@/core/components/Permission'

interface DetailDocumentComponentDialogProps {
  open: boolean
  setOpen: (value: React.SetStateAction<any>) => void
  onCreateDocument: (segment: WoSegmentType) => void
  handleClose: () => void
  segment: WoSegmentType
}

const DocumentDetailRnM = (props: DetailDocumentComponentDialogProps) => {
  const { navigate } = useWo()
  const { open, handleClose, onCreateDocument, setOpen, segment } = props

  const enabled = ['PROCESSED', 'CREATED'].includes(segment?.status)

  const { data: mrDocs } = useQuery({
    enabled: !!segment?.id,
    queryKey: [MR_LIST_QUERY_KEY, segment?.id],
    queryFn: async () => {
      const res = await MrQueryMethods.getMrList({
        page: 1,
        limit: Number.MAX_SAFE_INTEGER,
        workOrderSegmentId: segment?.id
      })
      return res.items ?? []
    },
    placeholderData: [] as MrType[]
  })

  const { data: srDocs } = useQuery({
    enabled: !!segment?.id,
    queryKey: [SR_LIST_QUERY_KEY, segment?.id],
    queryFn: async () => {
      const res = await RnMQueryMethods.getSrList({
        page: 1,
        limit: Number.MAX_SAFE_INTEGER,
        workOrderSegmentId: segment?.id
      })
      return res.items ?? []
    },
    placeholderData: [] as SrType[]
  })

  const { data: partSwapList } = useQuery({
    enabled: !!segment?.id,
    queryKey: [PART_SWAP_QUERY_LIST_KEY, segment?.id],
    queryFn: async () => {
      const res = await PartSwapQueryMethods.getPartSwapList({
        workOrderSegmentId: segment?.id
      })
      return res.items ?? []
    },
    placeholderData: [] as PartSwapType[]
  })

  const tableOptions = useMemo(
    () => ({
      data: [...(mrDocs ?? []), ...(srDocs ?? []), ...(partSwapList ?? [])],
      columns: tableColumns({
        detail: item => {
          switch (true) {
            case item.number.startsWith('MR'):
              navigate(`/mr/list/${item.id}`)
              break
            case item.number.startsWith('SR'):
              navigate(`/service-request/list/${item.id}`)
              break
            case item.number.startsWith('PS'):
              navigate(`/part-swap/list/${item.id}`)
              break
            default:
              break
          }
        }
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [mrDocs, srDocs]
  )

  const table = useReactTable<any>(tableOptions)

  return (
    <Dialog maxWidth='lg' open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Dokumen Terbuat
        <Typography component='span' className='flex flex-col text-center'>
          Lihat list dokumen yang dibuat untuk segment ini
        </Typography>
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        <IconButton onClick={() => setOpen(null)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <div className='rounded-md shadow-sm'>
          <Table
            table={table}
            emptyLabel={
              <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-64'>
                <Typography> Belum ada Dokument</Typography>
                <Typography className='text-sm text-gray-400'>
                  Semua Dokumen yang telah kamu buat untuk segment ini akan ditampilkan di sini
                </Typography>
              </td>
            }
          />
        </div>
      </DialogContent>
      <DialogActions className='flex items-center justify-center my-2'>
        <Permission permission={['material-request.create', 'service-requisition.create', 'part-swap.create']}>
          <Button disabled={!enabled} onClick={() => onCreateDocument(segment)} variant='contained'>
            Buat Dokumen
          </Button>
        </Permission>
      </DialogActions>
    </Dialog>
  )
}

export default DocumentDetailRnM
