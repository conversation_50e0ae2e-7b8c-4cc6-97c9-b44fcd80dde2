import { Chip, Grid, Icon<PERSON>utton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { MrType } from '@/types/mrTypes'
import { SrType } from '@/types/srTypes'
import truncateString from '@/core/utils/truncate'
import { getStatusConfig } from '@/pages/repair-and-maintenance/wo/fr-list/config/utils'
import { PartSwapType } from '@/types/partSwapTypes'

type RowAction = {
  detail: (id: DocType) => void
}

type DocType = MrType | SrType | PartSwapType

const columnHelper = createColumnHelper<DocType>()

export const tableColumns = (rowAction: RowAction) => [
  columnHelper.accessor('number', {
    header: 'NO DOKUMEN',
    cell: ({ row }) => (
      <Typography
        role='button'
        onClick={() => rowAction.detail(row.original)}
        color='primary'
        sx={{ cursor: 'pointer' }}
      >
        {row.original.number}
      </Typography>
    )
  }),
  columnHelper.accessor('status', {
    header: 'STATUS',
    cell: ({ row }) => (
      <Chip
        size='small'
        variant='tonal'
        label={getStatusConfig(row.original.status)?.label}
        color={getStatusConfig(row.original.status)?.color as any}
      />
    )
  }),
  columnHelper.display({
    id: 'docType',
    header: 'JENIS DOKUMEN',
    cell: ({ row }) => {
      switch (true) {
        case row.original.number?.startsWith('MR'):
          return 'Material Request'
        case row.original.number?.startsWith('SR'):
          return 'Service Request'
        case row.original.number?.startsWith('PS'):
          return 'Part Swap'
        default:
          return '-'
      }
    }
  }),
  columnHelper.accessor('createdAt', {
    header: 'TANGGAL DIBUAT',
    cell: ({ row }) => formatDate(new Date(row.original.createdAt), 'eeee, dd/MM/yyyy', { locale: id })
  }),
  columnHelper.display({
    id: 'actions',
    header: 'ACTION',
    cell: ({ row }) => (
      <Grid container spacing={1}>
        <IconButton onClick={() => rowAction.detail(row.original)}>
          <i className='ri-eye-line text-textSecondary' />
        </IconButton>
      </Grid>
    )
  })
]
