import { CompanySiteType, ImageItemType, ItemType } from '@/types/companyTypes'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Autocomplete,
  Button,
  CircularProgress,
  debounce,
  Dialog,
  DialogActions,
  DialogContent,
  DialogProps,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { useEffect, useLayoutEffect, useState } from 'react'
import { defaultImageList } from '../add-item-dialog'
import PhotoPicker from '@/components/PhotoPicker'
import CompanyQueryMethods, {
  ITEM_LIST_QUERY_KEY,
  ITEM_QUERY_KEY,
  SERIAL_NUMBER_LIST_QUERY_KEY
} from '@/api/services/company/query'
import { useQuery } from '@tanstack/react-query'
import { Controller, useForm } from 'react-hook-form'
import { ItemType as FormItemType, itemSchema } from '@/pages/repair-and-maintenance/wo/create-wo/config/schema'
import { zodResolver } from '@hookform/resolvers/zod'
import NumberField from '@/components/numeric/NumberField'
import { useUploadImage } from '@/api/services/file/mutation'
import { SerialNumberType } from '@/types/serialNumber'
import { mergeArrays } from '@/utils/helper'
import { useAuth } from '@/contexts/AuthContext'

type AddMiscSegmentDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  onClose?: () => void
  onSuccessfullAdd?: (data: AddItemDto) => void
  onSuccessfullUpdate?: (data: AddItemDto) => void
  selectedItem?: ItemType
  edit?: boolean
}

export type AddItemDto = {
  parentCode: string
  number: string
  externalCode: string
  name: string
  category: string
  brandName: string
} & FormItemType

const DialogAddMiscSegmentWo = (props: AddMiscSegmentDialogProps) => {
  const { open, setOpen } = props
  const { userProfile } = useAuth()

  const [imageList, setImageList] = useState<ImageItemType[]>(defaultImageList)
  const [itemSearchQuery, setItemSearchQuery] = useState<string>('')
  const [selectedItem, setSelectedItem] = useState<ItemType | null>(props?.selectedItem ?? null)

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    props?.onClose?.()
    setSelectedItem(null)
    setOpen(false)
  }

  const { control, handleSubmit, reset, getValues } = useForm<AddItemDto>({
    defaultValues: {
      parentCode: selectedItem?.parentCode || '',
      number: selectedItem?.number || '',
      externalCode: selectedItem?.vendorNumber || '',
      name: selectedItem?.name || '',
      category: selectedItem?.category?.name || '',
      brandName: selectedItem?.brandName || '',
      note: selectedItem?.description || '',
      largeUnitQuantity: selectedItem?.largeUnitQuantity || 0,
      quantity: selectedItem?.quantity || 0,
      quantityUnit: selectedItem?.quantityUnit || '',
      type: 'MISCELLANEOUS',
      images: imageList?.map(img => ({ filename: img.fileName }))
    },
    resolver: zodResolver(itemSchema)
  })

  useQuery({
    enabled: !!selectedItem?.itemId && !!props.edit,
    queryKey: [ITEM_QUERY_KEY, selectedItem?.itemId],
    queryFn: async () => {
      const res = await CompanyQueryMethods.getItem(selectedItem?.itemId)
      setSelectedItem(curr => ({ ...curr, largeUnit: res.largeUnit, smallUnit: res.smallUnit }))
    }
  })

  const { data: snList } = useQuery({
    enabled: !!selectedItem?.id,
    queryKey: [SERIAL_NUMBER_LIST_QUERY_KEY, selectedItem?.id],
    queryFn: async () => {
      const res = await CompanyQueryMethods.getSerialNumberList({
        itemId: selectedItem?.id,
        status: 'IN_STOCK'
      })
      return res.items
    },
    placeholderData: [] as SerialNumberType[]
  })

  useEffect(() => {
    if (snList.length > 0) {
      reset({
        ...getValues(),
        serialNumber: `${snList[0].number}`
      })
    }
  }, [snList])

  const {
    data: itemList,
    isLoading: fetchItemsLoading,
    remove: removeItemList
  } = useQuery({
    enabled: !!itemSearchQuery,
    queryKey: [ITEM_LIST_QUERY_KEY, itemSearchQuery],
    queryFn: async () => {
      const res = await CompanyQueryMethods.getItemList({
        page: 1,
        limit: Number.MAX_SAFE_INTEGER,
        search: itemSearchQuery,
        isInStock: true,
        siteIds: userProfile?.sites
          ?.filter(site => site?.type === CompanySiteType.WORKSHOP)
          ?.map(site => site?.id)
          .join(',')
      })
      return res?.items || []
    },
    placeholderData: [] as ItemType[]
  })

  const { mutateAsync: uploadMutate, isLoading: uploadLoading } = useUploadImage()

  const onSubmitItem = (dto: AddItemDto) => {
    Promise.all(
      imageList
        .filter(item => !!item.fileName)
        .map(item =>
          uploadMutate({
            fieldName: `item_image_${dto.number}`,
            file: item.content,
            scope: 'public-image',
            fileName: item.fileName
          })
        )
    ).then(values => {
      const uploadIds = values.map(val => ({
        url: val.data?.url ?? '',
        uploadId: val.data?.id ?? ''
      }))
      const retData = {
        ...getValues(),
        itemId: dto.itemId,
        type: dto.type,
        serialNumber: dto.serialNumber,
        quantity: dto.quantity,
        quantityUnit: dto.quantityUnit,
        largeUnitQuantity: dto.largeUnitQuantity,
        note: dto.note,
        images: uploadIds.map(id => ({ uploadId: id.uploadId, url: id.url }))
      }
      if (props.edit) {
        props.onSuccessfullUpdate?.({
          ...retData,
          images: mergeArrays(props.selectedItem.images, retData.images, 'uploadId')
        })
      } else {
        props?.onSuccessfullAdd?.(retData)
      }
      setSelectedItem(null)
    })
  }

  useEffect(() => {
    if (props.selectedItem) {
      const { id, serialNumber } = props.selectedItem
      reset({ ...getValues(), itemId: props.selectedItem?.itemId ?? id, serialNumber: serialNumber })
      if (props.selectedItem.images.length > 0) {
        setImageList([
          ...props.selectedItem.images.map(image => ({
            content: image.url,
            uploadId: image.uploadId
          })),
          ...(new Array(5 - (props.selectedItem.images?.length ?? 0)).fill({
            content: '',
            fileName: ''
          }) as ImageItemType[])
        ])
      }
    }
  }, [props.selectedItem])

  useEffect(() => {
    if (!open) reset()
  }, [open])

  return (
    <Dialog maxWidth='lg' open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        {!props.edit ? 'Tambah' : 'Ubah'} Miscellaneous
        {!props.edit && (
          <Typography component='span' className='flex flex-col text-center'>
            Masukkan data item lain-lain yang akan ditambahkan ke Segment
          </Typography>
        )}
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        <IconButton
          onClick={() => {
            setOpen(false)
            props?.onClose?.()
          }}
          className='absolute block-start-4 inline-end-4'
        >
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <Typography>Detil Barang</Typography>
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='itemId'
              render={({ field: { onChange }, fieldState: { error } }) => (
                <Autocomplete
                  value={selectedItem}
                  onInputChange={debounce((e, newValue, reason) => {
                    if (reason === 'input') {
                      setItemSearchQuery(newValue)
                    }
                  }, 700)}
                  options={itemList || []}
                  getOptionLabel={(option: ItemType) => `${option.number} | ${option.name} | ${option.brandName}`}
                  freeSolo={!itemSearchQuery}
                  noOptionsText='Barang tidak ditemukan'
                  onChange={(e, newValue: ItemType) => {
                    onChange(newValue.id)
                    setSelectedItem(newValue)
                    reset({
                      ...getValues(),
                      parentCode: newValue?.parentCode || '',
                      number: newValue?.number || '',
                      externalCode: newValue?.vendorNumber || '',
                      name: newValue?.name || '',
                      category: newValue?.category?.name || '',
                      brandName: newValue?.brandName || '',
                      largeUnitQuantity: newValue?.largeUnitQuantity || 0
                    })
                    removeItemList()
                  }}
                  renderInput={params => (
                    <TextField
                      {...params}
                      InputProps={{
                        ...params.InputProps,
                        startAdornment: <i className='ri-search-line text-textSecondary size-5 mx-2' />,
                        endAdornment: <>{fetchItemsLoading ? <CircularProgress /> : null}</>,
                        onKeyDown: e => {
                          if (e.key === 'Enter') {
                            e.stopPropagation()
                          }
                        }
                      }}
                      placeholder='Cari Kode barang, kode eksternal, nama barang, atau merk barang'
                      error={!!error}
                    />
                  )}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Controller
              control={control}
              name='parentCode'
              render={({ field }) => <TextField value={field.value} fullWidth label='Kode Induk' />}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Controller
              control={control}
              name='number'
              render={({ field }) => <TextField value={field.value} fullWidth label='Kode Barang' />}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Controller
              control={control}
              name='externalCode'
              render={({ field }) => <TextField value={field.value} fullWidth label='Kode Eksternal' />}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Controller
              control={control}
              name='name'
              render={({ field }) => <TextField value={field.value} fullWidth label='Nama Item' />}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Controller
              control={control}
              name='category'
              render={({ field }) => <TextField value={field.value} fullWidth label='Kategori Item' />}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Controller
              control={control}
              name='brandName'
              render={({ field }) => <TextField value={field.value} fullWidth label='Merk Item' />}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Controller
              control={control}
              name='quantity'
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Quantity'
                  InputProps={{ inputComponent: NumberField as any }}
                  error={!!error}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Controller
              control={control}
              name='quantityUnit'
              render={({ field, fieldState: { error } }) => (
                <FormControl fullWidth>
                  <InputLabel>Satuan</InputLabel>
                  <Select {...field} label='Satuan' className='bg-white' error={!!error}>
                    {[selectedItem?.largeUnit, selectedItem?.smallUnit].map(role => (
                      <MenuItem key={role} value={role}>
                        {role}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button
          onClick={() => {
            setOpen(false)
            props?.onClose?.()
          }}
          variant='outlined'
          className='is-full sm:is-auto'
        >
          BATALKAN
        </Button>
        <LoadingButton
          startIcon={<></>}
          loading={uploadLoading}
          loadingPosition='start'
          variant='contained'
          onClick={handleSubmit(onSubmitItem)}
          className='px-8 is-full !ml-0 sm:is-auto'
        >
          {props.edit ? 'UPDATE' : 'TAMBAHKAN'}
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default DialogAddMiscSegmentWo
