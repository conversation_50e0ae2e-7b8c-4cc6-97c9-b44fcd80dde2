// MUI Imports

import Dialog, { DialogProps } from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import DialogActions from '@mui/material/DialogActions'
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import Button from '@mui/material/Button'
import { Checkbox, Grid } from '@mui/material'
import { usePrList } from '@/pages/purchase-order/context/PrListContext'
import { useState } from 'react'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { useRouter } from '@/routes/hooks'

type SelectItemDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
}

const SelectItemDialog = ({ open, setOpen }: SelectItemDialogProps) => {
  const router = useRouter()
  const { setConfirmState } = useMenu()
  const { prData } = usePrList()
  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  const [selectedItems, setSelectedItems] = useState([])

  const unclosedItems = prData?.items
    ?.filter(item => !item.isClosed)
    .reduce((acc, item) => {
      if (!acc[item.item?.vendorId ?? 'noVendor']) {
        acc[item.item?.vendorId ?? 'noVendor'] = []
      }
      acc[item.item?.vendorId ?? 'noVendor'].push(item)
      return acc
    }, {})

  const handleSubmit = () => {
    router.push(`/po/pr-list/${prData?.id}/po/create?items=${selectedItems.map(item => item.id).join(',')}`)
  }

  return (
    <Dialog fullWidth open={open} onClose={handleClose} maxWidth='md'>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Pilih Barang
        <Typography component='span' className='flex flex-col text-center'>
          Buat PO menggunakan list barang dibawah. Pastikan kamu memilih barang-barang dari vendor yang sama
        </Typography>
      </DialogTitle>
      <DialogContent className='overflow-auto pbs-0 sm:pbe-16 sm:px-12 max-h-[80vh]'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <div className='flex flex-col gap-4'>
          {Object.keys(unclosedItems).map(vendorId => (
            <div key={vendorId} className='flex flex-col border-gray-200 border-2 rounded-2xl p-4 gap-4'>
              <div className='flex justify-between w-full'>
                <div className='flex flex-col justify-center gap-1'>
                  <Typography className='text-base'>
                    {unclosedItems[vendorId][0].item?.vendor?.name ?? 'Tanpa Vendor'}
                  </Typography>
                  {unclosedItems[vendorId][0].item?.vendor ? (
                    <Typography className='caption text-gray-400 text-xs'>
                      Kode Vendor {unclosedItems[vendorId][0].item?.vendor?.code}
                    </Typography>
                  ) : null}
                </div>
                <Checkbox
                  onChange={e => {
                    if (e.target.checked) {
                      setSelectedItems(current => [...current, ...unclosedItems[vendorId]])
                    } else {
                      setSelectedItems(current =>
                        current.filter(item => !unclosedItems[vendorId].some(i => i.id === item.id))
                      )
                    }
                  }}
                  indeterminate={
                    selectedItems.length > 0 &&
                    selectedItems.filter(item => unclosedItems[vendorId].some(i => i.id === item.id)).length > 0 &&
                    selectedItems.filter(item => unclosedItems[vendorId].some(i => i.id === item.id)).length <
                      unclosedItems[vendorId].length
                  }
                  checked={
                    selectedItems.length > 0 &&
                    selectedItems.filter(item => unclosedItems[vendorId].some(i => i.id === item.id)).length > 0 &&
                    selectedItems.filter(item => unclosedItems[vendorId].some(i => i.id === item.id)).length ===
                      unclosedItems[vendorId].length
                  }
                  disabled={
                    !!unclosedItems[vendorId][0].item?.vendorId &&
                    selectedItems.length > 0 &&
                    !selectedItems.every(
                      item => !item.item?.vendorId || item.item?.vendorId === unclosedItems[vendorId][0].item?.vendorId
                    )
                  }
                  className='mr-[14px]'
                />
              </div>
              {unclosedItems[vendorId].map(prItem => {
                const {
                  quantity,
                  quantityUnit,
                  unit,
                  item: { name, brandName, number },
                  isLargeUnit,
                  remainingQuantity,
                  largeUnitQuantity
                } = prItem
                const isChecked = selectedItems.some(item => item.id === prItem.id)
                const remainingQty = isLargeUnit ? remainingQuantity / largeUnitQuantity : remainingQuantity
                return (
                  <div className='flex flex-col gap-3 overflow-hidden flex-wrap items-start p-4 w-full rounded-lg bg-gray-600 bg-opacity-10 max-md:max-w-full'>
                    <div className='flex gap-10 items-center justify-between flex-wrap w-full'>
                      <div className='flex flex-col gap-2 text-base tracking-normal leading-none min-w-[240px] text-gray-600 text-opacity-60'>
                        <div className='font-medium text-gray-600 text-opacity-90'>
                          {name} - {brandName} {number}
                        </div>
                        {unit ? (
                          <>
                            <div>
                              {unit?.brandName} {unit?.type}
                            </div>
                            <div className='text-sm'>
                              {unit?.number} - No. Lambung {unit?.hullNumber}
                            </div>
                          </>
                        ) : null}
                        <div className='text-sm text-green-700'>
                          Qty Sisa: {remainingQty} {quantityUnit}
                        </div>
                      </div>
                      <Checkbox
                        id={prItem.id}
                        checked={isChecked}
                        disabled={
                          !!prItem.item?.vendorId &&
                          selectedItems.length > 0 &&
                          !selectedItems.every(
                            item => !item.item?.vendorId || item.item?.vendorId === prItem.item?.vendorId
                          )
                        }
                        onChange={e => {
                          if (e.target.checked) {
                            setSelectedItems(current => [...current, prItem])
                          } else {
                            setSelectedItems(current => current.filter(item => item.id !== prItem.id))
                          }
                        }}
                      />
                    </div>
                  </div>
                )
              })}
            </div>
          ))}
        </div>
      </DialogContent>
      <DialogActions className='max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16 gap-2 sm:gap-4'>
        <Button onClick={() => setOpen(false)} variant='outlined' className='is-full sm:is-auto'>
          BATAL
        </Button>
        <Button
          variant='contained'
          disabled={selectedItems.length === 0}
          onClick={handleSubmit}
          className='px-8 !ml-0 is-full sm:is-auto'
        >
          PILIH BARANG
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default SelectItemDialog
