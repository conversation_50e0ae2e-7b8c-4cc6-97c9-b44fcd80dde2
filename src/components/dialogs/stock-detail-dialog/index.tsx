// MUI Imports

import Dialog, { DialogProps } from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import { IconButton, Typography } from '@mui/material'
import { MrItemType } from '@/types/mrTypes'
import CompanyQueryMethods, { SITE_LIST_QUERY_KEY } from '@/api/services/company/query'
import { useQuery } from '@tanstack/react-query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { SiteType } from '@/types/companyTypes'

type StockDetailDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  mrItem?: MrItemType
}

const StockDetailDialog = ({ open, setOpen, mrItem }: StockDetailDialogProps) => {
  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  const {
    data: { items: siteList }
  } = useQuery({
    queryKey: [SITE_LIST_QUERY_KEY],
    queryFn: () => {
      return CompanyQueryMethods.getSiteList({
        limit: Number.MAX_SAFE_INTEGER
      })
    },
    placeholderData: defaultListData as ListResponse<SiteType>
  })

  return (
    <Dialog open={open} onClose={handleClose} fullWidth maxWidth='sm' scroll='body'>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Cek Stok
        <Typography align='center'>Cek ketersediaan stok barang ini di gudang</Typography>
      </DialogTitle>
      <DialogContent className='overflow-visible pbs-0 p-8'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <div className='flex flex-col gap-4'>
          <div className='flex flex-col gap-2 items-start w-full p-4 rounded-md bg-gray-500 bg-opacity-10'>
            <Typography variant='h5'>
              {mrItem?.item?.name} - {mrItem?.item?.brandName}
            </Typography>
            <Typography color='grey'>Kode Barang {mrItem?.item?.number}</Typography>
          </div>
          <Typography>Ketersediaan Stok</Typography>
          {mrItem?.item?.stocks?.map(stock => (
            <div className='flex justify-between w-full'>
              <Typography>{siteList.find(site => site.id === stock.siteId)?.name ?? 'Site Tidak Ditemukan'}</Typography>
              <Typography>
                {stock.stock} {mrItem?.item?.smallUnit}
              </Typography>
            </div>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default StockDetailDialog
