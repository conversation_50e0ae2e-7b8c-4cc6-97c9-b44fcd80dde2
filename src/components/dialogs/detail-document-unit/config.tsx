import { <PERSON>rid, <PERSON><PERSON><PERSON>utton, Tooltip, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import truncateString from '@/core/utils/truncate'
import { UnitLogType } from '@/types/companyTypes'

export enum UnitLogDocumentType {
  MaterialRequest = 'material-request',
  FieldReport = 'field-report',
  WorkOrder = 'work-order',
  PartSwap = 'part-swap'
}

type RowAction = {
  detail: (type: string, id: string) => void
}

const columnHelper = createColumnHelper<UnitLogType>()

export const tableColumns = (rowAction: RowAction) => [
  columnHelper.accessor('description', {
    header: 'NO DOKUMEN',
    cell: ({ row }) => {
      let docNumber = row.original.description
      let docId = ''
      let docType = ''
      const description = row.original.description
      if (description.includes('#')) {
        docType = description.split('#')[1]
        docId = description.split('#')[2]
        docNumber = description.split('#')[3]
      }
      return (
        <Tooltip title={row.original.description} placement='bottom'>
          <Typography
            role='button'
            onClick={() => rowAction.detail(docType, docId)}
            color='primary'
            sx={{ cursor: docType ? 'pointer' : 'default' }}
          >
            {truncateString(docNumber, 14)}
          </Typography>
        </Tooltip>
      )
    }
  }),
  columnHelper.display({
    id: 'kmBefore',
    header: 'KM SEBELUM',
    cell: ({ row }) => (row.original.kmBefore ? `${row.original.kmBefore} km` : '-')
  }),
  columnHelper.display({
    id: 'kmAfter',
    header: 'KM SESUDAH',
    cell: ({ row }) => (row.original.kmAfter ? `${row.original.kmAfter} km` : '-')
  }),
  columnHelper.display({
    id: 'hmBefore',
    header: 'HM SEBELUM',
    cell: ({ row }) => (row.original.hmBefore ? `${row.original.hmBefore} jam` : '-')
  }),
  columnHelper.display({
    id: 'hmAfter',
    header: 'HM SESUDAH',
    cell: ({ row }) => (row.original.hmAfter ? `${row.original.hmAfter} jam` : '-')
  }),
  columnHelper.accessor('createdAt', {
    header: 'TANGGAL DIBUAT',
    cell: ({ row }) => formatDate(new Date(row.original.createdAt), 'eeee, dd/MM/yyyy', { locale: id })
  }),
  columnHelper.display({
    id: 'actions',
    header: 'ACTION',
    cell: ({ row }) => {
      let docId = ''
      let docType = ''
      const description = row.original.description
      if (description.includes('#')) {
        docType = description.split('#')[1]
        docId = description.split('#')[2]
      }
      return (
        <Grid container spacing={1}>
          {docType && (
            <IconButton onClick={() => rowAction.detail(docType, docId)}>
              <i className='ri-eye-line text-textSecondary' />
            </IconButton>
          )}
        </Grid>
      )
    }
  })
]
