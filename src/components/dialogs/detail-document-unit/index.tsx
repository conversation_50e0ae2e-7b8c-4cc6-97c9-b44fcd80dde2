import Table from '@/components/table'
import { useWo } from '@/pages/repair-and-maintenance/wo/context/WoContext'
import { Dialog, DialogContent, DialogTitle, IconButton, Typography } from '@mui/material'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { tableColumns, UnitLogDocumentType } from './config'
import { useQuery } from '@tanstack/react-query'
import { useMemo } from 'react'
import { CompanyUnitType } from '@/pages/repair-and-maintenance/unit/context/UnitContext'
import CompanyQueryMethods, { UNIT_LOG_LIST_QUERY_KEY } from '@/api/services/company/query'
import { UnitLogType } from '@/types/companyTypes'
import { useRouter } from '@/routes/hooks'

interface DetailDocumentComponentDialogProps {
  open: boolean
  setOpen: (value: React.SetStateAction<any>) => void
  handleClose: () => void
  unit: CompanyUnitType
}

const DocumentDetailUnit = (props: DetailDocumentComponentDialogProps) => {
  const { open, handleClose, setOpen, unit } = props
  const router = useRouter()

  const { data: unitLogs } = useQuery({
    enabled: !!unit?.id,
    queryKey: [UNIT_LOG_LIST_QUERY_KEY, unit?.id],
    queryFn: async () => {
      const res = await CompanyQueryMethods.getUnitLogs(unit?.id)
      return res.items ?? []
    },
    placeholderData: [] as UnitLogType[]
  })

  const tableOptions = useMemo(
    () => ({
      data: unitLogs ?? [],
      columns: tableColumns({
        detail: (type, id) => {
          if (type === UnitLogDocumentType.MaterialRequest) {
            router.push(`/mr/list/${id}`)
          }
          if (type === UnitLogDocumentType.FieldReport) {
            router.push(`/fr/created/${id}`)
          }
          if (type === UnitLogDocumentType.WorkOrder) {
            router.push(`/wo/created/${id}`)
          }
          if (type === UnitLogDocumentType.PartSwap) {
            router.push(`/part-swap/list/${id}`)
          }
        }
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [unitLogs]
  )

  const table = useReactTable<any>(tableOptions)

  return (
    <Dialog maxWidth='lg' open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Riwayat Data Unit
        <Typography component='span' className='flex flex-col text-center'>
          Lihat riwayat perubahan data unit ini
        </Typography>
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        <IconButton onClick={() => setOpen(null)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <div className='rounded-md shadow-sm'>
          <Table
            table={table}
            emptyLabel={
              <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-64'>
                <Typography>Belum ada Dokumen</Typography>
                <Typography className='text-sm text-gray-400'>
                  Semua Dokumen perubahan data unit ini akan ditampilkan di sini
                </Typography>
              </td>
            }
          />
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default DocumentDetailUnit
