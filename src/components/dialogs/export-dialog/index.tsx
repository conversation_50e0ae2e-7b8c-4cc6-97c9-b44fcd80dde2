import { Button, Typography } from '@mui/material'
import MessageDialog from '../message-dialog'
import { Link } from 'react-router-dom'

interface ExportDialogProps {
  open: boolean
  onClose?: () => void
  onConfirm?: () => void
  contentScope: string
}

const ExportDialog = ({ open, onClose, onConfirm, contentScope }: ExportDialogProps) => {
  const handleConfirm = () => {
    onConfirm && onConfirm()
    onClose && onClose()
  }

  return (
    <MessageDialog
      open={open}
      onClose={onClose}
      onConfirm={handleConfirm}
      title='Ekspor Data'
      content={
        <span>
          Mohon menunggu, sistem sedang memproses ekspor data <span className='font-bold'>{contentScope}</span>. Cek dan
          unduh hasilnya di halaman{' '}
          <Link to='/data-export'>
            <Button variant='text' className='p-0'>
              List Ekspor Data
            </Button>
          </Link>
          .
        </span>
      }
      confirmText='<PERSON><PERSON>, <PERSON><PERSON><PERSON>'
    />
  )
}

export default ExportDialog
