// MUI Imports

import Dialog, { DialogProps } from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import DialogActions from '@mui/material/DialogActions'
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import { Grid } from '@mui/material'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'

import { object, string, TypeOf } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from 'react-toastify'
import LoadingButton from '@mui/lab/LoadingButton'
import { useMt } from '@/pages/material-transfer/context/MtContext'
import { useCancelMt } from '@/api/services/mt/mutation'

type CancelMtDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
}

const cancelMtSchema = object({
  cancelationNote: string({ message: 'Wajib diisi' }).min(1, { message: 'Wajib diisi' })
})

type CancelMtInput = Required<TypeOf<typeof cancelMtSchema>>

const CancelMtDialog = ({ open, setOpen }: CancelMtDialogProps) => {
  const { mtData, refreshData } = useMt()
  const { control, handleSubmit } = useForm<CancelMtInput>({
    resolver: zodResolver(cancelMtSchema)
  })

  const { mutate: cancelMtMutate, isLoading: cancelMtLoading } = useCancelMt()

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  const onSubmitHandler: SubmitHandler<CancelMtInput> = (inputValues: CancelMtInput) => {
    cancelMtMutate(
      {
        mtId: mtData?.id,
        cancelationNote: inputValues.cancelationNote
      },
      {
        onSuccess: () => {
          refreshData()
          toast.success('Material Transfer berhasil dibatalkan')
          setOpen(false)
        }
      }
    )
  }

  return (
    <Dialog open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Batalkan MT
        <Typography component='span' className='flex flex-col text-center'>
          Masukkan alasan pembatalan pembuatan Material Transfer. Action ini tidak dapat diubah lagi
        </Typography>
      </DialogTitle>
      <DialogContent className='overflow-visible pbs-0 sm:pbe-16 sm:px-12'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          <Grid item xs={12}>
            <Controller
              name='cancelationNote'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Alasan Pembatalan'
                  required
                  variant='outlined'
                  placeholder='Masukkkan Alasan Pembatalan'
                  disabled={cancelMtLoading}
                  {...(errors.cancelationNote && { error: true, helperText: errors.cancelationNote.message })}
                />
              )}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className='max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16 flex-col sm:flex-row max-sm:gap-2'>
        <Button
          onClick={() => setOpen(false)}
          variant='outlined'
          disabled={cancelMtLoading}
          className='is-full sm:is-auto'
        >
          BATAL
        </Button>
        <LoadingButton
          startIcon={<></>}
          loading={cancelMtLoading}
          loadingPosition='start'
          variant='contained'
          color='error'
          onClick={handleSubmit(onSubmitHandler)}
          className='px-8 max-sm:!ml-0 is-full sm:is-auto'
        >
          BATALKAN MT
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default CancelMtDialog
