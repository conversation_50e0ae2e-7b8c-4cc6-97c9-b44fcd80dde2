// MUI Imports

import Dialog, { DialogProps } from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import DialogActions from '@mui/material/DialogActions'
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import { Grid } from '@mui/material'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'

import { object, string, TypeOf } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from 'react-toastify'
import { useAddDepartment, useUpdateDepartment } from '@/api/services/company/mutation'
import LoadingButton from '@mui/lab/LoadingButton'
import { useDepartment } from '@/pages/company-data/department/context/DepartmentContext'

type AddDepartmentDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
}

const addDepartmentSchema = object({
  code: string({ message: 'Wajib diisi' }),
  name: string({ message: 'Wajib diisi' })
})

type AddDepartmentInput = Required<TypeOf<typeof addDepartmentSchema>>

const AddDepartmentDialog = ({ open, setOpen }: AddDepartmentDialogProps) => {
  const { departmentData, fetchDepartmentList, fetchDepartmentData } = useDepartment()
  const { control, handleSubmit } = useForm<AddDepartmentInput>({
    resolver: zodResolver(addDepartmentSchema),
    defaultValues: {
      code: departmentData?.code,
      name: departmentData?.name
    }
  })

  const { mutate: addMutate, isLoading: addLoading } = useAddDepartment()
  const { mutate: updateMutate, isLoading: updateLoading } = useUpdateDepartment()

  const isLoading = addLoading || updateLoading

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  const onSubmitHandler: SubmitHandler<AddDepartmentInput> = (inputValues: AddDepartmentInput) => {
    if (departmentData) {
      updateMutate(
        {
          departmentId: departmentData.id,
          ...inputValues
        },
        {
          onSuccess: () => {
            toast.success('Data departemen berhasil diubah')
            fetchDepartmentData()
            fetchDepartmentList()
            setOpen(false)
          }
        }
      )
    } else {
      addMutate(inputValues, {
        onSuccess: () => {
          toast.success('Data departemen berhasil ditambahkan')
          fetchDepartmentList()
          setOpen(false)
        }
      })
    }
  }

  return (
    <Dialog PaperProps={{ className: 'rounded-t-[16px] md:rounded-t-none' }} open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        {departmentData ? 'Ubah' : 'Tambah'} Departemen
        {!departmentData && (
          <Typography component='span' className='flex flex-col text-center'>
            Tambahkan departemen untuk didaftarkan ke list
          </Typography>
        )}
      </DialogTitle>
      <DialogContent className='overflow-visible pbs-0 sm:pbe-16 sm:px-12'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          <Grid item xs={12} sm={6}>
            <Controller
              name='code'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Kode Departemen'
                  required
                  variant='outlined'
                  placeholder='Masukkkan Kode Departemen'
                  disabled={isLoading}
                  {...(errors.code && { error: true, helperText: errors.code?.message })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <Controller
              name='name'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Nama Departemen'
                  required
                  variant='outlined'
                  disabled={isLoading}
                  placeholder='Masukkkan Nama Departemen'
                  {...(errors.name && { error: true, helperText: errors.name?.message })}
                />
              )}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button onClick={() => setOpen(false)} variant='outlined' disabled={isLoading} className='is-full sm:is-auto'>
          BATAL
        </Button>
        <LoadingButton
          startIcon={<></>}
          loading={isLoading}
          loadingPosition='start'
          variant='contained'
          onClick={handleSubmit(onSubmitHandler)}
          className='px-8 is-full !ml-0 sm:is-auto'
        >
          {departmentData ? 'UBAH DATA' : 'TAMBAHKAN'}
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default AddDepartmentDialog
