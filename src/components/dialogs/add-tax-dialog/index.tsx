import { zodResolver } from '@hookform/resolvers/zod'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Autocomplete,
  Button,
  debounce,
  Dialog,
  DialogActions,
  DialogContent,
  DialogProps,
  DialogTitle,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { useEffect, useState } from 'react'
import { Controller, SubmitHandler, useForm, useWatch } from 'react-hook-form'
import { toast } from 'react-toastify'
import { TypeOf, z } from 'zod'
import { useQuery } from '@tanstack/react-query'
import AccountsQueryMethods, { ACCOUNT_LIST_QUERY_KEY, ACCOUNT_QUERY_KEY } from '@/api/services/account/query'
import { AccountType } from '@/types/accountTypes'
import { isNullOrUndefined } from '@/utils/helper'
import { TaxType } from '@/types/taxTypes'
import { useCreateTax, useUpdateTax } from '@/api/services/tax/mutation'
import { useTax } from '@/pages/accounting/tax/context/TaxContext'
import TaxQueryMethods, { TAX_TYPE_QUERY_KEY } from '@/api/services/tax/query'
import NumberField from '@/components/numeric/NumberField'

type AddTaxesDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  taxData?: TaxType
  readonly?: boolean
}

const addTaxesSchema = z.object({
  name: z.string({ message: 'Wajib diisi' }),
  taxTypeId: z.number({ message: 'Wajib diisi' }),
  taxSubTypeId: z.number().optional().nullable(),
  inputTaxAccountId: z.string({ message: 'Wajib diisi' }),
  outputTaxAccountId: z.string({ message: 'Wajib diisi' }),
  percentage: z.number({ message: 'Wajib diisi' })
})
type AddTaxInput = Required<TypeOf<typeof addTaxesSchema>>

const AddTaxesDialog = ({ open, setOpen, taxData, readonly = false }: AddTaxesDialogProps) => {
  const [accountQuery, setAccountQuery] = useState<string>('')
  const [outAccountQuery, setOutAccountQuery] = useState<string>('')

  const { fetchTaxList, taxTypeList, handleDeleteRow, handleDetailEdit } = useTax()
  const { control, handleSubmit, reset } = useForm<AddTaxInput>({
    resolver: zodResolver(addTaxesSchema)
  })

  const taxTypeId = useWatch({ control, name: 'taxTypeId' })
  const inputTaxAccountId = useWatch({ control, name: 'inputTaxAccountId' })
  const outputTaxAccountId = useWatch({ control, name: 'outputTaxAccountId' })

  const { mutate: addMutate, isLoading: addTaxLoading } = useCreateTax()
  const { mutate: updateTaxMutate, isLoading: updateTaxLoading } = useUpdateTax()

  const { data: sellTaxAccount } = useQuery({
    enabled: !!inputTaxAccountId,
    queryKey: [ACCOUNT_QUERY_KEY, inputTaxAccountId],
    queryFn: () => AccountsQueryMethods.getAccount(inputTaxAccountId)
  })

  const { data: purchaseTaxAccount } = useQuery({
    enabled: !!outputTaxAccountId,
    queryKey: [ACCOUNT_QUERY_KEY, outputTaxAccountId],
    queryFn: () => AccountsQueryMethods.getAccount(outputTaxAccountId)
  })

  const { data: subtaxTypeList } = useQuery({
    enabled: !!taxTypeId,
    queryKey: [TAX_TYPE_QUERY_KEY, taxTypeId],
    queryFn: async () =>
      (await TaxQueryMethods.getTaxTypeList({ page: 1, limit: Number.MAX_SAFE_INTEGER, parentId: String(taxTypeId) }))
        .items,
    placeholderData: []
  })

  const { data: sellAccountList, remove: removeSellAccountList } = useQuery({
    enabled: !!accountQuery || !!sellTaxAccount?.name,
    queryKey: [ACCOUNT_LIST_QUERY_KEY, accountQuery, sellTaxAccount?.name],
    queryFn: async () =>
      (
        await AccountsQueryMethods.getAccountList({
          page: 1,
          level: 1,
          limit: Number.MAX_SAFE_INTEGER,
          search: sellTaxAccount?.name || accountQuery
        })
      ).items,
    placeholderData: []
  })

  const { data: purchaseAccountList, remove: removePurchaseAccountList } = useQuery({
    enabled: !!outAccountQuery || !!purchaseTaxAccount?.name,
    queryKey: [ACCOUNT_LIST_QUERY_KEY, outAccountQuery, purchaseTaxAccount?.name],
    queryFn: async () =>
      (
        await AccountsQueryMethods.getAccountList({
          page: 1,
          level: 1,
          limit: Number.MAX_SAFE_INTEGER,
          search: purchaseTaxAccount?.name || outAccountQuery
        })
      ).items,
    placeholderData: []
  })

  const isLoading = addTaxLoading || updateTaxLoading

  const onSubmitHandler: SubmitHandler<AddTaxInput> = (inputValues: AddTaxInput) => {
    if (taxData) {
      updateTaxMutate(
        {
          ...inputValues,
          id: taxData.id
        },
        {
          onSuccess: () => {
            toast.success('Data akun berhasil diubah')
            fetchTaxList()
            setOpen(false)
          }
        }
      )
    } else {
      addMutate(
        {
          ...inputValues
        },
        {
          onSuccess: () => {
            toast.success('Data akun berhasil ditambahkan')
            fetchTaxList()
            setOpen(false)
          }
        }
      )
    }
  }

  const [importDialogOpen, setImportDialogOpen] = useState(false)

  const handleImportSubmit = () => {
    setImportDialogOpen(false)
  }

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  useEffect(() => {
    if (taxData) {
      reset(taxData)
    } else {
      reset({
        taxTypeId: undefined,
        taxSubTypeId: undefined,
        inputTaxAccountId: undefined,
        outputTaxAccountId: undefined,
        percentage: null
      })
    }
  }, [taxData])

  return (
    <Dialog open={open} maxWidth='sm' onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        {readonly ? 'Kategori Pajak' : taxData ? 'Ubah Kategori' : 'Tambah Kategori'}
        {readonly ? (
          <Typography component='span' className='flex flex-col text-center'>
            Lihat detil kategori Pajak
          </Typography>
        ) : (
          !taxData && (
            <Typography component='span' className='flex flex-col text-center'>
              Tambahkan Kategori
            </Typography>
          )
        )}
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          {readonly ? (
            <>
              <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>Tipe Pajak</small>
                  <Typography>{taxData?.taxType?.name}</Typography>
                </div>
              </Grid>
              <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>Tipe PPh</small>
                  <Typography>{taxData?.taxSubType?.name}</Typography>
                </div>
              </Grid>
              <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>Keterangan</small>
                  <Typography>{taxData?.name}</Typography>
                </div>
              </Grid>
              <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>Persentase</small>
                  <Typography>{taxData?.percentage}%</Typography>
                </div>
              </Grid>
              <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>Akun Pajak Penjualan</small>
                  <Typography>{sellTaxAccount ? `[${sellTaxAccount?.code}] ${sellTaxAccount?.name}` : '-'}</Typography>
                </div>
              </Grid>
              <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>Akun Pajak Pembelian</small>
                  <Typography>
                    {purchaseTaxAccount ? `[${purchaseTaxAccount?.code}] ${purchaseTaxAccount?.name}` : '-'}
                  </Typography>
                </div>
              </Grid>
            </>
          ) : (
            <>
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='taxTypeId'
                  render={({ field, fieldState: { error } }) => (
                    <FormControl fullWidth>
                      <InputLabel id='salary-type'>Tipe Pajak</InputLabel>
                      <Select
                        key={JSON.stringify(field.value)}
                        {...field}
                        labelId='salary-type'
                        label='Tipe Pajak'
                        required
                        variant='outlined'
                        placeholder='Pilih tipe Pajak'
                        disabled={isLoading}
                        {...(!!error && { error: true })}
                      >
                        {taxTypeList.map(type => (
                          <MenuItem key={type.id} value={type.id}>
                            {type.name}
                          </MenuItem>
                        ))}
                      </Select>
                      {!!error && <FormHelperText error>{error.message}</FormHelperText>}
                    </FormControl>
                  )}
                />
              </Grid>
              {subtaxTypeList?.length > 0 && (
                <Grid item xs={12}>
                  <Controller
                    control={control}
                    name='taxSubTypeId'
                    rules={{
                      validate: value => {
                        if (isNullOrUndefined(value) && subtaxTypeList?.length > 0) {
                          return 'Wajib diisi'
                        }
                        return true
                      }
                    }}
                    render={({ field, fieldState: { error } }) => (
                      <FormControl fullWidth>
                        <InputLabel id='subtax-type'>Tipe PPh</InputLabel>
                        <Select
                          key={JSON.stringify(field.value)}
                          {...field}
                          labelId='subtax-type'
                          label='Tipe PPh'
                          required
                          variant='outlined'
                          placeholder='Pilih tipe PPh'
                          disabled={isLoading}
                          {...(!!error && { error: true })}
                        >
                          {subtaxTypeList.map(type => (
                            <MenuItem key={type.id} value={type.id}>
                              {type.name}
                            </MenuItem>
                          ))}
                        </Select>
                        {!!error && <FormHelperText error>{error.message}</FormHelperText>}
                      </FormControl>
                    )}
                  />
                </Grid>
              )}
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='name'
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label='Keterangan'
                      variant='outlined'
                      placeholder='Contoh: Jasa Konsultan'
                      disabled={isLoading}
                      InputLabelProps={{ shrink: !!field.value }}
                      {...(!!error && { error: true, helperText: error?.message })}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='percentage'
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label='Persentase'
                      variant='outlined'
                      placeholder='Contoh: 5%'
                      disabled={isLoading}
                      InputProps={{ inputComponent: NumberField as any, endAdornment: '%' }}
                      InputLabelProps={{ shrink: !!field.value }}
                      {...(!!error && { error: true, helperText: error?.message })}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='inputTaxAccountId'
                  render={({ field, fieldState: { error } }) => (
                    <Autocomplete
                      key={JSON.stringify(field.value)}
                      filterOptions={x => x}
                      clearOnBlur
                      onInputChange={debounce((e, newValue, reason) => {
                        if (reason === 'input') {
                          setAccountQuery(newValue)
                        }
                      }, 700)}
                      options={sellAccountList || []}
                      freeSolo
                      onChange={(e, newValue: AccountType) => {
                        if (newValue) {
                          removeSellAccountList()
                          field.onChange(newValue.id)
                        } else {
                          field.onChange(null)
                        }
                      }}
                      value={sellAccountList?.find(account => account.id === field.value) || null}
                      noOptionsText='Akun tidak ditemukan'
                      renderInput={params => (
                        <TextField
                          {...params}
                          label='Akun Pajak Penjualan'
                          placeholder='Cari kode akun'
                          variant='outlined'
                          InputProps={{
                            ...params.InputProps,
                            onKeyDown: e => {
                              if (e.key === 'Enter') {
                                e.stopPropagation()
                              }
                            }
                          }}
                          {...(!!error && { error: true, helperText: error.message })}
                        />
                      )}
                      getOptionLabel={option => (option as AccountType).name}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='outputTaxAccountId'
                  render={({ field, fieldState: { error } }) => (
                    <Autocomplete
                      key={JSON.stringify(field.value)}
                      filterOptions={x => x}
                      clearOnBlur
                      onInputChange={debounce((e, newValue, reason) => {
                        if (reason === 'input') {
                          setOutAccountQuery(newValue)
                        }
                      }, 700)}
                      options={purchaseAccountList || []}
                      freeSolo
                      onChange={(e, newValue: AccountType) => {
                        if (newValue) {
                          removePurchaseAccountList()
                          field.onChange(newValue.id)
                        } else {
                          field.onChange(null)
                        }
                      }}
                      value={purchaseAccountList?.find(account => account.id === field.value) || null}
                      noOptionsText='Akun tidak ditemukan'
                      renderInput={params => (
                        <TextField
                          {...params}
                          label='Akun Pajak Pembelian'
                          placeholder='Cari kode akun'
                          variant='outlined'
                          InputProps={{
                            ...params.InputProps,
                            onKeyDown: e => {
                              if (e.key === 'Enter') {
                                e.stopPropagation()
                              }
                            }
                          }}
                          {...(!!error && { error: true, helperText: error.message })}
                        />
                      )}
                      getOptionLabel={option => (option as AccountType).name}
                    />
                  )}
                />
              </Grid>
            </>
          )}
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        {readonly ? (
          <>
            <Button onClick={() => handleDeleteRow(taxData.id)} variant='outlined' color='error'>
              Hapus Kategori
            </Button>
            <Button onClick={handleDetailEdit} variant='outlined'>
              Edit Kategori
            </Button>
          </>
        ) : (
          <>
            <Button
              onClick={() => setOpen(false)}
              variant='outlined'
              disabled={isLoading}
              className='is-full sm:is-auto'
            >
              BATALKAN
            </Button>
            <LoadingButton
              startIcon={<></>}
              loading={isLoading}
              loadingPosition='start'
              variant='contained'
              onClick={handleSubmit(onSubmitHandler, console.error)}
              className='px-8 is-full !ml-0 sm:is-auto'
            >
              {taxData ? 'UBAH DATA' : 'TAMBAHKAN'}
            </LoadingButton>
          </>
        )}
      </DialogActions>
    </Dialog>
  )
}

export default AddTaxesDialog
