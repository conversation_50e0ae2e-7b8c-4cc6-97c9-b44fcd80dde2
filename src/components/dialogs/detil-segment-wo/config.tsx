import truncateString from '@/core/utils/truncate'
import { WoSegmentItem } from '@/types/woTypes'
import { Icon<PERSON>utton, Tooltip, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'

const columnHelper = createColumnHelper<WoSegmentItem>()

type RowActionType = {
  detail: (item: WoSegmentItem) => void
  type?: 'COMPONENT' | 'MISCELLANEOUS'
}

export const tableColumns = ({ detail, type = 'COMPONENT' }: RowActionType) => [
  columnHelper.accessor('item', {
    header: 'KODE BARANG',
    cell: ({ row }) => row.original.item?.number
  }),
  ...(type === 'MISCELLANEOUS'
    ? []
    : [
        columnHelper.accessor('serialNumber', {
          header: 'NO. SERIAL',
          cell: ({ row }) => row.original?.serialNumber?.number ?? '-'
        })
      ]),
  columnHelper.accessor('item.name', {
    header: 'NAMA ITEM',
    cell: ({ row }) => row.original.item?.name ?? '-'
  }),
  columnHelper.accessor('item.brandName', {
    header: 'MERK ITEM',
    cell: ({ row }) => row.original.item?.brandName ?? '-'
  }),
  columnHelper.accessor('item.largeUnitQuantity', {
    header: type === 'COMPONENT' ? 'QTY' : 'QTY AWAL',
    cell: ({ row }) => row.original.quantity + ' ' + row.original.item?.smallUnit
  }),
  ...(type === 'MISCELLANEOUS'
    ? [
        columnHelper.accessor('usedQuantity', {
          header: 'QTY DIPAKAI',
          cell: ({ row }) => row.original.usedQuantity + ' ' + row.original.item?.smallUnit
        })
      ]
    : []),
  columnHelper.accessor('note', {
    header: 'KETERANGAN',
    cell: ({ row }) => (
      <Tooltip sx={{ cursor: 'default' }} title={row.original.note}>
        <Typography>{truncateString(row.original.note ?? '-', 10)}</Typography>
      </Tooltip>
    )
  }),
  columnHelper.display({
    id: 'detail',
    header: 'ACTION',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => detail(row.original)} className='flex items-center gap-0.5'>
          <i className='ri-eye-line text-secondary' />
        </IconButton>
      )
    }
  })
]
