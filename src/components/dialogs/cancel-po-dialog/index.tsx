// React Imports
import { useEffect, useState } from 'react'

// MUI Imports
import Button from '@mui/material/Button'
import Dialog from '@mui/material/Dialog'
import DialogActions from '@mui/material/DialogActions'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import IconButton from '@mui/material/IconButton'
import Typography from '@mui/material/Typography'

import { Controller, FormProvider, useForm } from 'react-hook-form'

import {
  Autocomplete,
  createFilterOptions,
  FormControl,
  FormHelperText,
  InputLabel,
  MenuItem,
  Select,
  TextField
} from '@mui/material'

import { useFilePicker } from 'use-file-picker'

import type { CancellationType } from '@/types/apps/ledgerTypes'
import { PoType } from '@/pages/purchase-order/config/types'
import { useQuery } from '@tanstack/react-query'
import UserQueryMethods, { DEFAULT_APPROVER_QUERY_KEY } from '@/api/services/user/query'
import { cancelationTypeOptions } from '@/pages/purchase-order/config/options'
import LoadingButton from '@mui/lab/LoadingButton'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import { useAuth } from '@/contexts/AuthContext'
import { toCurrency } from '@/utils/helper'

type AddMrItemProps = {
  open: boolean
  setOpen: (open: boolean) => void
  onSubmit: (formData?: CancellationType) => void
  isLoading?: boolean
  poData?: PoType
}

const CancelPoDialog = ({ open, setOpen, poData, onSubmit, isLoading }: AddMrItemProps) => {
  const { userProfile } = useAuth()
  const methods = useForm<CancellationType>()

  const scope = DefaultApprovalScope.PurchaseOrderCancelation

  const { data: defaultApprovers } = useQuery({
    enabled: !!poData.siteId && !!userProfile?.departmentId,
    queryKey: [DEFAULT_APPROVER_QUERY_KEY, scope, poData?.siteId, userProfile?.departmentId],
    queryFn: () =>
      UserQueryMethods.getDefaultApproverList({
        limit: 1000,
        scope,
        siteId: poData?.siteId,
        departmentId: userProfile?.departmentId
      }),
    placeholderData: []
  })

  const { setValue, handleSubmit, control } = methods

  const { openFilePicker, filesContent } = useFilePicker({
    accept: 'image/*',
    readAs: 'DataURL'
  })

  const handleClose = () => {
    setOpen(false)
  }

  const filter = createFilterOptions<any>()

  const approverList =
    defaultApprovers.map(approver => ({ ...approver.user, threshold: approver.threshold ?? 0 })) ?? []

  useEffect(() => {
    if (filesContent && filesContent.length > 0) {
      setValue('proofFile', filesContent?.[0]?.content)
      setValue('proofFileName', filesContent?.[0]?.name)
    }
  }, [filesContent])

  useEffect(() => {
    setValue(
      'approvals',
      approverList
        .filter(approver => approver.threshold <= (poData.grandTotal ?? 0))
        .map(approver => ({
          userId: approver.id
        }))
    )
  }, [approverList])

  return (
    <FormProvider {...methods}>
      <Dialog fullWidth open={open} onClose={handleClose} maxWidth='md' scroll='paper'>
        <DialogTitle variant='h4' className='flex gap-2 flex-col items-center sm:pbs-16 sm:pbe-6 sm:px-16'>
          <div className='max-sm:is-[80%] max-sm:text-center'>Ajukan Pembatalan Purchase Order</div>
          <Typography component='span' className='flex flex-col text-center'>
            Masukkan dan lengkapi data pendukung pengajuan pembatalan
          </Typography>
        </DialogTitle>
        <DialogContent className='overflow-visible pbs-0 sm:pbe-10 sm:px-16'>
          <IconButton onClick={handleClose} className='absolute block-start-4 inline-end-4'>
            <i className='ri-close-line text-textSecondary' />
          </IconButton>
          <div className='flex flex-col gap-4'>
            <div className='flex flex-col gap-2'>
              <Typography variant='subtitle1' className='font-semibold' marginBottom={2}>
                Alasan Pembatalan
              </Typography>
              <Controller
                name='cancelationType'
                control={control}
                rules={{ required: true }}
                render={({ field: { value, onChange }, formState: { errors } }) => (
                  <FormControl fullWidth {...(errors.cancelationType && { error: true })}>
                    <InputLabel>Pilih Alasan Pembatalan</InputLabel>
                    <Select
                      label='Pilih Alasan Pembatalan'
                      value={value}
                      onChange={e => onChange((e.target as HTMLInputElement).value)}
                      className='flex-1'
                    >
                      {cancelationTypeOptions.map(option => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                    {errors.cancelationType && <FormHelperText error>Wajib Diisi.</FormHelperText>}
                  </FormControl>
                )}
              />
            </div>
            <div className='flex flex-col gap-2'>
              <Typography variant='subtitle1' marginBottom={2}>
                Unggah Bukti
              </Typography>
              <div className='flex items-center gap-4'>
                <TextField
                  size='small'
                  fullWidth
                  value={filesContent?.[0]?.name}
                  placeholder='Tidak ada file dipilih'
                  aria-readonly
                  className='flex-1'
                />
                <Button variant='contained' onClick={() => openFilePicker()}>
                  Unggah
                </Button>
              </div>
              <Controller
                name='cancelationNote'
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label='Catatan (opsional)'
                    placeholder='Masukkan catatan'
                    className='mt-2'
                  />
                )}
              />
            </div>
            <div className='flex flex-col justify-center py-2 w-full max-md:max-w-full'>
              <div className='flex w-full bg-gray-600 bg-opacity-60 min-h-[1px] max-md:max-w-full' />
            </div>
            <div className='flex flex-col gap-2'>
              <Typography variant='subtitle1' className='font-semibold' marginBottom={2}>
                Persetujuan Pembatalan
              </Typography>
              <div className='flex flex-col gap-4'>
                {approverList
                  .filter(approver => approver.threshold <= (poData.grandTotal ?? 0))
                  .map((approver, index) => (
                    <Autocomplete
                      key={approver.id}
                      value={approver}
                      filterOptions={(options, params) => {
                        const filtered = filter(options, params)

                        return filtered
                      }}
                      options={approverList}
                      getOptionLabel={option => {
                        // Value selected with enter, right from the input
                        if (typeof option === 'string') {
                          return option
                        }

                        // Regular option
                        return option.fullName
                      }}
                      renderOption={(props, option) => {
                        const { key, ...optionProps } = props

                        return (
                          <li key={key} {...optionProps}>
                            {option.fullName}
                          </li>
                        )
                      }}
                      readOnly
                      freeSolo
                      renderInput={params => (
                        <TextField
                          {...params}
                          label={`Persetujuan Tingkat ${Number(index) + 1}`}
                          InputProps={{
                            endAdornment: (
                              <Typography variant='caption' className='!text-green-700'>
                                {toCurrency(approver.threshold ?? 0)}
                              </Typography>
                            )
                          }}
                        />
                      )}
                    />
                  ))}
              </div>
            </div>
          </div>
        </DialogContent>
        <DialogActions className='justify-center pbs-0 sm:pbe-10 sm:px-16 flex-col sm:flex-row max-sm:gap-2'>
          <Button
            variant='outlined'
            color='secondary'
            type='reset'
            onClick={handleClose}
            className='is-full sm:is-auto'
          >
            BATAL
          </Button>
          <LoadingButton
            startIcon={<></>}
            loadingPosition='start'
            variant='contained'
            loading={isLoading}
            onClick={handleSubmit(onSubmit)}
            type='submit'
            className='px-8 max-sm:!ml-0 is-full sm:is-auto'
          >
            AJUKAN PEMBATALAN
          </LoadingButton>
        </DialogActions>
      </Dialog>
    </FormProvider>
  )
}

export default CancelPoDialog
