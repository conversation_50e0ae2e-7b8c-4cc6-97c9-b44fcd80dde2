// MUI Imports

import Dialog, { DialogProps } from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import { SjType } from '@/pages/material-transfer/config/types'
import { useMt } from '@/pages/material-transfer/context/MtContext'
import { Button, DialogActions, IconButton, Typography } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import Separator from '@/components/Separator'
import { useQuery } from '@tanstack/react-query'
import MtQueryMethods, { SJ_QUERY_KEY } from '@/api/services/mt/query'
import { sjStatusOptions } from '@/pages/material-transfer/config/options'
import { WarehouseDataType } from '@/types/appTypes'

type SjDetailDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  sj?: SjType
  warehouseData?: WarehouseDataType
  warehouseDatatype?: 'MT' | 'MB' | 'Pindah Barang'
  onPrintPdf?: () => void
  onExportPdf?: () => void
}

const SjDetailDialog = ({
  open,
  setOpen,
  sj,
  warehouseData,
  warehouseDatatype = 'MT',
  onExportPdf,
  onPrintPdf
}: SjDetailDialogProps) => {
  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  const handleExport = async () => {
    onExportPdf?.()
  }
  const handlePrint = async () => {
    onPrintPdf?.()
  }

  return (
    <Dialog open={open} onClose={handleClose} fullWidth maxWidth='sm' scroll='body'>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Detil Surat Jalan
      </DialogTitle>
      <DialogContent className='overflow-visible pbs-0 p-8'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <div className='flex flex-col gap-4'>
          <div className='flex flex-col gap-2 items-start w-full p-4 rounded-md bg-gray-500 bg-opacity-10'>
            <Typography color='gray'>Nomor Surat Jalan</Typography>
            <Typography variant='h5'>{sj?.number}</Typography>
          </div>
          <div className='flex flex-col gap-1'>
            <Typography color='gray'>Status</Typography>
            <Typography variant='h6'>{sjStatusOptions.find(option => option.value === sj.status)?.label}</Typography>
          </div>
          <div className='flex flex-col gap-1'>
            <Typography color='gray'>Dibuat dari {warehouseDatatype}</Typography>
            <Typography variant='h6'>{warehouseData?.number}</Typography>
          </div>
          <div className='flex flex-col gap-1'>
            <Typography color='gray'>Tanggal Dibuat</Typography>
            <Typography variant='h6'>
              {formatDate(sj?.createdAt ?? Date.now(), 'eeee, dd/MM/yyyy, HH:mm', { locale: id })}
            </Typography>
          </div>
          <Separator containerClassName='mt-0' />
          <Typography variant='button'>Material/Barang Dikirim</Typography>
          {sj?.items?.map(item => (
            <div className='flex flex-col gap-2 items-start w-full p-4 rounded-md bg-gray-500 bg-opacity-10'>
              <Typography>
                {item.item?.name} - {item.item?.brandName} {item.item?.number}
              </Typography>
              <Typography className='text-green-700'>
                {item?.quantity} {item?.quantityUnit}
              </Typography>
            </div>
          ))}
        </div>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button
          color='secondary'
          variant='outlined'
          startIcon={<i className='ri-upload-2-line' />}
          className='is-full sm:is-auto'
          onClick={handleExport}
        >
          Ekspor
        </Button>
        <Button
          color='secondary'
          variant='outlined'
          startIcon={<i className='ic-outline-local-printshop' />}
          className='px-8 is-full !ml-0 sm:is-auto'
          onClick={handlePrint}
        >
          Cetak
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default SjDetailDialog
