// MUI Imports

import Dialog, { DialogProps } from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import DialogActions from '@mui/material/DialogActions'
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import { Autocomplete, FormControl, FormHelperText, Grid, InputLabel, MenuItem, Select } from '@mui/material'
import { Controller, SubmitHandler, useForm, useWatch } from 'react-hook-form'

import { CategoryType } from '@/types/companyTypes'
import { z, TypeOf, ZodIssueCode } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from 'react-toastify'
import { useAddVendor, useUpdateVendor } from '@/api/services/company/mutation'
import LoadingButton from '@mui/lab/LoadingButton'
import { useVendor } from '@/pages/company-data/vendor/context/VendorContext'
import NumberField from '@/components/numeric/NumberField'
import { paymentMethodOptions } from '@/pages/purchase-order/config/options'
import { PurchaseOrderPaymentMethod } from '@/pages/purchase-order/config/enum'
import { isNullOrUndefined } from '@/utils/helper'

type AddVendorDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
}

const addVendorSchema = z
  .object({
    code: z.string({ message: 'Wajib diisi' }),
    type: z.string({ message: 'Wajib diisi' }).optional().nullable(),
    name: z.string({ message: 'Wajib diisi' }),
    address: z.string({ message: 'Wajib diisi' }).optional().nullable(),
    phoneNumber: z.string({ message: 'Wajib diisi' }).optional().nullable(),
    taxplayerNumber: z.string({ message: 'Wajib diisi' }),
    faxNumber: z.string().nullable().optional(),
    email: z.string().email({ message: 'Alamat email tidak valid' }).nullable().optional(),
    paymentTerms: z.enum(['COD', 'NET']).optional().nullable(),
    paymentDueDays: z.number().optional().nullable(),
    addresses: z
      .array(
        z.object({
          address: z.string({ message: 'Wajib diisi' }).min(1, { message: 'wajib diisi' }),
          phoneNumber: z.string({ message: 'Wajib diisi' }).min(1, { message: 'wajib diisi' }),
          faxNumber: z.string().nullable().optional(),
          picName: z.string({ message: 'Wajib diisi' }).min(1, { message: 'wajib diisi' }),
          picPhoneNumber: z.string().optional().nullable(),
          isDefault: z.boolean()
        })
      )
      .min(1),
    categoryId: z.string({ message: 'Wajib dipilih' })
  })
  .superRefine((values, ctx) => {
    if (isNullOrUndefined(values.paymentDueDays) && values.paymentTerms === 'NET') {
      ctx.addIssue({
        code: ZodIssueCode.custom,
        message: 'Wajib diisi',
        path: ['paymentDueDays']
      })
    }
  })

type AddVendorInput = Required<TypeOf<typeof addVendorSchema>>

const AddVendorDialog = ({ open, setOpen }: AddVendorDialogProps) => {
  const { vendorData, categoryList, fetchVendorList, fetchVendorData } = useVendor()
  const { control, handleSubmit, getValues } = useForm<AddVendorInput>({
    resolver: zodResolver(addVendorSchema),
    defaultValues: {
      code: vendorData?.code,
      name: vendorData?.name,
      address: vendorData?.address,
      phoneNumber: vendorData?.phoneNumber,
      taxplayerNumber: vendorData?.taxplayerNumber,
      faxNumber: vendorData?.faxNumber,
      email: vendorData?.email,
      categoryId: vendorData?.categoryId,
      paymentDueDays: vendorData?.paymentDueDays,
      paymentTerms: vendorData?.paymentTerms as any,
      addresses: vendorData?.addresses?.length
        ? vendorData.addresses.map(addr => ({
            address: addr.address,
            phoneNumber: addr.phoneNumber,
            faxNumber: addr.faxNumber ?? null,
            picName: addr.picName,
            picPhoneNumber: addr.picPhoneNumber,
            isDefault: addr.isDefault
          }))
        : [
            {
              address: null,
              phoneNumber: null,
              faxNumber: null,
              picName: null,
              picPhoneNumber: null,
              isDefault: false
            }
          ]
    }
  })

  const paymentTerms = useWatch({
    control,
    name: 'paymentTerms'
  })

  const { mutate: addMutate, isLoading: addLoading } = useAddVendor()
  const { mutate: updateMutate, isLoading: updateLoading } = useUpdateVendor()

  const isLoading = addLoading || updateLoading

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  const onSubmitHandler: SubmitHandler<AddVendorInput> = (inputValues: AddVendorInput) => {
    if (vendorData) {
      updateMutate(
        {
          vendorId: vendorData.id,
          ...inputValues
        },
        {
          onSuccess: () => {
            toast.success('Data vendor berhasil diubah')
            fetchVendorData()
            fetchVendorList()
            setOpen(false)
          }
        }
      )
    } else {
      addMutate(inputValues, {
        onSuccess: () => {
          toast.success('Data vendor berhasil ditambahkan')
          fetchVendorList()
          setOpen(false)
        }
      })
    }
  }

  return (
    <Dialog PaperProps={{ className: 'rounded-t-[16px] md:rounded-t-none' }} open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        {vendorData ? 'Ubah' : 'Tambah'} Vendor
        {!vendorData && (
          <Typography component='span' className='flex flex-col text-center'>
            Tambahkan vendor untuk didaftarkan ke list
          </Typography>
        )}
      </DialogTitle>
      <DialogContent className='overflow-visible pbs-0 sm:pbe-16 sm:px-12'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          <Grid item xs={12} sm={6}>
            <Controller
              name='categoryId'
              control={control}
              rules={{ required: true }}
              render={({ field: { value, onChange }, formState: { errors } }) => (
                <Autocomplete
                  key={JSON.stringify(categoryList)}
                  selectOnFocus
                  clearOnBlur
                  handleHomeEndKeys
                  freeSolo
                  value={categoryList.find(category => category.id === value) || null}
                  getOptionLabel={option => (option as CategoryType).name}
                  options={categoryList}
                  disabled={isLoading}
                  onChange={(e, newValue) => {
                    if (newValue) {
                      onChange((newValue as CategoryType).id)
                    } else {
                      onChange(null)
                    }
                  }}
                  renderInput={params => (
                    <TextField
                      {...params}
                      required
                      label='Kategori Vendor'
                      {...(errors.categoryId && { error: true, helperText: errors.categoryId?.message })}
                    />
                  )}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <Controller
              name='code'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Kode Vendor'
                  required
                  variant='outlined'
                  placeholder='Masukkkan Kode Vendor'
                  disabled={isLoading}
                  {...(errors.code && { error: true, helperText: errors.code?.message })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Typography variant='caption'>
              Kategori Belum ada?{' '}
              <Typography variant='caption' color='primary' component='span'>
                Tambah Kategori
              </Typography>
            </Typography>
          </Grid>
          <Grid item xs={12}>
            <Controller
              name='name'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Nama Vendor'
                  required
                  variant='outlined'
                  disabled={isLoading}
                  placeholder='Masukkkan Nama Vendor'
                  {...(errors.name && { error: true, helperText: errors.name?.message })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              name='email'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Email Vendor'
                  variant='outlined'
                  type='email'
                  disabled={isLoading}
                  placeholder='Masukkkan Email Vendor'
                  {...(errors.email && { error: true, helperText: errors.email?.message })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              name='taxplayerNumber'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='NPWP Vendor'
                  variant='outlined'
                  disabled={isLoading}
                  required
                  type='number'
                  placeholder='Masukkkan NPWP Vendor'
                  {...(errors.taxplayerNumber && { error: true, helperText: errors.taxplayerNumber?.message })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Typography>Alamat & PIC</Typography>
          </Grid>
          <Grid item xs={12}>
            <Controller
              name={`addresses.0.address`}
              control={control}
              rules={{ required: true }}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  fullWidth
                  multiline
                  rows={4}
                  label='Alamat Vendor'
                  required
                  variant='outlined'
                  disabled={isLoading}
                  placeholder='Masukkkan Alamat Vendor'
                  {...(!!error && { error: true, helperText: error?.message })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Controller
              name={`addresses.0.phoneNumber`}
              control={control}
              rules={{ required: true }}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Nomor Telepon Vendor'
                  required
                  variant='outlined'
                  type='tel'
                  disabled={isLoading}
                  placeholder='Masukkkan Nomor Telepon Vendor'
                  {...(!!error && { error: true, helperText: error?.message })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Controller
              name={`addresses.0.faxNumber`}
              control={control}
              rules={{ required: true }}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Fax Vendor'
                  variant='outlined'
                  disabled={isLoading}
                  placeholder='Masukkkan Fax Vendor'
                  InputProps={{
                    inputComponent: NumberField as any
                  }}
                  {...(!!error && { error: true, helperText: error?.message })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Controller
              name={`addresses.0.picName`}
              control={control}
              rules={{ required: true }}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Nama PIC'
                  variant='outlined'
                  disabled={isLoading}
                  placeholder='Masukkkan Nama PIC'
                  {...(!!error && { error: true, helperText: error?.message })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Controller
              name={`addresses.0.picPhoneNumber`}
              control={control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Nomor Telepon Vendor'
                  variant='outlined'
                  type='tel'
                  disabled={isLoading}
                  placeholder='Masukkkan Nomor Telepon Vendor'
                  {...(!!error && { error: true, helperText: error?.message })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Typography>Metode Pembayaran</Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='paymentTerms'
              render={({ field, fieldState: { error } }) => (
                <FormControl fullWidth>
                  <InputLabel error={!!error} id='payment-terms'>
                    Payment Term
                  </InputLabel>
                  <Select {...field} id='payment-terms' label='Payment Term' className='bg-white' error={!!error}>
                    {paymentMethodOptions
                      .filter(m => m.value !== PurchaseOrderPaymentMethod.CBD)
                      .map(p => (
                        <MenuItem key={p.value} value={p.value}>
                          {p.label}
                        </MenuItem>
                      ))}
                  </Select>
                  {!!error && <FormHelperText error>{error.message}</FormHelperText>}
                </FormControl>
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='paymentDueDays'
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  label='Payment Due Days'
                  InputProps={{ inputComponent: NumberField as any }}
                  {...(!!error ? { error: true, helperText: error?.message } : {})}
                />
              )}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button onClick={() => setOpen(false)} variant='outlined' disabled={isLoading} className='is-full sm:is-auto'>
          BATAL
        </Button>
        <LoadingButton
          startIcon={<></>}
          loading={isLoading}
          loadingPosition='start'
          variant='contained'
          onClick={handleSubmit(onSubmitHandler, errors => {
            console.error(errors)
            Object.entries(errors).forEach(([field, error]) => {
              toast.error(`${field}: ${error?.message}`, {
                autoClose: 5000
              })
            })
          })}
          className='px-8 is-full !ml-0 sm:is-auto'
        >
          {vendorData ? 'UBAH DATA' : 'TAMBAHKAN'}
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default AddVendorDialog
