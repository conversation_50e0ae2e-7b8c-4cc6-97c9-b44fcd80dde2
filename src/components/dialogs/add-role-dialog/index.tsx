// React Imports
import { useState, useEffect } from 'react'

// MUI Imports
import Dialog from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import TextField from '@mui/material/TextField'
import Checkbox from '@mui/material/Checkbox'
import FormGroup from '@mui/material/FormGroup'
import FormControlLabel from '@mui/material/FormControlLabel'
import DialogActions from '@mui/material/DialogActions'
import Button from '@mui/material/Button'

// Style Imports
import tableStyles from '@/core/styles/table.module.css'
import { useRole } from '@/pages/user/role/context/RoleContext'
import { toTitleCase } from '@/utils/helper'
import { PermissionType, RoleType } from '@/types/userTypes'
import { array, object, string, TypeOf } from 'zod'
import { SubmitHandler, useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { FormHelperText } from '@mui/material'
import { useAddRole, useUpdateRole } from '@/api/services/user/mutation'
import { toast } from 'react-toastify'
import LoadingButton from '@mui/lab/LoadingButton'
import { useUpdateEffect } from 'react-use'

type RoleDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  roleData?: RoleType
}

const addRoleSchema = object({
  name: string({ message: 'Nama role wajib diisi' }).min(1, { message: 'Nama role wajib diisi' }),
  description: string().optional().nullable(),
  code: string().optional().nullable(),
  permissionIds: array(string({ message: 'Permission wajib dipilih' })).min(1, 'Permission wajib dipilih')
})

type AddRoleInput = Required<TypeOf<typeof addRoleSchema>>

const AddRoleDialog = ({ open, setOpen, roleData }: RoleDialogProps) => {
  const {
    permissionListResponse: { items },
    fetchRoleList,
    fetchRoleData
  } = useRole()

  const { mutate: addMutate, isLoading: addLoading } = useAddRole()
  const { mutate: updateMutate, isLoading: updateLoading } = useUpdateRole()
  const isLoading = addLoading || updateLoading

  const {
    register,
    setValue,
    handleSubmit,
    formState: { errors }
  } = useForm<AddRoleInput>({
    resolver: zodResolver(addRoleSchema),
    defaultValues: {
      ...roleData,
      permissionIds: roleData ? roleData.permissions.map(permission => permission.id) : []
    }
  })

  const sectionList = items.reduce((group, item) => {
    const { section, scope } = item
    group[section] = group[section] ?? {}
    group[section] = {
      ...group[section],
      [scope]: [...(group[section][scope] ?? []), item]
    }
    return group
  }, {})

  const sectionKeys = Object.keys(sectionList)

  const [selectedCheckbox, setSelectedCheckbox] = useState<string[]>([])

  const [isIndeterminateCheckbox, setIsIndeterminateCheckbox] = useState<boolean>(false)

  const handleClose = () => {
    setOpen(false)
  }

  const togglePermission = (id: string) => {
    const arr = selectedCheckbox

    if (selectedCheckbox.includes(id)) {
      arr.splice(arr.indexOf(id), 1)
      setSelectedCheckbox([...arr])
    } else {
      arr.push(id)
      setSelectedCheckbox([...arr])
    }
  }

  const handleSelectAllCheckbox = () => {
    if (isIndeterminateCheckbox) {
      setSelectedCheckbox([])
    } else {
      items.forEach(item => {
        togglePermission(item.id)
      })
    }
  }

  const onSubmitHandler: SubmitHandler<AddRoleInput> = (values: AddRoleInput) => {
    if (roleData) {
      updateMutate(
        {
          roleId: roleData.id,
          ...values
        },
        {
          onSuccess: () => {
            toast.success('Data role berhasil diubah')
            fetchRoleData()
            fetchRoleList()
            handleClose()
          }
        }
      )
    } else {
      addMutate(values, {
        onSuccess: () => {
          toast.success('Role berhasil ditambahkan')
          fetchRoleList()
          handleClose()
        }
      })
    }
  }

  useEffect(() => {
    if (selectedCheckbox.length > 0 && selectedCheckbox.length < items.length) {
      setIsIndeterminateCheckbox(true)
    } else {
      setIsIndeterminateCheckbox(false)
    }
    setValue('permissionIds', selectedCheckbox)
  }, [selectedCheckbox])

  useEffect(() => {
    if (roleData) {
      setSelectedCheckbox(roleData.permissions.map(permission => permission.id))
    }
  }, [roleData])

  return (
    <Dialog
      PaperProps={{ className: 'rounded-t-[16px] md:rounded-t-none' }}
      fullWidth
      maxWidth='md'
      scroll='body'
      open={open}
      onClose={handleClose}
    >
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-16'>
        {roleData ? 'Edit Role' : 'Tambah Role'}
        <Typography component='span' className='flex flex-col text-center'>
          {roleData ? 'Edit Role' : 'Tambah Role'} dan tetapkan permission
        </Typography>
      </DialogTitle>
      <form onSubmit={handleSubmit(onSubmitHandler)}>
        <DialogContent className='overflow-visible pbs-0 sm:pbe-6 sm:px-16'>
          <IconButton onClick={handleClose} className='absolute block-start-4 inline-end-4'>
            <i className='ri-close-line text-textSecondary' />
          </IconButton>
          <TextField
            label='Nama Role'
            variant='outlined'
            fullWidth
            placeholder='Masukkan name role'
            className='mb-4'
            disabled={isLoading}
            {...register('name')}
            {...(errors.name && { error: true, helperText: errors.name?.message })}
          />
          <TextField
            label='Deskripsi Role'
            variant='outlined'
            fullWidth
            placeholder='Masukkan deskripsi role'
            className='mb-4'
            disabled={isLoading}
            {...register('description')}
          />
          <TextField
            label='Kode Role'
            variant='outlined'
            fullWidth
            placeholder='Masukkan kode role'
            disabled={isLoading}
            inputProps={{ maxLength: 7 }}
            {...register('code')}
          />
          <Typography className='plb-3 sm:plb-4 font-semibold'>Permission untuk Role</Typography>
          <div className='flex flex-col overflow-x-auto mb-3 h-[40vh]'>
            <table className={tableStyles.table}>
              <tbody className='border-be'>
                <tr className='max-sm:flex flex-col'>
                  <th className='pis-0'>
                    <Typography className='font-medium whitespace-nowrap flex-grow min-is-[225px]' color='text.primary'>
                      Akses Administrator
                    </Typography>
                  </th>
                  <th className='sm:!text-end pie-0 max-sm:!py-0'>
                    <FormControlLabel
                      className='mie-0 capitalize'
                      control={
                        <Checkbox
                          disabled={isLoading}
                          onChange={handleSelectAllCheckbox}
                          indeterminate={isIndeterminateCheckbox}
                          checked={selectedCheckbox.length === items.length}
                        />
                      }
                      label='Pilih Semua'
                    />
                  </th>
                </tr>
                {sectionKeys.map(section => {
                  const permissionKeys = Object.keys(sectionList[section])
                  return (
                    <>
                      <Typography className='pt-6 pb-2 font-semibold text-gray-400'>
                        {toTitleCase(section.replace(/-/g, ' '))}
                      </Typography>
                      {permissionKeys.map(scope => {
                        return (
                          <tr key={scope} className='max-sm:flex flex-col'>
                            <td className='pis-0'>
                              <Typography
                                className='font-medium whitespace-nowrap flex-grow min-is-[225px]'
                                color='text.primary'
                              >
                                {toTitleCase(scope.replace(/-/g, ' '))}
                              </Typography>
                            </td>
                            <td className='!text-end pie-0 max-sm:!py-0'>
                              <FormGroup className='flex-row sm:justify-end flex-nowrap gap-6'>
                                {(sectionList[section][scope] as PermissionType[]).map(permission => (
                                  <FormControlLabel
                                    className='mie-0'
                                    control={
                                      <Checkbox
                                        disabled={isLoading}
                                        id={permission.id}
                                        onChange={() => togglePermission(permission.id)}
                                        checked={selectedCheckbox.includes(permission.id)}
                                      />
                                    }
                                    label={permission.name}
                                  />
                                ))}
                              </FormGroup>
                            </td>
                          </tr>
                        )
                      })}
                    </>
                  )
                })}
              </tbody>
            </table>
          </div>
          {errors.permissionIds && <FormHelperText error>{errors.permissionIds?.message}</FormHelperText>}
        </DialogContent>
        <DialogActions className='justify-center flex-col md:flex-row gap-2 pbs-0 sm:pbe-12 sm:px-12'>
          <Button
            className='is-full md:is-auto'
            variant='outlined'
            disabled={isLoading}
            type='reset'
            color='secondary'
            onClick={handleClose}
          >
            BATAL
          </Button>
          <LoadingButton
            className='is-full md:is-auto !ml-0'
            startIcon={<></>}
            loadingPosition='start'
            loading={isLoading}
            variant='contained'
            type='submit'
          >
            {roleData ? 'EDIT' : 'TAMBAHKAN'}
          </LoadingButton>
        </DialogActions>
      </form>
    </Dialog>
  )
}

export default AddRoleDialog
