import truncateString from '@/core/utils/truncate'
import { WoSegmentType } from '@/types/woTypes'
import { createColumnHelper } from '@tanstack/react-table'

const columnHelper = createColumnHelper<WoSegmentType>()

export const tableColumns = [
  columnHelper.accessor('id', {
    header: 'NO',
    cell: ({ row }) => row.index + 1
  }),
  columnHelper.accessor('jobCode.code', {
    header: 'JOB CODE',
    cell: ({ row }) =>
      row.original?.jobCode?.code
        ? `${row.original?.jobCode?.code} | ${truncateString(row.original?.jobCode?.description, 12)}`
        : '-'
  }),
  columnHelper.accessor('componentCode.code', {
    header: 'SMCS',
    cell: ({ row }) =>
      row.original?.componentCode?.code
        ? `${row.original?.componentCode?.code} | ${truncateString(row.original?.componentCode?.description, 12)}`
        : '-'
  }),
  columnHelper.accessor('modifierCode.code', {
    header: 'MODIFIER',
    cell: ({ row }) =>
      row.original?.modifierCode?.code
        ? `${row.original?.modifierCode?.code} | ${truncateString(row.original?.modifierCode?.description, 12)}`
        : '-'
  })
]
