import { useAddCategory, useUpdateCategory } from '@/api/services/company/mutation'
import { useItem } from '@/pages/company-data/item/context/ItemContext'
import { zodResolver } from '@hookform/resolvers/zod'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Autocomplete,
  Box,
  Button,
  Checkbox,
  colors,
  debounce,
  Dialog,
  DialogActions,
  DialogContent,
  DialogProps,
  DialogTitle,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Tooltip,
  Typography
} from '@mui/material'
import { useEffect, useState } from 'react'
import { Controller, SubmitHandler, useForm, useWatch } from 'react-hook-form'
import { toast } from 'react-toastify'
import { object, string, TypeOf, z } from 'zod'
import ImportDialog from '../import-dialog'
import { ExportImportScope } from '@/types/exportImportTypes'
import { useCategory } from '@/pages/company-data/category/context/CategoryContext'
import MobileDropDown from '@/components/layout/shared/components/MobileDropDown'
import { SalaryType } from '@/types/salaryTypes'
import { usePayrollContext } from '@/pages/accounting/payroll/context/PayrollContext'
import truncateString from '@/core/utils/truncate'
import { useQuery } from '@tanstack/react-query'
import AccountsQueryMethods, { ACCOUNT_LIST_QUERY_KEY } from '@/api/services/account/query'
import { AccountType } from '@/types/accountTypes'
import { useCreateSalary, useUpdateSalary } from '@/api/services/salaries/mutation'
import { SalaryPayload } from '@/types/payload'
import { useAccount } from '@/pages/accounting/accounts/context/AccountContext'
import CurrencyField from '@/components/numeric/CurrencyField'
import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { formatISO, toDate } from 'date-fns'
import { isNullOrUndefined } from '@/utils/helper'
import { useCreateAccount, useUpdateAccount } from '@/api/services/account/mutation'

type AddAccountsDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  accountData?: AccountType
  readonly?: boolean
}

const addAccountsSchema = z.object({
  code: z
    .string()
    .min(1, { message: 'Wajib diisi' })
    .regex(/^[A-Z0-9\-\.,]+$/, {
      message: 'Hanya huruf kapital, angka, tanda hubung (-), titik (.), dan koma (,) yang diperbolehkan'
    }),
  name: z.string({ message: 'Wajib diisi' }),
  accountTypeId: z.number({ message: 'Wajib diisi' }),
  parentId: z.string().optional().nullable(),
  balance: z.number({ message: 'Wajib diisi' }),
  balanceAt: z.string({ message: 'Wajib diisi' }), // ISO date string
  level: z.number().optional().nullable(),
  note: z.string().optional().nullable(),
  useParent: z.boolean().optional().nullable()
})
type AddAccountsInput = Required<TypeOf<typeof addAccountsSchema>>

const AddAccountsDialog = ({ open, setOpen, accountData: accountData, readonly = false }: AddAccountsDialogProps) => {
  const [accountQuery, setAccountQuery] = useState<string>('')

  const { accountTypeList, fetchAccountList } = useAccount()
  const { control, handleSubmit, reset, setValue } = useForm<AddAccountsInput>({
    resolver: zodResolver(addAccountsSchema)
  })

  const accountType = useWatch({ control, name: 'accountTypeId' })

  const useParent = useWatch({ control, name: 'useParent' })

  const { data: subaccountList } = useQuery({
    queryKey: [ACCOUNT_LIST_QUERY_KEY, 'SUBACCOUNT_LIST_QUERY_KEY', accountType],
    enabled: !!useParent,
    queryFn: async () =>
      (
        await AccountsQueryMethods.getAccountList({
          page: 1,
          limit: Number.MAX_SAFE_INTEGER,
          level: 0,
          accountTypeIds: accountType.toString()
        })
      )?.items ?? [],
    placeholderData: []
  })

  const { mutate: addMutate, isLoading: addCategoryLoading } = useCreateAccount()
  const { mutate: updateCategoryMutate, isLoading: updateCategoryLoading } = useUpdateAccount()

  const isLoading = addCategoryLoading || updateCategoryLoading

  const onSubmitHandler: SubmitHandler<AddAccountsInput> = (inputValues: AddAccountsInput) => {
    if (accountData) {
      updateCategoryMutate(
        {
          ...inputValues,
          id: accountData.id
        },
        {
          onSuccess: () => {
            toast.success('Data akun berhasil diubah')
            fetchAccountList()
            setOpen(false)
          }
        }
      )
    } else {
      addMutate(
        {
          ...inputValues
        },
        {
          onSuccess: () => {
            toast.success('Data akun berhasil ditambahkan')
            fetchAccountList()
            setOpen(false)
          }
        }
      )
    }
  }

  const [importDialogOpen, setImportDialogOpen] = useState(false)

  const handleImportSubmit = () => {
    setImportDialogOpen(false)
  }

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  useEffect(() => {
    if (accountData) {
      reset(accountData)
    } else {
      reset({
        accountTypeId: undefined,
        parentId: undefined,
        code: '',
        name: '',
        balance: null,
        balanceAt: '',
        note: ''
      })
    }
  }, [accountData])

  return (
    <Dialog open={open} maxWidth='sm' onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        {readonly ? 'Detil Akun' : accountData ? 'Ubah Akun' : 'Tambah Akun'}
        {readonly ? (
          <Typography component='span' className='flex flex-col text-center'>
            Lihat detil kategori gaji/tunjangan
          </Typography>
        ) : (
          !accountData && (
            <Typography component='span' className='flex flex-col text-center'>
              Tambahkan Akun Perkiraan
            </Typography>
          )
        )}
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          <>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='accountTypeId'
                render={({ field, fieldState: { error } }) => (
                  <FormControl fullWidth>
                    <InputLabel id='salary-type'>Tipe Akun</InputLabel>
                    <Select
                      key={JSON.stringify(field.value)}
                      {...field}
                      labelId='salary-type'
                      label='Tipe Akun'
                      required
                      variant='outlined'
                      placeholder='Contoh: Perkiraan'
                      disabled={isLoading}
                      {...(!!error && { error: true })}
                    >
                      {accountTypeList.map(type => (
                        <MenuItem key={type.id} value={type.id}>
                          {type.name}
                        </MenuItem>
                      ))}
                    </Select>
                    {!!error && <FormHelperText error>{error.message}</FormHelperText>}
                  </FormControl>
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='useParent'
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        onBlur={e => {
                          if (e.target.value === 'true') {
                            setValue('level', 1)
                          } else {
                            setValue('level', undefined)
                          }
                        }}
                      />
                    }
                    label='Gunakan Sub Akun'
                  />
                )}
              />
            </Grid>
            {useParent && (
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='parentId'
                  render={({ field, fieldState: { error } }) => (
                    <FormControl fullWidth>
                      <InputLabel>Kode Induk</InputLabel>
                      <Select
                        {...field}
                        labelId='parent-id'
                        label='Kode Induk'
                        required
                        variant='outlined'
                        placeholder='Cari Kode'
                        disabled={isLoading}
                        {...(!!error && { error: true })}
                      >
                        {subaccountList?.map(type => (
                          <MenuItem key={type.id} value={type.id}>
                            {`[${type.code}] ${type.name}`}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  )}
                />
              </Grid>
            )}
            <Grid item xs={12}>
              <Controller
                control={control}
                name='code'
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label='Kode Perkiraan'
                    variant='outlined'
                    placeholder='Contoh: 00123'
                    disabled={isLoading}
                    InputLabelProps={{ shrink: !!field.value }}
                    {...(!!error && { error: true, helperText: error?.message })}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='name'
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label='Nama Akun'
                    variant='outlined'
                    placeholder='Contoh: Gaji Pokok'
                    disabled={isLoading}
                    InputLabelProps={{ shrink: !!field.value }}
                    {...(!!error && { error: true, helperText: error?.message })}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                control={control}
                name='balance'
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label='Saldo Awal'
                    variant='outlined'
                    placeholder='Contoh: 10000'
                    disabled={isLoading}
                    InputProps={{ inputComponent: CurrencyField as any }}
                    InputLabelProps={{ shrink: !!field.value }}
                    {...(!!error && { error: true, helperText: error?.message })}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                control={control}
                name='balanceAt'
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <AppReactDatepicker
                    boxProps={{ className: 'is-full' }}
                    selected={value ? toDate(value) : undefined}
                    onChange={(date: Date) => onChange(formatISO(date))}
                    dateFormat='eeee dd/MM/yyyy'
                    customInput={
                      <TextField
                        fullWidth
                        label='Per Tanggal'
                        className='flex-1'
                        InputProps={{
                          readOnly: true
                        }}
                        {...(!!error && { error: true, helperText: 'Wajib diisi.' })}
                      />
                    }
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='note'
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label='Catatan (opsional)'
                    variant='outlined'
                    multiline
                    rows={4}
                    disabled={isLoading}
                    InputLabelProps={{ shrink: !!field.value }}
                    {...(!!error && { error: true, helperText: error?.message })}
                  />
                )}
              />
            </Grid>
          </>
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button onClick={() => setOpen(false)} variant='outlined' disabled={isLoading} className='is-full sm:is-auto'>
          BATALKAN
        </Button>
        <LoadingButton
          startIcon={<></>}
          loading={isLoading}
          loadingPosition='start'
          variant='contained'
          onClick={handleSubmit(onSubmitHandler, console.error)}
          className='px-8 is-full !ml-0 sm:is-auto'
        >
          {accountData ? 'UBAH DATA' : 'TAMBAHKAN'}
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default AddAccountsDialog
