// MUI Imports

import Dialog, { DialogProps } from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import DialogActions from '@mui/material/DialogActions'
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import { Grid } from '@mui/material'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'

import { object, string, TypeOf } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from 'react-toastify'
import LoadingButton from '@mui/lab/LoadingButton'
import { useCancelMr } from '@/api/services/mr/mutation'
import { useMr } from '@/pages/material-request/context/MrContext'
import { usePr } from '@/pages/purchase-requisition/context/PrContext'
import { useCancelPr } from '@/api/services/pr/mutation'

type CancelPrDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
}

const cancelPrSchema = object({
  cancelationNote: string({ message: 'Wajib diisi' }).min(1, { message: 'Wajib diisi' })
})

type CancelPrInput = Required<TypeOf<typeof cancelPrSchema>>

const CancelPrDialog = ({ open, setOpen }: CancelPrDialogProps) => {
  const { prData, refreshData } = usePr()
  const { control, handleSubmit } = useForm<CancelPrInput>({
    resolver: zodResolver(cancelPrSchema)
  })

  const { mutate: cancelPrMutate, isLoading: cancelPrLoading } = useCancelPr()

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  const onSubmitHandler: SubmitHandler<CancelPrInput> = (inputValues: CancelPrInput) => {
    cancelPrMutate(
      {
        prId: prData?.id,
        cancelationNote: inputValues.cancelationNote
      },
      {
        onSuccess: () => {
          refreshData()
          toast.success('PR berhasil dibatalkan')
          setOpen(false)
        }
      }
    )
  }

  return (
    <Dialog open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Batalkan PR
        <Typography component='span' className='flex flex-col text-center'>
          Masukkan alasan pembatalan pembuatan PR. Action ini tidak dapat diubah lagi
        </Typography>
      </DialogTitle>
      <DialogContent className='overflow-visible pbs-0 sm:pbe-16 sm:px-12'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          <Grid item xs={12}>
            <Controller
              name='cancelationNote'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Alasan Pembatalan'
                  required
                  variant='outlined'
                  placeholder='Masukkkan Alasan Pembatalan'
                  disabled={cancelPrLoading}
                  {...(errors.cancelationNote && { error: true, helperText: errors.cancelationNote.message })}
                />
              )}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className='max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16 flex-col sm:flex-row max-sm:gap-2'>
        <Button
          onClick={() => setOpen(false)}
          variant='outlined'
          disabled={cancelPrLoading}
          className='is-full sm:is-auto'
        >
          BATAL
        </Button>
        <LoadingButton
          startIcon={<></>}
          loading={cancelPrLoading}
          loadingPosition='start'
          variant='contained'
          color='error'
          onClick={handleSubmit(onSubmitHandler)}
          className='px-8 max-sm:!ml-0 is-full sm:is-auto'
        >
          BATALKAN PR
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default CancelPrDialog
