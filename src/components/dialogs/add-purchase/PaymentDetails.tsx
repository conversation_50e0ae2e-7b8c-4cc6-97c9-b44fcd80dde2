import React from 'react'

import { FormControl, FormHelperText, InputLabel, MenuItem, Select, TextField } from '@mui/material'
import { Controller, useFormContext, useWatch } from 'react-hook-form'

import type { PurchaseType } from '@/types/apps/purchaseType'
import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { PurchasePayload } from '@/pages/purchase-order/config/types'
import { paymentMethodOptions } from '@/pages/purchase-order/config/options'
import { formatISO, toDate } from 'date-fns'
import { PurchaseOrderPaymentMethod } from '@/pages/purchase-order/config/enum'
import NumberField from '@/components/numeric/NumberField'

const PaymentDetails: React.FC = () => {
  const { control } = useFormContext<PurchasePayload>()

  const paymentMethodWatch = useWatch({
    control,
    name: 'paymentTerms',
    defaultValue: undefined
  })

  return (
    <div className='flex flex-col gap-5'>
      <div className='flex gap-4 mt-4 w-full max-md:max-w-full flex-col sm:flex-row'>
        <Controller
          name='paymentTerms'
          control={control}
          rules={{ required: true }}
          render={({ field: { value, onChange }, fieldState: { error } }) => (
            <FormControl fullWidth {...(!!error && { error: true })}>
              <InputLabel>Pembayaran</InputLabel>
              <Select
                label='Pembayaran'
                value={value}
                onChange={e => onChange((e.target as HTMLInputElement).value)}
                className='flex-1'
              >
                {paymentMethodOptions.map(option => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
              {!!error && <FormHelperText error>Wajib Diisi.</FormHelperText>}
            </FormControl>
          )}
        />
        {paymentMethodWatch === PurchaseOrderPaymentMethod.NET ? (
          <Controller
            name='paymentDueDays'
            control={control}
            rules={{ required: true }}
            render={({ field: { value, onChange }, fieldState: { error } }) => (
              <TextField
                {...{ value, onChange }}
                label='Hari'
                {...(!!error && { error: true, helperText: 'Wajib diisi' })}
                InputProps={{ inputComponent: NumberField as any, endAdornment: 'Hari' }}
              />
              // <AppReactDatepicker
              //   boxProps={{ className: 'is-full' }}
              //   selected={value ? toDate(value) : undefined}
              //   onChange={(date: Date) => onChange(formatISO(date))}
              //   dateFormat='eeee dd/MM/yyyy'
              //   minDate={new Date()}
              //   customInput={
              //     <TextField
              //       fullWidth
              //       label='Tanggal Jatuh Tempo'
              //       className='flex-1'
              //       {...(!!error && { error: true, helperText: 'Wajib diisi.' })}
              //     />
              //   }
              // />
            )}
          />
        ) : null}
        <Controller
          name='estimatedDeliveryTime'
          control={control}
          render={({ field: { value, onChange }, formState: { errors } }) => (
            <AppReactDatepicker
              boxProps={{ className: 'is-full' }}
              selected={value ? toDate(value) : undefined}
              onChange={(date: Date) => onChange(formatISO(date))}
              dateFormat='eeee dd/MM/yyyy'
              minDate={new Date()}
              customInput={
                <TextField
                  fullWidth
                  label='Perkiraan Barang Tiba (Opsional)'
                  className='flex-1'
                  InputProps={{
                    readOnly: true
                  }}
                />
              }
            />
          )}
        />
      </div>
    </div>
  )
}

export default PaymentDetails
