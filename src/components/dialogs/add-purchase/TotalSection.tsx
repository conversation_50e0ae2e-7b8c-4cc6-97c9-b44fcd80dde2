import React, { useEffect } from 'react'

import { Controller, useFormContext, useWatch } from 'react-hook-form'

import { TextField } from '@mui/material'

import type { PurchaseType } from '@/types/apps/purchaseType'
import { toCurrency } from '@/utils/helper'
import CurrencyField from '@/components/numeric/CurrencyField'
import { PurchasePayload } from '@/pages/purchase-order/config/types'
import { useAddPurchase } from './context'

interface TotalFieldProps {
  label: string
  value: string
}

const TotalField: React.FC<TotalFieldProps> = ({ label, value }) => (
  <div className='flex flex-col flex-1 shrink rounded-lg basis-0 min-w-[240px]'>
    <div className='flex flex-col px-3 w-full rounded-lg border border-solid border-gray-600 border-opacity-20'>
      <label className='gap-2.5 self-start px-1 h-0.5 text-xs leading-none bg-white text-gray-600 text-opacity-60'>
        {label}
      </label>
      <div className='overflow-hidden flex-1 shrink gap-2 self-stretch py-4 pl-1.5 w-full text-base text-gray-600 text-opacity-60'>
        {value}
      </div>
    </div>
  </div>
)

const TotalSection: React.FC = () => {
  const { control, setValue } = useFormContext<PurchasePayload>()
  const { currentCurrency } = useAddPurchase()

  const shippingCostWatch = useWatch({
    control,
    name: `shippingCost`,
    defaultValue: 0
  })

  const [totalDiscount, totalTax, totalPrice, grandTotal] = useWatch({
    control,
    name: ['totalDiscount', 'totalTax', 'totalPrice', 'grandTotal'],
    defaultValue: {}
  })

  useEffect(() => {
    const grandTotal = totalPrice + shippingCostWatch
    setValue('grandTotal', grandTotal)
  }, [totalPrice, shippingCostWatch])

  return (
    <>
      <div className='flex flex-col justify-center py-2 mt-4 w-full max-md:max-w-full'>
        <div className='flex w-full bg-gray-600 bg-opacity-60 min-h-[1px] max-md:max-w-full' />
      </div>
      <div className='flex gap-4 items-start mt-4 w-full tracking-normal max-md:max-w-full flex-col sm:flex-row'>
        <TextField
          label='Total Diskon'
          className='flex-1'
          disabled
          value={totalDiscount}
          InputProps={{
            className: 'bg-white',
            inputComponent: CurrencyField as any,
            inputProps: {
              prefix: currentCurrency?.symbol ?? 'Rp'
            }
          }}
        />
        <TextField
          label='Total Pajak'
          className='flex-1'
          disabled
          value={totalTax}
          InputProps={{
            className: 'bg-white',
            inputComponent: CurrencyField as any,
            inputProps: {
              prefix: currentCurrency?.symbol ?? 'Rp'
            }
          }}
        />
        <Controller
          name='shippingCost'
          control={control}
          render={({ field, formState: { errors } }) => (
            <TextField
              {...field}
              label='Ongkos Kirim'
              className='flex-1'
              InputProps={{
                className: 'bg-white',
                inputComponent: CurrencyField as any,
                inputProps: {
                  prefix: currentCurrency?.symbol ?? 'Rp'
                }
              }}
            />
          )}
        />
      </div>
      <div className='flex overflow-hidden gap-1 items-center p-4 mt-4 w-full text-base tracking-normal leading-none rounded-lg bg-green-400 bg-opacity-30 text-gray-600 text-opacity-90 max-md:max-w-full'>
        <div className='flex flex-wrap flex-1 shrink gap-6 justify-center items-center self-stretch my-auto w-full basis-0 min-w-[240px] max-md:max-w-full'>
          <div className='self-stretch my-auto font-medium'>TOTAL PURCHASE</div>
          <div className='self-stretch my-auto font-bold text-right'>
            {toCurrency(grandTotal, false, currentCurrency?.symbol)}
          </div>
        </div>
      </div>
    </>
  )
}

export default TotalSection
