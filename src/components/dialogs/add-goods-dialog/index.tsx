// MUI Imports

import Dialog, { DialogProps } from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import Button from '@mui/material/Button'
import { FormControl, Grid, InputLabel, MenuItem, Select } from '@mui/material'
import Card from '@mui/material/Card'
import { getCoreRowModel, useReactTable } from '@tanstack/react-table'
import DebouncedInput from '@/components/DebounceInput'
import Table from '@/components/table'
import { goodsTableColumns } from '@/pages/material-goods/list-stock/config/table'
import { ItemType } from '@/types/companyTypes'
import { useSo } from '@/pages/material-goods/stock-opnam/context/SoContext'
import { useEffect } from 'react'
import { SoItemType } from '@/types/soTypes'

type AddGoodsDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  setData: (data: ItemType[]) => void
  excludedData: SoItemType[]
}

const AddGoodsDialog = (props: AddGoodsDialogProps) => {
  const { open, setOpen, setData, excludedData } = props

  const {
    itemListResponse: { items: itemList, totalItems, totalPages },
    itemsParams: { categoryId, search },
    categoryList,
    setItemPickerOpen,
    setPartialItemsParams,
    selectedSiteId
  } = useSo()

  const handleClose = () => {
    setOpen(!open)
  }

  const handleSubmit = () => {
    setData(table.getSelectedRowModel().flatRows.map(data => data.original))
    setOpen(!open)
  }

  const table = useReactTable({
    data: itemList,
    columns: goodsTableColumns(selectedSiteId),
    initialState: {
      pagination: {
        pageSize: 10
      }
    },
    manualPagination: true,
    rowCount: totalItems,
    pageCount: totalPages,
    getCoreRowModel: getCoreRowModel()
  })

  useEffect(() => {
    setItemPickerOpen(prev => {
      if (prev !== open) {
        return open
      }
      return prev
    })
  }, [open])

  return (
    <>
      <Dialog fullWidth maxWidth='md' open={open} onClose={handleClose}>
        <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-4 sm:px-12'>
          Pilih Barang
          <Typography component='span' className='flex flex-col text-center'>
            Tambahkan material/barang yang akan masuk stok opnam
          </Typography>
        </DialogTitle>
        <IconButton onClick={handleClose} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <DialogContent>
          <div className='flex justify-center gap-4 pb-5 flex-row items-start sm:flex-col sm:items-center'>
            <div className='flex gap-4 items-center flex-col sm:flex-row is-full sm:is-auto'>
              <DebouncedInput
                value={search}
                onChange={value => setPartialItemsParams('search', value)}
                placeholder='Cari'
                className='is-full sm:is-auto'
              />
              <FormControl size='small' className='w-[240px] max-sm:is-full'>
                <InputLabel id='role-select'>Pilih Kategori</InputLabel>
                <Select
                  fullWidth
                  id='select-category'
                  value={categoryId}
                  onChange={e => setPartialItemsParams('categoryId', e.target.value)}
                  label='Pilih Kategori'
                  size='small'
                  labelId='category-select'
                  inputProps={{ placeholder: 'Pilih Kategori' }}
                  defaultValue=''
                >
                  <MenuItem value=''>Semua Kategori</MenuItem>
                  {categoryList.map(category => (
                    <MenuItem key={category.id} value={category.id}>
                      {category.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </div>
          </div>
          <Card>
            <Table
              table={table}
              emptyLabel={
                <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                  <Typography> Belum ada Barang</Typography>
                  <Typography className='text-sm text-gray-400'>
                    Semua barang yang telah kamu buat akan ditampilkan di sini
                  </Typography>
                </td>
              }
              onRowsPerPageChange={pageSize => setPartialItemsParams('limit', pageSize)}
              onPageChange={pageIndex => setPartialItemsParams('page', pageIndex)}
            />
          </Card>

          <div className='flex flex-col sm:flex-row justify-center gap-4 py-5 sm:p-5 is-full sm:is-auto'>
            <Button color='secondary' variant='outlined' className='is-full sm:is-auto' onClick={handleClose}>
              BATALKAN
            </Button>
            <Button startIcon={<></>} variant='contained' onClick={handleSubmit} className='is-full sm:is-auto'>
              PILIH BARANG
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}

export default AddGoodsDialog
