import { ComponentType, useComponent } from '@/pages/repair-and-maintenance/code/component/context/CodeContext'
import { ObjectComponentType } from '@/pages/repair-and-maintenance/code/component/create/config/schema'
import { CodeType } from '@/types/codes'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Button,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  ListItemText,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import React from 'react'
import { Controller, useForm } from 'react-hook-form'

interface EditCodeComponentDialogProps {
  open: boolean
  setOpen: (value: React.SetStateAction<CodeType>) => void
  handleClose: () => void
  component: CodeType
  familyList: CodeType[]
}

const EditCodeComponentDialog = (props: EditCodeComponentDialogProps) => {
  const { open, setOpen, handleClose, component, familyList } = props
  const { onEditHandler, loadingUpdateComponentCode } = useComponent()
  const { control, handleSubmit } = useForm<ObjectComponentType>({
    defaultValues: {
      code: component.code,
      description: component.description,
      family: component.codeFamilies?.map(family => `${family.id}`),
      createdAt: component.createdAt,
      id: `${component.id}`
    }
  })

  return (
    <Dialog open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Edit
        <Typography component='span' className='flex flex-col text-center'>
          Edit component yang terdaftar
        </Typography>
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        <IconButton onClick={() => setOpen(null)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          <Grid item xs={6}>
            <Controller
              control={control}
              name='code'
              render={({ field: { onChange, value }, fieldState: { error } }) => (
                <TextField
                  error={Boolean(error)}
                  fullWidth
                  label='Kode Component'
                  variant='outlined'
                  placeholder='Masukkkan Kode Component'
                  value={value}
                  onChange={e => onChange((e.target as HTMLInputElement).value)}
                />
              )}
            />
          </Grid>
          <Grid item xs={6}>
            <Controller
              control={control}
              name='family'
              render={({ field: { onChange, value }, fieldState: { error } }) => {
                return (
                  <FormControl fullWidth>
                    <InputLabel>Family</InputLabel>
                    <Select
                      label='Family'
                      error={Boolean(error)}
                      id='numbering-divider'
                      value={value}
                      renderValue={selected =>
                        selected?.map(f => familyList?.find(c => String(c.id) === f)?.code).join(', ')
                      }
                      onChange={e => onChange(e.target.value as string[])}
                      placeholder='Pilih Family'
                      multiple
                      inputProps={{
                        className: 'bg-white dark:bg-inherit'
                      }}
                      MenuProps={{
                        PaperProps: {
                          className: 'max-h-[200px] overflow-y-auto'
                        }
                      }}
                    >
                      {familyList.map(family => (
                        <MenuItem key={family.id} value={String(family.id)}>
                          <Checkbox checked={value?.includes(String(family.id))} />
                          <ListItemText primary={family.code} />
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )
              }}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='description'
              render={({ field: { onChange, value }, fieldState: { error } }) => (
                <TextField
                  error={Boolean(error)}
                  fullWidth
                  label='Deskripsi'
                  variant='outlined'
                  placeholder='Masukkkan Deskripsi'
                  value={value}
                  onChange={e => onChange((e.target as HTMLInputElement).value)}
                />
              )}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button
          onClick={() => setOpen(null)}
          variant='outlined'
          disabled={loadingUpdateComponentCode}
          className='is-full sm:is-auto'
        >
          BATAL
        </Button>
        <LoadingButton
          startIcon={<></>}
          loading={loadingUpdateComponentCode}
          loadingPosition='start'
          variant='contained'
          onClick={handleSubmit(onEditHandler)}
          className='px-8 is-full !ml-0 sm:is-auto'
        >
          SIMPAN PERUBAHAN
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default EditCodeComponentDialog
