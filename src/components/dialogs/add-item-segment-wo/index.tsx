import * as Sentry from '@sentry/react'
import { ImageItemType, ItemType } from '@/types/companyTypes'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Autocomplete,
  Button,
  CircularProgress,
  debounce,
  Dialog,
  DialogActions,
  DialogContent,
  DialogProps,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { useEffect, useState } from 'react'
import { defaultImageList } from '../add-item-dialog'
import PhotoPicker from '@/components/PhotoPicker'
import CompanyQueryMethods, { ITEM_LIST_QUERY_KEY, ITEM_QUERY_KEY } from '@/api/services/company/query'
import { useQuery } from '@tanstack/react-query'
import { Controller, useForm, useWatch } from 'react-hook-form'
import { ItemType as FormItemType, itemSchema } from '@/pages/repair-and-maintenance/wo/create-wo/config/schema'
import { zodResolver } from '@hookform/resolvers/zod'
import NumberField from '@/components/numeric/NumberField'
import { useUploadImage } from '@/api/services/file/mutation'
import { ItemTypeWithSn } from '../search-serial-number'
import { mergeArrays } from '@/utils/helper'
import { toast } from 'react-toastify'
import { WoSegmentType } from '@/types/woTypes'

type AddItemSegmentDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  onClose?: () => void
  onSuccessfullAdd?: (data: AddItemDto & { segment?: WoSegmentType }) => void
  onSuccessfullUpdate?: (data: AddItemDto & { segment?: WoSegmentType }) => void
  selectedItem?: ItemTypeWithSn
  edit?: boolean
  segments?: WoSegmentType[]
  currentSegment?: WoSegmentType
}

export type AddItemDto = {
  id?: string
  parentCode: string
  number: string
  externalCode: string
  name: string
  category: string
  brandName: string
} & FormItemType

const DialogAddItemSegmentWo = (props: AddItemSegmentDialogProps) => {
  const { open, setOpen, segments, currentSegment } = props

  const [imageList, setImageList] = useState<ImageItemType[]>(defaultImageList)
  const [itemSearchQuery, setItemSearchQuery] = useState<string>('')
  const [selectedItem, setSelectedItem] = useState<ItemType | null>(props.selectedItem?.id ? props?.selectedItem : null)
  const [selectedSegmentId, setSelectedSegmentId] = useState<string | null>(null)

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    props?.onClose?.()
    setSelectedItem(null)
    setOpen(false)
  }

  const { control, handleSubmit, reset, getValues } = useForm<AddItemDto>({
    defaultValues: {
      parentCode: selectedItem?.parentCode || '',
      number: selectedItem?.number || '',
      externalCode: selectedItem?.vendorNumber || '',
      name: selectedItem?.name || '',
      category: selectedItem?.category?.name || '',
      brandName: selectedItem?.brandName || '',
      note: selectedItem?.description || '',
      largeUnitQuantity: selectedItem?.largeUnitQuantity || 0,
      quantity: selectedItem?.quantity || 0,
      quantityUnit: selectedItem?.quantityUnit || '',
      type: 'COMPONENT',
      images: imageList?.map(img => ({ filename: img.fileName }))
    },
    resolver: zodResolver(itemSchema)
  })

  const watchSn = useWatch({ control, name: 'serialNumber' })

  useQuery({
    enabled: !!selectedItem?.id && !!props.edit,
    queryKey: [ITEM_QUERY_KEY, selectedItem?.id],
    queryFn: async () => {
      const res = await CompanyQueryMethods.getItem(selectedItem?.id)
      setSelectedItem(curr => ({ ...curr, largeUnit: res?.largeUnit, smallUnit: res?.smallUnit }))
    }
  })

  const {
    data: itemList,
    isLoading: fetchItemsLoading,
    remove: removeItemList
  } = useQuery({
    enabled: !!itemSearchQuery,
    queryKey: [ITEM_LIST_QUERY_KEY, itemSearchQuery],
    queryFn: async () => {
      const res = await CompanyQueryMethods.getItemList({
        page: 1,
        limit: Number.MAX_SAFE_INTEGER,
        search: itemSearchQuery,
        isInStock: true
      })
      return res.items
    },
    placeholderData: [] as ItemType[]
  })

  const { mutateAsync: uploadMutate, isLoading: uploadLoading } = useUploadImage()

  const onSubmitItem = (dto: AddItemDto) => {
    Sentry.captureMessage(`Submit Segment Item: ${JSON.stringify(dto)}`)
    Promise.all(
      imageList
        .filter(item => !!item.fileName)
        .map(item =>
          uploadMutate({
            fieldName: `item_image_${dto.number}`,
            file: item.content,
            scope: 'public-image',
            fileName: item.fileName
          })
        )
    ).then(values => {
      const uploadIds = values.map(val => ({
        url: val.data?.url ?? '',
        uploadId: val.data?.id ?? ''
      }))
      const retData = {
        ...getValues(),
        number: selectedItem?.number,
        name: selectedItem?.name,
        brandName: selectedItem?.brandName,
        itemId: dto.itemId,
        type: dto.type,
        serialNumber: dto.serialNumber,
        quantity: dto.quantity,
        quantityUnit: dto.quantityUnit,
        largeUnitQuantity: dto.largeUnitQuantity,
        note: dto.note,
        images: uploadIds.map(id => ({ uploadId: id.uploadId, url: id.url })),
        ...(selectedSegmentId && { segment: segments.find(segment => segment.id === selectedSegmentId) })
      }
      if (props.edit) {
        props.onSuccessfullUpdate?.({
          ...retData,
          images: mergeArrays(props.selectedItem?.images, retData.images, 'uploadId')
        })
      } else {
        props?.onSuccessfullAdd?.(retData)
      }
      setSelectedItem(null)
    })
  }

  useEffect(() => {
    if (props.selectedItem) {
      const { id, serialNumber } = props.selectedItem
      if (!!id) {
        reset({ ...getValues(), itemId: id, serialNumber: serialNumber })
      } else {
        reset({ ...getValues(), serialNumber, quantity: 1 })
      }
      if (props.selectedItem?.images?.length > 0) {
        setImageList([
          ...props.selectedItem?.images.map(image => ({
            content: image.url,
            uploadId: image.uploadId
          })),
          ...(new Array(5 - (props.selectedItem.images?.length ?? 0)).fill({
            content: '',
            fileName: ''
          }) as ImageItemType[])
        ])
      }
    }
  }, [props.selectedItem])

  useEffect(() => {
    if (!open) reset()
  }, [open])

  useEffect(() => {
    if (currentSegment?.id) {
      setSelectedSegmentId(currentSegment?.id)
    }
  }, [currentSegment])

  return (
    <Dialog maxWidth='lg' open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        {!props.edit ? 'Tambah' : 'Ubah'} Barang
        {!props.edit && (
          <Typography component='span' className='flex flex-col text-center'>
            Masukkan data barang yang akan ditambahkan ke Segment
          </Typography>
        )}
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        <IconButton
          onClick={() => {
            setOpen(false)
            props?.onClose?.()
          }}
          className='absolute block-start-4 inline-end-4'
        >
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={4}>
          {segments && segments?.length > 0 && (
            <>
              <Grid item xs={12}>
                <Typography>Untuk Segment</Typography>
              </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>Segment</InputLabel>
                  <Select
                    key={selectedSegmentId}
                    label='Segment'
                    id='segment-select'
                    labelId='segment-select'
                    value={selectedSegmentId}
                    onChange={e => setSelectedSegmentId(e.target.value)}
                  >
                    {segments?.map(segment => (
                      <MenuItem key={segment.id} value={segment.id}>
                        No. {segment.number} /{' '}
                        {segment?.division?.id ? `[${segment.division.code}] ${segment.division.name}` : '-'} /{' '}
                        {segment?.jobCode?.id ? `${segment.jobCode.code} | ${segment.jobCode.description}` : '-'} /{' '}
                        {segment?.modifierCode?.id
                          ? `${segment.modifierCode.code} | ${segment.modifierCode.description}`
                          : '-'}{' '}
                        /{' '}
                        {segment?.modifierCode?.id
                          ? `${segment.modifierCode.code} | ${segment.modifierCode.description}`
                          : '-'}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </>
          )}
          <Grid item xs={12}>
            <Typography>Detil Barang</Typography>
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='itemId'
              render={({ field: { onChange }, fieldState: { error } }) => (
                <Autocomplete
                  value={selectedItem}
                  onInputChange={debounce((e, newValue, reason) => {
                    if (reason === 'input') {
                      setItemSearchQuery(newValue)
                    }
                  }, 700)}
                  options={itemList || []}
                  getOptionLabel={(option: ItemType) => `${option.number} | ${option.name} | ${option.brandName}`}
                  freeSolo={!itemSearchQuery}
                  noOptionsText='Barang tidak ditemukan'
                  onChange={(e, newValue: ItemType) => {
                    onChange(newValue?.id)
                    setSelectedItem(newValue)
                    reset({
                      ...getValues(),
                      parentCode: newValue?.parentCode || '',
                      number: newValue?.number || '',
                      externalCode: newValue?.vendorNumber || '',
                      name: newValue?.name || '',
                      category: newValue?.category?.name || '',
                      brandName: newValue?.brandName || '',
                      largeUnitQuantity: newValue?.largeUnitQuantity || 0,
                      ...(getValues('serialNumber') !== '' && { quantityUnit: newValue?.smallUnit })
                    })
                    removeItemList()
                  }}
                  renderInput={params => (
                    <TextField
                      {...params}
                      InputProps={{
                        ...params.InputProps,
                        startAdornment: <i className='ri-search-line text-textSecondary size-5 mx-2' />,
                        endAdornment: <>{fetchItemsLoading ? <CircularProgress /> : null}</>,
                        onKeyDown: e => {
                          if (e.key === 'Enter') {
                            e.stopPropagation()
                          }
                        }
                      }}
                      placeholder='Cari Kode barang, kode eksternal, nama barang, atau merk barang'
                      error={!!error}
                    />
                  )}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Controller
              control={control}
              name='parentCode'
              render={({ field }) => <TextField disabled value={field.value} fullWidth label='Kode Induk' />}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Controller
              control={control}
              name='number'
              render={({ field }) => <TextField disabled value={field.value} fullWidth label='Kode Barang' />}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Controller
              control={control}
              name='externalCode'
              render={({ field }) => <TextField disabled value={field.value} fullWidth label='Kode Eksternal' />}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Controller
              control={control}
              name='name'
              render={({ field }) => <TextField disabled value={field.value} fullWidth label='Nama Item' />}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Controller
              control={control}
              name='category'
              render={({ field }) => <TextField disabled value={field.value} fullWidth label='Kategori Item' />}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Controller
              control={control}
              name='brandName'
              render={({ field }) => <TextField disabled value={field.value} fullWidth label='Merk Item' />}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Controller
              control={control}
              name='serialNumber'
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <TextField
                  value={value}
                  onChange={onChange}
                  onBlur={e =>
                    e.target.value?.length > 1 &&
                    reset({ ...getValues(), quantity: 1, quantityUnit: selectedItem?.smallUnit })
                  }
                  fullWidth
                  label='No. Serial'
                  error={!!error}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Controller
              control={control}
              name='quantity'
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Quantity'
                  disabled={watchSn?.length > 1}
                  InputProps={{ inputComponent: NumberField as any }}
                  error={!!error}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Controller
              control={control}
              name='quantityUnit'
              render={({ field, fieldState: { error } }) => (
                <FormControl fullWidth>
                  <InputLabel>Satuan</InputLabel>
                  <Select
                    {...field}
                    label='Satuan'
                    disabled={selectedItem && watchSn?.length > 1}
                    className='bg-white'
                    error={!!error}
                  >
                    {[selectedItem?.largeUnit, selectedItem?.smallUnit].map(role => (
                      <MenuItem key={role} value={role}>
                        {role}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='note'
              render={({ field }) => (
                <TextField {...field} ref={field.ref} placeholder='Keterangan Barang (opsional)' fullWidth multiline />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Typography>Foto Barang (opsional)</Typography>
          </Grid>
          <Grid item xs={12}>
            <div className='flex gap-5 overflow-y-hidden max-sm:px-2'>
              {imageList?.map((item, index) => (
                <Controller
                  control={control}
                  name={`images.${index}.filename`}
                  key={`${item.content}_${index}`}
                  render={({ field }) => (
                    <PhotoPicker
                      key={`${item.content}_${index}`}
                      content={item.content}
                      // disabled={isLoading}
                      onPicked={(content, fileName) => {
                        field.onChange(fileName)
                        setImageList(current => {
                          const tempCurrent = [...current]
                          tempCurrent[index] = { content, fileName }
                          return tempCurrent
                        })
                      }}
                      onRemoved={() => {
                        field.onChange('')
                        setImageList(current => {
                          const tempCurrent = [...current]
                          tempCurrent[index] = { content: '', fileName: '' }
                          return tempCurrent
                        })
                      }}
                    />
                  )}
                />
              ))}
            </div>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button
          onClick={() => {
            setOpen(false)
            props?.onClose?.()
          }}
          variant='outlined'
          className='is-full sm:is-auto'
        >
          BATALKAN
        </Button>
        <LoadingButton
          startIcon={<></>}
          loading={uploadLoading}
          disabled={segments?.length > 0 && !selectedSegmentId}
          loadingPosition='start'
          variant='contained'
          onClick={handleSubmit(onSubmitItem, errors => {
            Sentry.captureException(errors)
            Object.entries(errors).forEach(([field, error]) => {
              toast.error(`${field}: ${error?.message}`, {
                autoClose: 5000
              })
            })
          })}
          className='px-8 is-full !ml-0 sm:is-auto'
        >
          {props.edit ? 'UPDATE' : 'TAMBAHKAN'}
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default DialogAddItemSegmentWo
