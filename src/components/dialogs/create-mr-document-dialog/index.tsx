// MUI Imports

import Dialog, { DialogProps } from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import { Grid } from '@mui/material'
import ChevronRight from '@/components/menu/svg/ChevronRight'
import { Link } from 'react-router-dom'
import { useMrList } from '@/pages/material-request/context/MrListContext'

type CreateMrDocumentDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
}

const CreateMrDocumentDialog = ({ open, setOpen }: CreateMrDocumentDialogProps) => {
  const { mrData, canCreatePr, canCreateMt } = useMrList()
  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  return (
    <Dialog open={open} onClose={handleClose} fullWidth maxWidth='sm' scroll='body'>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Buat Dokumen
        <Typography component='span' className='flex flex-col text-center'>
          Pilih jenis dokumen yang akan dibuat dari Material Request ini
        </Typography>
      </DialogTitle>
      <DialogContent className='overflow-visible pbs-0'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          {canCreatePr && (
            <Grid item xs={12}>
              <Link to={`/mr-in/${mrData?.id}/create-pr`}>
                <div className='flex items-center gap-4 hover:bg-primaryLighter cursor-pointer rounded-xl p-4'>
                  <i className='mdi-file-document-outline size-6 text-textSecondary' />
                  <div className='flex-1'>
                    <Typography className='text-slate-700'>Buat PR</Typography>
                    <Typography variant='subtitle2'>
                      Pilih jika dokumen akan diajukan untuk pembelian material/barang
                    </Typography>
                  </div>
                  <ChevronRight />
                </div>
              </Link>
            </Grid>
          )}
          {canCreateMt && (
            <Grid item xs={12}>
              <Link to={`/mr-in/${mrData?.id}/create-mt`}>
                <div className='flex items-center gap-4 hover:bg-primaryLighter cursor-pointer rounded-xl p-4'>
                  <i className='solar-card-transfer-linear size-6 text-slate-700' />
                  <div className='flex-1'>
                    <Typography className='text-slate-700'>Buat Material Transfer</Typography>
                    <Typography variant='subtitle2'>
                      Pilih jika dokumen akan diajukan untuk permintaan material/barang dari gudang lain
                    </Typography>
                  </div>
                  <ChevronRight />
                </div>
              </Link>
            </Grid>
          )}
        </Grid>
      </DialogContent>
    </Dialog>
  )
}

export default CreateMrDocumentDialog
