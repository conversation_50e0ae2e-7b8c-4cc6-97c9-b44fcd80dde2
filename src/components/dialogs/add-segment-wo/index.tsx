import Table from '@/components/table'
import * as Sentry from '@sentry/react'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Autocomplete,
  Button,
  CircularProgress,
  debounce,
  Dialog,
  DialogActions,
  DialogContent,
  DialogProps,
  DialogTitle,
  Divider,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { memo, useCallback, useEffect, useMemo, useState } from 'react'
import { toast } from 'react-toastify'
import { miscTableColumns, partTableColumns } from './config'
import { AddItemDto } from '../add-item-segment-wo'
import { CodeType } from '@/types/codes'
import { Controller, useFieldArray, useFormContext } from 'react-hook-form'
import { SegmentType, ItemType } from '@/pages/repair-and-maintenance/wo/create-wo/config/schema'
import AddWarehouseItemDialog from '../add-warehouse-item'
import { useQuery } from '@tanstack/react-query'
import { WoSegmentItem, WoSegmentType } from '@/types/woTypes'
import RnMQueryMethods from '@/api/services/rnm/query'
import { WO_SEGMENT_KEY } from '@/api/services/rnm/service'
import AccountsQueryMethods, { DIVISION_LIST_QUERY_KEY } from '@/api/services/account/query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { DivisionType } from '@/types/accountTypes'
import { tableColumns } from '../detil-segment-wo/config'

type AddSegmentDialogProps = {
  viewOnly?: boolean
  open: boolean
  setOpen: (open: boolean) => void
  onOpenDialogItem: () => void
  onOpenDialogMisc: () => void
  parts?: AddItemDto[]
  onAddSegment?: (s: SegmentType) => void
  onEditItem?: (item: ItemType) => void
  loading?: boolean
  configOptions: {
    segment?: WoSegmentType
    jobCodeQuery: string
    setJobCodeQuery: React.Dispatch<React.SetStateAction<string>>
    componentCodeQuery: string
    setComponentCodeQuery: React.Dispatch<React.SetStateAction<string>>
    modifierCodeQuery: string
    setModifierCodeQuery: React.Dispatch<React.SetStateAction<string>>
    jobCodeList?: CodeType[]
    componentCodeList?: CodeType[]
    modifierCodeList?: CodeType[]
    fetchJobCodeLoading?: boolean
    fetchComponentCodeLoading?: boolean
    fetchModifierCodeLoading?: boolean
  }
}

const DialogAddSegmentWo = (props: AddSegmentDialogProps) => {
  const { open, setOpen, onOpenDialogItem, onOpenDialogMisc, configOptions } = props
  const {
    segment,
    jobCodeQuery,
    setJobCodeQuery,
    componentCodeQuery,
    setComponentCodeQuery,
    modifierCodeQuery,
    setModifierCodeQuery,
    jobCodeList,
    componentCodeList,
    modifierCodeList,
    fetchJobCodeLoading,
    fetchComponentCodeLoading,
    fetchModifierCodeLoading
  } = configOptions

  const { control, handleSubmit, reset, getValues } = useFormContext<SegmentType>()
  const [selectedItem, setItem] = useState<ItemType | null>(null)
  const [itemState, setItemState] = useState<{ state: boolean; item: WoSegmentItem | null }>({
    state: false,
    item: null
  })

  const { fields, remove } = useFieldArray({ control, name: 'items' })

  const { data: segmentDetail } = useQuery({
    enabled: !!segment?.id && !!open,
    queryKey: [WO_SEGMENT_KEY, segment, open],
    queryFn: async () => {
      return await RnMQueryMethods.getWoSegment(segment?.workOrderId, segment?.id)
    }
  })

  const { data: divisions } = useQuery({
    queryKey: [DIVISION_LIST_QUERY_KEY],
    queryFn: () => {
      return AccountsQueryMethods.getDivisionList({ limit: Number.MAX_SAFE_INTEGER })
    },
    placeholderData: defaultListData as ListResponse<DivisionType>
  })

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  const handleDeleteItem = useCallback(
    (id: string) => {
      remove(fields.findIndex(item => item.id === id))
    },
    [fields]
  )

  const partTableOptions = useMemo(
    () => ({
      data: fields.filter(part => part.type === 'COMPONENT') ?? [],
      columns: partTableColumns({
        viewOnly: props.viewOnly,
        detail: item => {
          setItem({ ...item, itemId: item.itemId })
        },
        edit: props.onEditItem,
        delete: handleDeleteItem
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [props, fields]
  )

  const miscellaneousTableOptions = useMemo(
    () => ({
      data: segmentDetail?.items.filter(i => i.type !== 'COMPONENT') ?? [],
      columns: tableColumns({
        type: 'MISCELLANEOUS',
        detail: item => {
          setItemState(curr => ({ ...curr, state: true, item }))
        }
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [segmentDetail?.items]
  )

  const tableParts = useReactTable<any>(partTableOptions)
  const tableMisc = useReactTable<any>(miscellaneousTableOptions)

  const miscTableOptions = useMemo(
    () => ({
      data: fields.filter(part => part.type === 'MISCELLANEOUS') ?? [],
      columns: miscTableColumns({
        viewOnly: props.viewOnly,
        detail: item => {
          setItem({ ...item, itemId: item.itemId })
        },
        edit: item => props.onEditItem({ ...item, type: 'MISCELLANEOUS' }),
        delete: handleDeleteItem
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [props, fields]
  )

  const miscTable = useReactTable<any>(miscTableOptions)

  const onSubmitSegment = (segment: SegmentType) => {
    Sentry.captureMessage(`Submit Segmen: ${JSON.stringify(segment)}`)
    props?.onAddSegment?.(segment)
  }

  useEffect(() => {
    if (segmentDetail) {
      const parts = segmentDetail.items.map(part => ({
        images: part.images,
        itemId: part.item?.id,
        number: part.item?.number,
        name: part.item?.name,
        brandName: part.item?.brandName,
        type: part.type as any,
        serialNumber: part?.serialNumber?.number ?? '-',
        quantity: part.quantity,
        quantityUnit: part.quantityUnit,
        largeUnitQuantity: part.largeUnitQuantity,
        note: part.note
      }))
      reset({
        ...getValues(),
        items: parts
      })
    }
  }, [segmentDetail])

  return (
    <>
      <Dialog maxWidth='lg' open={open} onClose={handleClose}>
        <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
          {!props.viewOnly ? 'Tambah' : 'Detil'} Segment
          {!props.viewOnly && (
            <Typography component='span' className='flex flex-col text-center'>
              Tambahkan Segment untuk dokumen WO ini
            </Typography>
          )}
        </DialogTitle>
        <DialogContent className='pbs-0 sm:pbe-16 sm:px-8 !py-4'>
          <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
            <i className='ri-close-line text-textSecondary' />
          </IconButton>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <Typography>Detil Segment</Typography>
            </Grid>
            <Grid item xs={12} md={4}>
              <Controller
                control={control}
                name='jobCodeId'
                render={({ field: { value, onChange }, fieldState: { error } }) =>
                  props.viewOnly ? (
                    <div className='flex flex-col gap-2'>
                      <Typography variant='caption'>Job Code</Typography>
                      {jobCodeList?.length ? (
                        <Typography variant='body1'>
                          {jobCodeList.find(job => job.id === value)
                            ? `${jobCodeList.find(job => job.id === value)?.code} | ${jobCodeList.find(job => job.id === value)?.description}`
                            : '-'}
                        </Typography>
                      ) : segmentDetail?.jobCode?.code ? (
                        <Typography variant='body1'>
                          {segmentDetail?.jobCode?.code} | {segmentDetail?.jobCode?.description}
                        </Typography>
                      ) : (
                        <Typography variant='body1'>-</Typography>
                      )}
                    </div>
                  ) : (
                    <Autocomplete
                      readOnly={props.viewOnly}
                      freeSolo={!jobCodeQuery}
                      onInputChange={debounce((e, newValue, reason) => {
                        if (reason === 'input') {
                          setJobCodeQuery(newValue)
                        }
                      }, 700)}
                      value={jobCodeList?.find(job => job.id === value) || null}
                      getOptionLabel={option => `${(option as CodeType).code} | ${(option as CodeType).description}`}
                      options={jobCodeList ?? []}
                      // disabled={isLoading}
                      onChange={(e, newValue) => {
                        if (newValue) {
                          onChange((newValue as CodeType).id)
                        } else {
                          onChange(null)
                        }
                      }}
                      noOptionsText='Job Code tidak ditemukan'
                      renderInput={params => (
                        <TextField
                          {...params}
                          InputProps={{
                            ...params.InputProps,
                            startAdornment: <i className='ri-search-line text-textSecondary size-5 mr-2' />,
                            endAdornment: <>{fetchJobCodeLoading ? <CircularProgress /> : null}</>,
                            onKeyDown: e => {
                              if (e.key === 'Enter') {
                                e.stopPropagation()
                              }
                            }
                          }}
                          label='Job Code'
                          placeholder='Cari Job Code'
                          {...(!!error && { error: true })}
                        />
                      )}
                    />
                  )
                }
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Controller
                control={control}
                name='componentCodeId'
                render={({ field: { value, onChange }, fieldState: { error } }) =>
                  props.viewOnly ? (
                    <div className='flex flex-col gap-2'>
                      <Typography variant='caption'>SMCS</Typography>
                      {componentCodeList?.length ? (
                        <Typography variant='body1'>
                          {componentCodeList?.find(component => component.id === value)
                            ? `${componentCodeList.find(component => component.id === value)?.code} | ${componentCodeList.find(component => component.id === value)?.description}`
                            : '-'}
                        </Typography>
                      ) : segmentDetail?.componentCode?.code ? (
                        <Typography variant='body1'>
                          {segmentDetail?.componentCode?.code} | {segmentDetail?.componentCode?.description}
                        </Typography>
                      ) : (
                        <Typography variant='body1'>-</Typography>
                      )}
                    </div>
                  ) : (
                    <Autocomplete
                      readOnly={props.viewOnly}
                      freeSolo={!componentCodeQuery}
                      onInputChange={debounce((e, newValue, reason) => {
                        if (reason === 'input') {
                          setComponentCodeQuery(newValue)
                        }
                      }, 700)}
                      value={componentCodeList?.find(component => component.id === value) || null}
                      getOptionLabel={option => `${(option as CodeType).code} | ${(option as CodeType).description}`}
                      options={componentCodeList ?? []}
                      // disabled={isLoading}
                      onChange={(e, newValue) => {
                        if (newValue) {
                          onChange((newValue as CodeType).id)
                        } else {
                          onChange(null)
                        }
                      }}
                      noOptionsText='Kode Component tidak ditemukan'
                      renderInput={params => (
                        <TextField
                          {...params}
                          InputProps={{
                            ...params.InputProps,
                            startAdornment: <i className='ri-search-line text-textSecondary size-5 mr-2' />,
                            endAdornment: <>{fetchComponentCodeLoading ? <CircularProgress /> : null}</>,
                            onKeyDown: e => {
                              if (e.key === 'Enter') {
                                e.stopPropagation()
                              }
                            }
                          }}
                          label='SMCS'
                          placeholder='Cari Kode Component'
                          {...(!!error && { error: true })}
                        />
                      )}
                    />
                  )
                }
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Controller
                control={control}
                name='modifierCodeId'
                render={({ field: { onChange, value } }) =>
                  props.viewOnly ? (
                    <div className='flex flex-col gap-2'>
                      <Typography variant='caption'>Modifier (Opsional)</Typography>
                      {modifierCodeList?.length ? (
                        <Typography variant='body1'>
                          {modifierCodeList?.find(modifier => modifier.id === value)
                            ? `${modifierCodeList.find(modifier => modifier.id === value)?.code} | ${modifierCodeList.find(modifier => modifier.id === value)?.description}`
                            : '-'}
                        </Typography>
                      ) : segmentDetail?.modifierCode?.code ? (
                        <Typography variant='body1'>
                          {segmentDetail?.modifierCode?.code} | {segmentDetail?.modifierCode?.description}
                        </Typography>
                      ) : (
                        <Typography variant='body1'>-</Typography>
                      )}
                    </div>
                  ) : (
                    <Autocomplete
                      readOnly={props.viewOnly}
                      freeSolo={!modifierCodeQuery}
                      onInputChange={debounce((e, newValue, reason) => {
                        if (reason === 'input') {
                          setModifierCodeQuery(newValue)
                        }
                      }, 700)}
                      value={modifierCodeList?.find(modifier => modifier.id === value) || null}
                      getOptionLabel={option => `${(option as CodeType).code} | ${(option as CodeType).description}`}
                      options={modifierCodeList ?? []}
                      // disabled={isLoading}
                      onChange={(e, newValue) => {
                        if (newValue) {
                          onChange((newValue as CodeType).id)
                        } else {
                          onChange(null)
                        }
                      }}
                      noOptionsText='Kode Modifier tidak ditemukan'
                      renderInput={params => (
                        <TextField
                          {...params}
                          InputProps={{
                            ...params.InputProps,
                            startAdornment: <i className='ri-search-line text-textSecondary size-5 mr-2' />,
                            endAdornment: <>{fetchModifierCodeLoading ? <CircularProgress /> : null}</>,
                            onKeyDown: e => {
                              if (e.key === 'Enter') {
                                e.stopPropagation()
                              }
                            }
                          }}
                          label='Modifier (Opsional)'
                          placeholder='Cari Kode Modifier'
                        />
                      )}
                    />
                  )
                }
              />
            </Grid>
            {props.viewOnly ? (
              !!segment?.divisionId ? (
                <>
                  <Grid item xs={12}>
                    <Typography>Dikerjakan Oleh</Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <Typography>
                      {segment?.division?.id ? `${segment.division.code} | ${segment.division.name}` : '-'}
                    </Typography>
                  </Grid>
                </>
              ) : null
            ) : (
              <>
                <Grid item xs={12}>
                  <Typography>Dikerjakan Oleh (Opsional)</Typography>
                </Grid>
                <Grid item xs={12}>
                  <Controller
                    control={control}
                    name='divisionId'
                    render={({ field: { value, onChange }, fieldState: { error } }) => {
                      return (
                        <FormControl fullWidth>
                          <InputLabel id='division-select'>Divisi</InputLabel>
                          <Select
                            key={value}
                            label='Divisi'
                            id='division-select'
                            labelId='division-select'
                            value={value}
                            onChange={onChange}
                          >
                            {divisions?.items?.map(div => (
                              <MenuItem key={div.id} value={div.id}>
                                {div.code} - {div.name}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      )
                    }}
                  />
                </Grid>
              </>
            )}
            <Grid item xs={12}>
              <Divider />
            </Grid>
            <Grid item xs={12}>
              <div className='flex justify-between items-center'>
                <Typography>Parts & Komponen (Opsional)</Typography>
                {!props.viewOnly && (
                  <Button variant='outlined' onClick={onOpenDialogItem} size='small'>
                    Tambah Barang
                  </Button>
                )}
              </div>
            </Grid>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='items'
                render={({ fieldState: { error } }) => (
                  <>
                    <div className='shadow-sm rounded-lg'>
                      <Table
                        table={tableParts}
                        emptyLabel={
                          <td
                            colSpan={tableParts.getVisibleFlatColumns().length}
                            className='text-center h-60 space-y-2'
                          >
                            <Typography variant='h5'> Belum ada Barang</Typography>
                            <Typography>Barang yang dipilih untuk Segment ini akan ditampilkan di sini</Typography>
                          </td>
                        }
                      />
                    </div>
                    {!!error && <FormHelperText error>{error?.message}</FormHelperText>}
                  </>
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Divider />
            </Grid>
            {!!props.viewOnly && (
              <>
                <Grid item xs={12}>
                  <Typography>Miscellaneous</Typography>
                </Grid>
                <Grid item xs={12}>
                  <div className='shadow-sm rounded-[8px]'>
                    <Table
                      table={tableMisc}
                      emptyLabel={
                        <td colSpan={tableMisc.getVisibleFlatColumns().length} className='text-center h-60'>
                          <Typography>Belum ada Barang</Typography>
                          <Typography className='text-sm text-gray-400'>
                            Tambahkan barang yang ingin dimasukkan dalam Segment ini
                          </Typography>
                        </td>
                      }
                      disablePagination
                    />
                  </div>
                </Grid>
              </>
            )}
          </Grid>
        </DialogContent>
        <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
          {props.viewOnly ? (
            <Button
              variant='outlined'
              color='secondary'
              onClick={() => {
                setOpen(false)
                reset()
              }}
            >
              Tutup
            </Button>
          ) : (
            <>
              <Button
                disabled={props.loading}
                onClick={() => {
                  setOpen(false)
                  reset()
                }}
                variant='outlined'
                className='is-full sm:is-auto'
              >
                BATALKAN
              </Button>
              <LoadingButton
                startIcon={<></>}
                loading={props.loading}
                loadingPosition='start'
                variant='contained'
                onClick={handleSubmit(
                  data => onSubmitSegment(data),
                  errors => {
                    Sentry.captureException(getValues())
                    Object.entries(errors).forEach(([field, error]) => {
                      toast.error(`${field}: ${error?.message}`, {
                        autoClose: 5000
                      })
                    })
                  }
                )}
                className='px-8 is-full !ml-0 sm:is-auto'
              >
                TAMBAH SEGMENT
              </LoadingButton>
            </>
          )}
        </DialogActions>
      </Dialog>
      {!!selectedItem && (
        <AddWarehouseItemDialog
          withoutUnit
          currentItem={
            selectedItem
              ? { ...selectedItem, id: 0 }
              : itemState.item
                ? { ...itemState.item, itemId: itemState.item.item.id }
                : null
          }
          viewOnly
          open={!!selectedItem || !!itemState.state}
          setOpen={open => {
            setItem(!open && null)
            setItemState(curr => ({ ...curr, state: open, ...(!open && { item: null }) }))
          }}
          onSubmit={() => {}}
        />
      )}
    </>
  )
}

export default memo(DialogAddSegmentWo)
