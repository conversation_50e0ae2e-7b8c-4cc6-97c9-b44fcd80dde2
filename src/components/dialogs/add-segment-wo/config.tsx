import { ItemType } from '@/pages/repair-and-maintenance/wo/create-wo/config/schema'
import { Grid, IconButton, Tooltip, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { AddItemDto } from '../add-item-segment-wo'
import truncateString from '@/core/utils/truncate'

const partColumnHelper = createColumnHelper<AddItemDto>()
const miscColumnHelper = createColumnHelper<ItemType>()

type RowActionType = {
  detail: (item: ItemType) => void
  edit: (item: ItemType) => void
  delete: (id: string) => void
  viewOnly?: boolean
}

export const partTableColumns = (rowAction: RowActionType) => {
  return [
    partColumnHelper.accessor('number', {
      header: 'Kode Barang'
    }),
    partColumnHelper.accessor('serialNumber', {
      header: 'NO. SERIAL',
      cell: ({ row }) => row.original.serialNumber ?? '-'
    }),
    partColumnHelper.accessor('name', {
      header: 'NAMA ITEM'
    }),
    partColumnHelper.accessor('brandName', {
      header: 'MERK ITEM'
    }),
    partColumnHelper.accessor('largeUnitQuantity', {
      header: 'QTY',
      cell: ({ row }) => row.original.quantity + ' ' + row.original.quantityUnit
    }),
    partColumnHelper.accessor('note', {
      header: 'KETERANGAN',
      cell: ({ row }) => (
        <Tooltip sx={{ cursor: 'default' }} title={row.original.note}>
          <Typography>{truncateString(row.original.note ?? '-', 10)}</Typography>
        </Tooltip>
      )
    }),
    partColumnHelper.display({
      id: 'actions',
      header: 'ACTION',
      cell: ({ row }) => {
        return (
          <Grid container>
            {!rowAction.viewOnly && (
              <>
                <Grid item>
                  <IconButton size='small' onClick={() => rowAction.delete(row.original.id)}>
                    <i className='x-delete-icon text-error' />
                  </IconButton>
                </Grid>
                <Grid item>
                  <IconButton size='small' onClick={() => rowAction.edit(row.original)}>
                    <i className='ri-pencil-fill text-textSecondary' />
                  </IconButton>
                </Grid>
              </>
            )}
            <Grid item>
              <IconButton size='small' onClick={() => rowAction.detail(row.original)}>
                <i className='ri-eye-line text-textSecondary' />
              </IconButton>
            </Grid>
          </Grid>
        )
      }
    })
  ]
}

export const miscTableColumns = (rowAction: RowActionType) => {
  return [
    miscColumnHelper.accessor('number', {
      header: 'Kode Barang'
    }),
    miscColumnHelper.accessor('name', {
      header: 'NAMA ITEM'
    }),
    miscColumnHelper.accessor('brandName', {
      header: 'MERK ITEM'
    }),
    miscColumnHelper.accessor('largeUnitQuantity', {
      header: 'QTY',
      cell: ({ row }) => row.original.quantity + ' ' + row.original.quantityUnit
    }),
    miscColumnHelper.accessor('note', {
      header: 'KETERANGAN',
      cell: ({ row }) => (
        <Tooltip sx={{ cursor: 'default' }} title={row.original.note}>
          <Typography>{truncateString(row.original.note ?? '-', 10)}</Typography>
        </Tooltip>
      )
    }),
    miscColumnHelper.display({
      id: 'actions',
      header: 'ACTION',
      cell: ({ row }) => {
        return (
          <Grid container>
            {!rowAction.viewOnly && (
              <>
                <IconButton size='small' onClick={() => rowAction.delete(row.original.id)}>
                  <i className='x-delete-icon text-error' />
                </IconButton>
                <IconButton size='small' onClick={() => rowAction.edit(row.original)}>
                  <i className='ri-pencil-fill text-textSecondary' />
                </IconButton>
              </>
            )}
            <IconButton size='small' onClick={() => rowAction.detail(row.original)}>
              <i className='ri-eye-line text-textSecondary' />
            </IconButton>
          </Grid>
        )
      }
    })
  ]
}
