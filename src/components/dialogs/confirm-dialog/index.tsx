// React Imports

// MUI Imports
import Dialog from '@mui/material/Dialog'
import Button from '@mui/material/Button'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import DialogActions from '@mui/material/DialogActions'
import { DialogContentText, useMediaQuery, useTheme } from '@mui/material'

type ConfirmDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  onConfirm: () => void
  onCancel?: () => void
  title: string
  content: string
  confirmText: string
  confirmColor?: string
  cancelText?: string
}

const ConfirmDialog = ({
  open,
  setOpen,
  onConfirm,
  onCancel = () => {},
  title,
  content,
  confirmText,
  cancelText = 'Batal',
  confirmColor = 'primary'
}: ConfirmDialogProps) => {
  const theme = useTheme()
  const fullScreen = useMediaQuery(theme.breakpoints.down('md'))

  const handleClose = () => {
    setOpen(false)
    onCancel()
  }

  const handleConfirm = () => {
    setOpen(false)
    onConfirm()
  }

  return (
    <Dialog
      component='form'
      fullScreen={fullScreen}
      open={open}
      onClose={handleClose}
      aria-labelledby='responsive-dialog-title'
    >
      <DialogTitle id='responsive-dialog-title'>{title}</DialogTitle>
      <DialogContent>
        <DialogContentText>{content}</DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button variant='outlined' color='secondary' autoFocus onClick={handleClose}>
          {cancelText}
        </Button>
        <Button
          variant='contained'
          color={confirmColor as 'inherit' | 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning'}
          onClick={handleConfirm}
        >
          {confirmText}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default ConfirmDialog
