// React Imports

// MUI Imports
import Dialog from '@mui/material/Dialog'
import Button from '@mui/material/Button'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import DialogActions from '@mui/material/DialogActions'
import { DialogContentText, useMediaQuery, useTheme } from '@mui/material'
import { ReactNode } from 'react'

export interface MessageDialogProps {
  open: boolean
  onClose?: () => void
  onConfirm?: () => void
  title: string
  content: ReactNode
  confirmText: string
}

const MessageDialog = ({
  open,
  onClose,
  onConfirm,
  title,
  content,
  confirmText = 'Ok<PERSON>, <PERSON><PERSON><PERSON>'
}: MessageDialogProps) => {
  const theme = useTheme()
  const fullScreen = useMediaQuery(theme.breakpoints.down('md'))

  const handleConfirm = () => {
    onConfirm && onConfirm()
    onClose && onClose()
  }

  return (
    <Dialog fullScreen={fullScreen} open={open} onClose={onClose} aria-labelledby='responsive-dialog-title'>
      <DialogTitle id='responsive-dialog-title'>{title}</DialogTitle>
      <DialogContent>
        <DialogContentText>{content}</DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button variant='contained' color={'primary'} onClick={handleConfirm}>
          {confirmText}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default MessageDialog
