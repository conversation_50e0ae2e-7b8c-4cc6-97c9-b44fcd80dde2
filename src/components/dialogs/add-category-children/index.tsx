import { useAddCategory } from '@/api/services/company/mutation'
import { zodResolver } from '@hookform/resolvers/zod'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogProps,
  DialogTitle,
  Grid,
  IconButton,
  TextField,
  Typography
} from '@mui/material'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import { toast } from 'react-toastify'
import { object, string, TypeOf } from 'zod'
import { useCategory } from '@/pages/company-data/category/context/CategoryContext'
import { AvailableCategoryType } from '@/types/exportImportTypes'
import MobileDropDown from '@/components/layout/shared/components/MobileDropDown'

type AddCategoryChildrenDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  type: AvailableCategoryType
}

const addCategoryChildrenSchema = object({
  code: string({ message: 'Wajib diisi' }),
  name: string({ message: 'Wajib diisi' })
})

type AddCategoryChildrenInput = Required<TypeOf<typeof addCategoryChildrenSchema>>

const AddCategoryChildrenDialog = ({ open, setOpen, type }: AddCategoryChildrenDialogProps) => {
  const { selectedParentId, fetchCategoryChildrenData, isMobile } = useCategory()
  const { control, handleSubmit } = useForm<AddCategoryChildrenInput>({
    resolver: zodResolver(addCategoryChildrenSchema)
  })

  const { mutate: addMutate, isLoading } = useAddCategory()

  const onSubmitHandler: SubmitHandler<AddCategoryChildrenInput> = (inputValues: AddCategoryChildrenInput) => {
    addMutate(
      { type, parentId: selectedParentId, ...inputValues },
      {
        onSuccess: () => {
          toast.success('Data jenis kategori berhasil ditambahkan')
          fetchCategoryChildrenData()
          setOpen(false)
        }
      }
    )
  }

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  if (isMobile) {
    return (
      <MobileDropDown open={open} onClose={() => setOpen(false)} onOpen={() => setOpen(true)}>
        <Grid container spacing={5}>
          <Grid item xs={12}>
            <Controller
              name='code'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Kode Jenis'
                  required
                  variant='outlined'
                  placeholder='Masukkkan Kode Jenis'
                  disabled={isLoading}
                  {...(errors.code && { error: true, helperText: errors.code?.message })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              name='name'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Nama Jenis'
                  required
                  variant='outlined'
                  placeholder='Masukkkan Nama Jenis'
                  disabled={isLoading}
                  {...(errors.name && { error: true, helperText: errors.name?.message })}
                />
              )}
            />
          </Grid>
        </Grid>
        <Box className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
          <Button onClick={() => setOpen(false)} variant='outlined' disabled={isLoading} className='is-full sm:is-auto'>
            BATALKAN
          </Button>
          <LoadingButton
            startIcon={<></>}
            loading={isLoading}
            loadingPosition='start'
            variant='contained'
            onClick={handleSubmit(onSubmitHandler)}
            className='px-8 is-full !ml-0 sm:is-auto'
          >
            SIMPAN
          </LoadingButton>
        </Box>
      </MobileDropDown>
    )
  }

  return (
    <Dialog open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Tambah Jenis Unit
        <Typography component='span' className='flex flex-col text-center'>
          Tambahkan jenis unit untuk kategori ini
        </Typography>
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          <Grid item xs={12}>
            <Controller
              name='code'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Kode Jenis'
                  required
                  variant='outlined'
                  placeholder='Masukkkan Kode Jenis'
                  disabled={isLoading}
                  {...(errors.code && { error: true, helperText: errors.code?.message })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              name='name'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Nama Jenis'
                  required
                  variant='outlined'
                  placeholder='Masukkkan Nama Jenis'
                  disabled={isLoading}
                  {...(errors.name && { error: true, helperText: errors.name?.message })}
                />
              )}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button onClick={() => setOpen(false)} variant='outlined' disabled={isLoading} className='is-full sm:is-auto'>
          BATALKAN
        </Button>
        <LoadingButton
          startIcon={<></>}
          loading={isLoading}
          loadingPosition='start'
          variant='contained'
          onClick={handleSubmit(onSubmitHandler)}
          className='px-8 is-full !ml-0 sm:is-auto'
        >
          SIMPAN
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default AddCategoryChildrenDialog
