// React Imports
import { useRef, useState, useEffect } from 'react'
import type { ReactNode } from 'react'

import { useTheme } from '@mui/material/styles'

// MUI Imports
import IconButton from '@mui/material/IconButton'
import Badge from '@mui/material/Badge'
import Popper from '@mui/material/Popper'
import Fade from '@mui/material/Fade'
import Paper from '@mui/material/Paper'
import ClickAwayListener from '@mui/material/ClickAwayListener'
import Typography from '@mui/material/Typography'
import Chip from '@mui/material/Chip'
import Tooltip, { TooltipProps } from '@mui/material/Tooltip'
import Divider from '@mui/material/Divider'
import Avatar from '@mui/material/Avatar'
import useMediaQuery from '@mui/material/useMediaQuery'
import Button from '@mui/material/Button'
import type { Theme } from '@mui/material/styles'

// Third Party Components
import classnames from 'classnames'

// Component Imports
import CustomAvatar from '@/core/components/mui/Avatar'

// Config Imports
import themeConfig from '@/configs/themeConfig'

// Hook Imports
import { useSettings } from '@/core/hooks/useSettings'

// Util Imports
import { getInitials } from '@/utils/getInitials'
import { useNotification } from '@/contexts/NotificationContext'
import { NotificationsType } from '@/types/notificationType'
import { useNotificationRead, useNotificationReadAll } from '@/api/services/notification/mutation'
import { useRouter } from '@/routes/hooks'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import MobileDropDown from './components/MobileDropDown'
import ConditionalWrapping from '@/components/layouts/ConditionalWrapping'

const ScrollWrapper = ({ children }: { children: ReactNode }) => {
  return <div className='overflow-x-hidden bs-full'>{children}</div>
}

const NotificationDropdown = () => {
  const router = useRouter()
  const { isMobile } = useMobileScreen()
  const theme = useTheme()
  const {
    notificationListResponse,
    fetchNotificationList,
    unreadNotificationCount,
    fetchNotificationUnreadCount,
    notificationMeta,
    setNotificationParams
  } = useNotification()

  // States
  const [open, setOpen] = useState(false)
  const [notificationsState, setNotificationsState] = useState([])

  useEffect(() => {
    if (notificationListResponse != null && notificationListResponse != undefined) {
      setNotificationsState(notificationListResponse)
    }
  }, [notificationListResponse])

  // Mutation
  const { mutateAsync: readMutate } = useNotificationRead()
  const { mutateAsync: readAllMutate } = useNotificationReadAll()

  // Vars
  const readAll = notificationsState?.every(notification => notification.read)

  // Refs
  const anchorRef = useRef<HTMLButtonElement>(null)
  const ref = useRef<HTMLDivElement | null>(null)

  // Hooks
  const isSmallScreen = useMediaQuery((theme: Theme) => theme.breakpoints.down('sm'))
  const { settings } = useSettings()

  const handleClose = () => {
    setOpen(false)
  }

  const handleToggle = () => {
    setOpen(prevOpen => {
      if (!prevOpen) fetchNotificationList()
      return !prevOpen
    })
  }

  // Read notification when notification is clicked
  const handleReadNotification = (notificationId: number, appPath: string) => {
    readMutate(notificationId, {
      onSuccess: () => {
        fetchNotificationList()
        fetchNotificationUnreadCount()
      }
    })
    router.push(appPath)
    setOpen(false)
  }

  // Read or unread all notifications when read all icon is clicked
  const readAllNotifications = () => {
    readAllMutate(
      {},
      {
        onSuccess: () => {
          fetchNotificationList()
          fetchNotificationUnreadCount()
        }
      }
    )
  }

  const handleShowMore = () => {
    setNotificationParams(prev => ({ limit: prev.limit + 10 }))
  }

  const content = (placement: TooltipProps['placement']) => (
    <div className='bs-full flex flex-col'>
      <div
        className='flex items-center justify-between py-3 px-4 is-full gap-2 sticky top-0 z-10'
        style={{
          backgroundColor:
            theme.palette.mode === 'dark' ? theme.palette.background.paper : theme.palette.background.default
        }}
      >
        <Typography variant='h6' className='flex-auto'>
          Notifikasi
        </Typography>
        {unreadNotificationCount?.data.unreadCount > 0 && (
          <Chip
            variant='tonal'
            size='small'
            color='primary'
            label={`${unreadNotificationCount?.data.unreadCount} Baru`}
          />
        )}
        <Tooltip
          title={readAll ? 'Semua pesan sudah dibaca' : 'Tandai semua pesan sebagai terbaca'}
          placement={placement === 'bottom-end' ? 'left' : 'right'}
          slotProps={{
            popper: {
              sx: {
                '& .MuiTooltip-tooltip': {
                  transformOrigin: placement === 'bottom-end' ? 'right center !important' : 'right center !important'
                }
              }
            }
          }}
        >
          {notificationsState?.length > 0 ? (
            <IconButton size='small' onClick={() => readAllNotifications()} className='text-textPrimary'>
              <i className={classnames(readAll ? 'ri-mail-line' : 'ri-mail-open-line', 'text-xl')} />
            </IconButton>
          ) : (
            <></>
          )}
        </Tooltip>
        {isMobile && (
          <IconButton onClick={() => setOpen(false)}>
            <i className='ri-close-line text-textSecondary md:hidden' />
          </IconButton>
        )}
      </div>
      <Divider />
      <ConditionalWrapping
        condition={isMobile}
        wrapper={props => <div className='max-h-96 overflow-y-auto'>{props.children}</div>}
      >
        {notificationsState?.length > 0 ? (
          <>
            {notificationsState?.map((notification, index) => {
              const { id, title, subtitle, time, read, appPath } = notification

              return (
                <div
                  key={id}
                  className={classnames('flex py-3 px-4 gap-3 cursor-pointer hover:bg-actionHover group', {
                    'border-be': index !== notificationsState?.length - 1
                  })}
                  onClick={() => handleReadNotification(id, appPath)}
                >
                  <div className='flex flex-col flex-auto'>
                    <Typography variant='body2' className='font-medium' color='text.primary'>
                      {title}
                    </Typography>
                    <Typography variant='caption' className='mbe-1' color='text.secondary'>
                      {subtitle}
                    </Typography>
                    <Typography variant='subtitle2' className='text-xs' color='text.disabled'>
                      {time}
                    </Typography>
                  </div>
                  <div className='flex flex-col items-end gap-2'>
                    <Badge
                      variant='dot'
                      color={'primary'}
                      invisible={read}
                      onClick={() => handleReadNotification(id, appPath)}
                      className={classnames('mbs-1 mie-1')}
                    />
                  </div>
                </div>
              )
            })}
          </>
        ) : (
          <div>
            <Typography className='text-center p-5'>Belum ada notifikasi</Typography>
          </div>
        )}
      </ConditionalWrapping>
      <Divider />
      {notificationMeta?.hasMoreItems && (
        <div className='flex justify-center'>
          <Button sx={{ width: '100%' }} onClick={handleShowMore} variant='text' size='small'>
            Show more
          </Button>
        </div>
      )}
    </div>
  )

  useEffect(() => {
    const adjustPopoverHeight = () => {
      if (ref.current) {
        // Calculate available height, subtracting any fixed UI elements' height as necessary
        const availableHeight = window.innerHeight - 100

        ref.current.style.height = `${Math.min(availableHeight, 550)}px`
      }
    }

    window.addEventListener('resize', adjustPopoverHeight)
  }, [])

  return (
    <>
      <IconButton ref={anchorRef} onClick={handleToggle} className='text-textPrimary'>
        <Badge
          color='error'
          className='cursor-pointer'
          variant='dot'
          overlap='circular'
          invisible={!unreadNotificationCount?.data.unreadCount}
          anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <i className='ri-notification-2-line' />
        </Badge>
      </IconButton>
      {isMobile ? (
        <MobileDropDown open={open} onClose={() => setOpen(false)} onOpen={() => setOpen(true)}>
          {content('bottom-end')}
        </MobileDropDown>
      ) : (
        <Popper
          open={open}
          transition
          disablePortal
          placement='bottom-end'
          ref={ref}
          anchorEl={anchorRef.current}
          {...(isSmallScreen
            ? {
                className: 'is-full !mbs-4 z-[1] max-bs-[550px] bs-[550px]',
                modifiers: [
                  {
                    name: 'preventOverflow',
                    options: {
                      padding: themeConfig.layoutPadding
                    }
                  }
                ]
              }
            : { className: 'is-96 !mbs-4 z-[1] max-bs-[550px] overflow-y-scroll shadow-md rounded-b-lg' })}
        >
          {({ TransitionProps, placement }) => (
            <Fade
              {...TransitionProps}
              style={{ transformOrigin: placement === 'bottom-end' ? 'right top' : 'left top' }}
            >
              <Paper
                className='bs-full'
                sx={{
                  backgroundColor:
                    theme.palette.mode === 'dark' ? theme.palette.background.paper : theme.palette.background.default
                }}
              >
                <ClickAwayListener onClickAway={handleClose}>
                  {content(placement as TooltipProps['placement'])}
                </ClickAwayListener>
              </Paper>
            </Fade>
          )}
        </Popper>
      )}
    </>
  )
}

export default NotificationDropdown
