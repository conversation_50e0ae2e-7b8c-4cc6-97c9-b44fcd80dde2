import { ReactNode } from 'react'
import SwipeableDrawer from '@mui/material/SwipeableDrawer'
import Box from '@mui/material/Box'
import { classNames } from '@/utils/helper'

interface MobileDropDownProps {
  open: boolean
  onClose: () => void
  onOpen: () => void
  children: ReactNode
  className?: string
}

const MobileDropDown = ({ open, onClose, onOpen, children, className }: MobileDropDownProps) => {
  return (
    <SwipeableDrawer
      PaperProps={{ className: 'rounded-t-[16px]' }}
      anchor='bottom'
      open={open}
      onClose={onClose}
      onOpen={onOpen}
    >
      <Box className={classNames('px-2 py-4 pt-2', className)}>{children}</Box>
    </SwipeableDrawer>
  )
}

export default MobileDropDown
