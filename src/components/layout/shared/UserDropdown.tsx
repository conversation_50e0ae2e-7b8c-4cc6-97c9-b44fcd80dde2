// React Imports
import { useMemo, useRef, useState } from 'react'
import type { MouseEvent } from 'react'

// MUI Imports
import { styled } from '@mui/material/styles'
import Badge from '@mui/material/Badge'
import Avatar from '@mui/material/Avatar'
import Popper from '@mui/material/Popper'
import Fade from '@mui/material/Fade'
import Paper from '@mui/material/Paper'
import ClickAwayListener from '@mui/material/ClickAwayListener'
import MenuList from '@mui/material/MenuList'
import Typography from '@mui/material/Typography'
import Divider from '@mui/material/Divider'
import Button from '@mui/material/Button'

// Third-party Imports
// import { signOut, useSession } from 'next-auth/react'
import * as Sentry from '@sentry/react'

// Type Imports

import { useParams } from 'react-router-dom'

// Hook Imports
import { useSettings } from '@/core/hooks/useSettings'

// Util Imports
import { getLocalizedUrl } from '@/utils/i18n'
import { useRouter } from '@/routes/hooks'
import { useUser } from '@/contexts/UserContext'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { AUTH_STORAGE_KEY, useAuth } from '@/contexts/AuthContext'
import {
  Box,
  FormControl,
  FormControlLabel,
  IconButton,
  MenuItem,
  Radio,
  RadioGroup,
  SwipeableDrawer
} from '@mui/material'
import EqRadio from '@/components/menu/components/Radio'
import localforage from 'localforage'
import { MODE_AUTH_KEY } from '@/utils/constants'
import { AuthState } from '@/types/payload'
import { useLocalStorage } from 'react-use'
import { getFromCookies } from '@/utils/storage'
import truncateString from '@/core/utils/truncate'
import mailStringRemover from '@/core/utils/mailStringRemover'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import MobileDropDown from './components/MobileDropDown'

// Styled component for badge content
const BadgeContentSpan = styled('span')({
  width: 8,
  height: 8,
  borderRadius: '50%',
  cursor: 'pointer',
  backgroundColor: 'var(--mui-palette-success-main)',
  boxShadow: '0 0 0 2px var(--mui-palette-background-paper)'
})

const UserDropdown = () => {
  const { clearAllData, userProfile, accounts, activeAccount, setAccount, removeAccount } = useAuth()
  const { setConfirmState } = useMenu()
  // States
  const [open, setOpen] = useState(false)
  const [, setMode] = useLocalStorage<string>(MODE_AUTH_KEY, '')

  // Refs
  const anchorRef = useRef<HTMLDivElement>(null)

  // Hooks
  const router = useRouter()
  const { isMobile } = useMobileScreen()

  // const { data: session } = useSession()
  const session = {
    user: {
      name: '',
      image: ''
    }
  }

  const { settings } = useSettings()
  const { lang: locale } = useParams()

  const handleDropdownOpen = () => {
    !open ? setOpen(true) : setOpen(false)
  }

  const handleDropdownClose = (event?: MouseEvent<HTMLLIElement> | (MouseEvent | TouchEvent), url?: string) => {
    if (url) {
      router.push(getLocalizedUrl(url))
    }

    if (anchorRef.current && anchorRef.current.contains(event?.target as Node)) {
      return
    }

    setOpen(false)
  }

  const handleAddAccount = () => {
    setMode('add')
    window.location.href = '/auth/login'
  }

  const handleUserLogout = async () => {
    setConfirmState({
      open: true,
      title: 'Keluar dari Akun',
      content: 'Kamu yakin ingin keluar dari akun ini?',
      confirmText: 'Keluar',
      onConfirm: () => {
        try {
          removeAccount(activeAccount)
          // Sign out from the app
          // await signOut({ callbackUrl: process.env.NEXT_PUBLIC_APP_URL })
        } catch (error) {
          Sentry.captureException(error)

          // Show above error in a toast like following
          // toastService.error((err as Error).message)
        }
      }
    })
  }

  const arrayAccounts = useMemo(
    () =>
      Object.entries(accounts)
        .map(([key, value]) => ({ email: key, state: value }))
        .filter(account => account.state.authenticated),
    [accounts]
  )

  const menuItems = (
    <MenuList>
      <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
        <i className='ri-close-line text-textSecondary' />
      </IconButton>
      <div className='flex items-center py-2 px-4 gap-2' tabIndex={-1}>
        <Avatar />
        <div className='flex items-start flex-col'>
          <Typography variant='button' className='font-semibold'>
            {userProfile?.fullName}
          </Typography>
          <Typography variant='caption' color='primary'>
            {userProfile?.title}
          </Typography>
          <Typography variant='caption'>{userProfile?.department?.name}</Typography>
        </div>
      </div>
      <Divider className='mlb-1' />
      <MenuItem disabled className='gap-3 px-4 flex flex-col justify-start items-start'>
        <Typography color='text.primary'>Akun Perusahaan</Typography>
      </MenuItem>
      <div className='px-4 space-y-2'>
        <FormControl className='mb-4 md:mb-0' fullWidth>
          <RadioGroup
            onChange={(_, value) => setAccount(mailStringRemover(value)).then(() => (window.location.href = '/'))}
            defaultValue={activeAccount || arrayAccounts?.[0]?.email}
            className='flex flex-col gap-1'
          >
            {arrayAccounts.map(({ email, state }) => (
              <FormControlLabel
                className='w-full bg-[#f6f6f6] dark:bg-inherit dark:border rounded-[10px] flex flex-row-reverse justify-between m-0 p-2 pl-4'
                control={<EqRadio />}
                value={email}
                label={(state?.companyCode as string)?.toLocaleUpperCase()}
              />
            ))}
          </RadioGroup>
        </FormControl>
        <Button size='small' onClick={handleAddAccount} fullWidth variant='outlined'>
          Tambah Akun
        </Button>
      </div>
      <Divider className='my-4 mb-2 mx-4' />
      <div className='flex items-center py-1.5 px-4'>
        <Button
          fullWidth
          variant='outlined'
          color='error'
          size='small'
          endIcon={<i className='ri-logout-box-r-line' />}
          onClick={handleUserLogout}
        >
          Keluar dari Akun
        </Button>
      </div>
    </MenuList>
  )

  return (
    <>
      <Badge
        ref={anchorRef}
        overlap='circular'
        badgeContent={<BadgeContentSpan onClick={handleDropdownOpen} />}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        className='mis-2'
      >
        <Avatar
          ref={anchorRef}
          alt={session?.user?.name || ''}
          src={session?.user?.image || ''}
          onClick={handleDropdownOpen}
          className='cursor-pointer bs-[38px] is-[38px]'
        />
      </Badge>
      {isMobile ? (
        <MobileDropDown open={open} onClose={() => setOpen(false)} onOpen={() => setOpen(true)}>
          {menuItems}
        </MobileDropDown>
      ) : (
        <Popper
          open={open}
          transition
          disablePortal
          placement='bottom-end'
          anchorEl={anchorRef.current}
          className='min-is-[240px] !mbs-4 z-[1]'
        >
          {({ TransitionProps, placement }) => (
            <Fade
              {...TransitionProps}
              style={{
                transformOrigin: placement === 'bottom-end' ? 'right top' : 'left top'
              }}
            >
              <Paper
                elevation={settings.skin === 'bordered' ? 0 : 8}
                {...(settings.skin === 'bordered' && { className: 'border' })}
              >
                <ClickAwayListener onClickAway={e => handleDropdownClose(e as MouseEvent | TouchEvent)}>
                  {menuItems}
                </ClickAwayListener>
              </Paper>
            </Fade>
          )}
        </Popper>
      )}
    </>
  )
}

export default UserDropdown
