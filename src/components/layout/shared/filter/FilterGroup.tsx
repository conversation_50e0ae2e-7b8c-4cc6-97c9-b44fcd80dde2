import { useEffect, useMemo, useState } from 'react'
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Chip,
  Select,
  MenuItem,
  InputLabel,
  FormControl,
  Button,
  Typography,
  IconButton,
  Grid,
  DialogProps,
  OutlinedInput,
  SelectChangeEvent,
  SelectProps,
  Autocomplete,
  debounce,
  CircularProgress
} from '@mui/material'
import DateRangePicker from '@/components/DateRangePicker'
import { useQuery } from '@tanstack/react-query'
import CompanyQueryMethods, { UNIT_LIST_QUERY_KEY } from '@/api/services/company/query'
import { UnitType } from '@/types/companyTypes'

export type FieldGroupType =
  | 'date'
  | 'status'
  | 'priority'
  | 'category'
  | 'site'
  | 'department'
  | 'requestedSiteIds'
  | 'requesterSiteIds'
  | 'unit'
  | 'project'

export type FilterGroupFieldConfig = {
  options: readonly { value: string; label: string; color?: string }[]
  values: readonly string[]
  required?: boolean
  default?: readonly string[]
}

export type FilterGroupConfig = Partial<Record<FieldGroupType, FilterGroupFieldConfig>>
export type FilterValues = Partial<Record<FieldGroupType, string[]>>

const FilterButton = ({ onClick, active }: { onClick: () => void; active: boolean }) => {
  const label = active ? 'Filter aktif' : 'Filter tidak aktif'
  return (
    <Button
      color={active ? 'primary' : 'secondary'}
      variant='outlined'
      onClick={onClick}
      startIcon={<i className='ri-equalizer-line' />}
      className='is-full sm:is-auto'
    >
      {label}
    </Button>
  )
}

type MultiSelectFilterProps = {
  label: string
  config: FilterGroupFieldConfig
  onValueChange: (value: string[]) => void
  emptyOptionLabel?: string
} & SelectProps<string[]>

const MultiSelectFilter = (props: MultiSelectFilterProps) => {
  const {
    label,
    config: { options, required },
    onValueChange,
    emptyOptionLabel,
    id,
    multiple,
    value,
    ...rest
  } = props

  const handleChange = (event: SelectChangeEvent<string | string[]>) => {
    const value = (event.target as HTMLInputElement).value
    if (typeof value === 'string') {
      onValueChange(value ? [value] : [])
    } else {
      onValueChange(value)
    }
  }
  return (
    <FormControl fullWidth>
      <InputLabel id={`${id}-label`}>{label}</InputLabel>
      <Select
        {...rest}
        labelId={`${id}-label`}
        id={id}
        multiple={multiple}
        value={value}
        input={<OutlinedInput id='select-multiple-chip' label={label} />}
        onChange={handleChange}
        renderValue={selected => {
          if (multiple && Array.isArray(selected) && selected.length > 0) {
            return (
              <div className='flex gap-2 flex-wrap'>
                {selected.map(value => (
                  <Chip key={value} label={options.find(option => option.value === value)?.label} />
                ))}
              </div>
            )
          } else if (!multiple) {
            if (Array.isArray(selected)) {
              selected = selected[0]
            }
            const selectedOption = options.find(option => option.value === selected)
            return selectedOption?.label ?? ''
          }
          return ''
        }}
      >
        {!required && emptyOptionLabel && <MenuItem value=''>{emptyOptionLabel}</MenuItem>}
        {options.map(({ value, label }) => (
          <MenuItem key={value} value={value}>
            {label}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  )
}

type AutocompleteProps<T> = {
  optionsList: T[]
  item: T
  setSearchQuery: (value: string) => void
  searchQuery?: string
  onChange: (value: any[]) => void
  loadingFetch: boolean
  label: string
  selectedItem?: T
  setSelectedItem?: (value: T) => void
}

const AutocompleteField = <T,>({
  optionsList,
  setSearchQuery,
  item,
  onChange,
  loadingFetch,
  label,
  selectedItem,
  setSelectedItem
}: AutocompleteProps<T>) => {
  return (
    <Autocomplete
      key={JSON.stringify({ item })}
      filterOptions={x => x}
      isOptionEqualToValue={(option, value) => (option as any)?.id === (value as any)?.id}
      onInputChange={debounce((e, newValue, reason) => {
        if (reason === 'input') {
          setSearchQuery(newValue)
        }
      }, 700)}
      options={optionsList || []}
      freeSolo
      onChange={(e, newValue: T) => {
        if (newValue) {
          setSelectedItem(newValue)
          onChange([(newValue as any)?.id ?? ''])
        }
      }}
      value={selectedItem}
      noOptionsText='Data tidak ditemukan'
      loading={loadingFetch}
      renderInput={params => (
        <TextField
          {...params}
          label={label}
          placeholder={`Masukkan ${label}`}
          variant='outlined'
          InputProps={{
            ...params.InputProps,
            endAdornment: <>{loadingFetch ? <CircularProgress /> : null}</>,
            onKeyDown: e => {
              if (e.key === 'Enter') {
                e.stopPropagation()
              }
            }
          }}
        />
      )}
      getOptionLabel={(option: T) =>
        `${(option as any)?.number || (option as any)?.code} - ${(option as any)?.name || (option as any)?.type} ${(option as any)?.address ?? ''}`
      }
      className='flex-1'
      renderOption={(props, option) => {
        const { key, ...optionProps } = props
        return (
          <li key={key} {...optionProps}>
            <Typography>
              {(option as any)?.number || (option as any)?.code} - {(option as any)?.name || (option as any)?.type}{' '}
              {(option as any)?.address ?? ''}
            </Typography>
          </li>
        )
      }}
    />
  )
}

type FilterGroupDialogProps = {
  config: FilterGroupConfig
  onFilterApplied: (values: FilterValues) => void
  onRemoveFilter?: (values: FilterValues) => void
  defaultActive?: boolean
}

const emptyFilterValues: FilterValues = {
  date: [undefined, undefined],
  status: [],
  priority: [],
  category: [],
  site: [],
  requestedSiteIds: [],
  requesterSiteIds: [],
  department: [],
  unit: []
}

const FilterGroupDialog = ({
  config,
  onFilterApplied,
  defaultActive,
  onRemoveFilter = () => {}
}: FilterGroupDialogProps) => {
  const [open, setOpen] = useState(false)

  const getDefault = (key: FieldGroupType) => {
    return config[key]?.default?.slice() ?? emptyFilterValues[key]
  }

  const [dateRange, setDateRange] = useState<string[]>([])
  const [status, setStatus] = useState<string[]>([])
  const [priorities, setPriorities] = useState<string[]>([])
  const [categories, setCategories] = useState<string[]>([])
  const [site, setSite] = useState<string[]>([])
  const [requestedSiteIds, setRequestedSiteIds] = useState<string[]>([])
  const [requesterSiteIds, setRequesterSiteIds] = useState<string[]>([])
  const [departments, setDepartments] = useState<string[]>([])
  const [units, setUnits] = useState<string[]>([])
  const [projects, setProjects] = useState<string[]>([])

  const filterValues: FilterValues = useMemo(() => {
    return {
      date: dateRange,
      status,
      priority: priorities,
      category: categories,
      site,
      requestedSiteIds,
      requesterSiteIds,
      department: departments,
      unit: units,
      project: projects
    } satisfies FilterValues
  }, [dateRange, status, priorities, categories, site, departments, requestedSiteIds, requesterSiteIds, units])

  const [filterEnabled, setFilterEnabled] = useState(false)
  const [active, setActive] = useState(
    defaultActive ??
      Object.keys(filterValues).some(
        key => JSON.stringify(filterValues[key] ?? []) !== JSON.stringify(getDefault(key as FieldGroupType))
      )
  )

  const handleToggle = () => setOpen(!open)

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  const handleApplyFilter = () => {
    onFilterApplied(filterValues)
    const notDefault = Object.keys(filterValues).some(
      key => JSON.stringify(filterValues[key] ?? []) !== JSON.stringify(getDefault(key as FieldGroupType))
    )
    setActive(notDefault)
    setOpen(false)
  }

  const handleRemoveFilter = () => {
    setDateRange(emptyFilterValues.date)
    setStatus(getDefault('status'))
    setPriorities(getDefault('priority'))
    setCategories(getDefault('category'))
    setSite(getDefault('site'))
    setDepartments(getDefault('department'))
    setRequestedSiteIds(getDefault('requestedSiteIds'))
    setRequesterSiteIds(getDefault('requesterSiteIds'))
    setUnits(getDefault('unit'))
    if (onRemoveFilter) {
      onRemoveFilter({
        date: emptyFilterValues.date,
        status: getDefault('status'),
        priority: getDefault('priority'),
        category: getDefault('category'),
        site: getDefault('site'),
        department: getDefault('department'),
        requestedSiteIds: getDefault('requestedSiteIds'),
        requesterSiteIds: getDefault('requesterSiteIds'),
        unit: getDefault('unit')
      })
      setOpen(false)
    }
  }

  const handleCancel = () => {
    setDateRange(config.date?.values.slice())
    setStatus(config.status?.values.slice())
    setPriorities(config.priority?.values.slice())
    setCategories(config.category?.values.slice())
    setSite(config.site?.values.slice())
    setDepartments(config.department?.values.slice())
    setRequestedSiteIds(config.requestedSiteIds?.values.slice())
    setRequesterSiteIds(config.requesterSiteIds?.values.slice())
    setUnits(config.unit?.values.slice())
    setOpen(false)
  }

  const isFilterEnabled = useMemo(
    () =>
      Object.keys(filterValues).some(
        key => JSON.stringify(filterValues[key] ?? []) !== JSON.stringify(getDefault(key as FieldGroupType))
      ),
    [filterValues, config]
  )

  useEffect(() => {
    setFilterEnabled(isFilterEnabled)
  }, [isFilterEnabled])

  useEffect(() => {
    setDateRange([config.date?.values?.[0], config.date?.values?.[1]])
    setStatus(config.status?.values.slice() ?? [])
    setPriorities(config.priority?.values.slice() ?? [])
    setCategories(config.category?.values.slice() ?? [])
    setSite(config.site?.values.slice() ?? [])
    setRequestedSiteIds(config.requestedSiteIds?.values.slice() ?? [])
    setRequesterSiteIds(config.requesterSiteIds?.values.slice() ?? [])
    setDepartments(config.department?.values.slice() ?? [])
    setUnits(config.unit?.values.slice() ?? [])
    setProjects(config.project?.values.slice() ?? [])
  }, [config])

  /* ==== Selection for unit ==== */
  const [unitQueryKey, setUnitQueryKey] = useState<string>('')
  const [selectedUnit, setSelectedUnit] = useState<UnitType | null>(null)

  const { data: unitListRes, isFetching: loadingUnit } = useQuery({
    enabled: !!config?.unit && (!!unitQueryKey || units.length > 0),
    queryKey: [UNIT_LIST_QUERY_KEY, unitQueryKey, units],
    queryFn: () =>
      CompanyQueryMethods.getUnitList({ limit: Number.MAX_SAFE_INTEGER, search: unitQueryKey || units?.[0] })
  })

  useEffect(() => {
    if (unitListRes?.items?.length > 0) {
      setSelectedUnit(units?.length > 0 ? unitListRes?.items?.find(unit => unit.id === units[0]) : null)
    }
  }, [units, unitListRes])
  /* ==== End of Selection for unit ==== */

  return (
    <>
      <FilterButton onClick={handleToggle} active={isFilterEnabled} />

      <Dialog open={open} onClose={handleClose} fullWidth maxWidth='sm'>
        <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
          Filter
          <Typography component='span' className='flex flex-col text-center'>
            Atur filter yang akan kamu terapkan
          </Typography>
        </DialogTitle>{' '}
        <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
          <IconButton onClick={handleCancel} className='absolute block-start-4 inline-end-4'>
            <i className='ri-close-line text-textSecondary' />
          </IconButton>
          <Grid container spacing={5}>
            <Grid item xs={12}>
              <DateRangePicker
                inputSize='medium'
                startDate={dateRange[0]}
                endDate={dateRange[1]}
                onChange={(start, end) => {
                  setDateRange([start, end])
                }}
              />
            </Grid>
            {config.status && (
              <Grid item xs={12}>
                <MultiSelectFilter
                  id='status'
                  label='Status'
                  config={config.status}
                  value={status}
                  onValueChange={setStatus}
                  emptyOptionLabel='Semua Status'
                />
              </Grid>
            )}
            {config.unit && (
              <Grid item xs={12}>
                <AutocompleteField
                  label='Unit'
                  item={selectedUnit}
                  loadingFetch={loadingUnit}
                  optionsList={unitListRes?.items ?? []}
                  setSearchQuery={setUnitQueryKey}
                  searchQuery={unitQueryKey}
                  onChange={setUnits}
                  selectedItem={selectedUnit}
                  setSelectedItem={setSelectedUnit}
                />
                {/* <MultiSelectFilter
                  id='unit'
                  label='Unit'
                  config={config.unit}
                  value={units}
                  onValueChange={setUnits}
                  emptyOptionLabel='Semua Unit'
                /> */}
              </Grid>
            )}
            {config.priority && (
              <Grid item xs={12}>
                <MultiSelectFilter
                  id='priority'
                  label='Prioritas'
                  config={config.priority}
                  value={priorities}
                  onValueChange={setPriorities}
                  emptyOptionLabel='Semua Prioritas'
                />
              </Grid>
            )}
            {config.category && (
              <Grid item xs={12}>
                <MultiSelectFilter
                  id='category'
                  label='Kategori'
                  config={config.category}
                  value={categories}
                  onValueChange={setCategories}
                  emptyOptionLabel='Semua Kategori'
                />
              </Grid>
            )}
            {config.site && (
              <Grid item xs={12}>
                <MultiSelectFilter
                  id='site'
                  label='Lokasi Site'
                  config={config.site}
                  value={site}
                  onValueChange={setSite}
                  emptyOptionLabel='Semua Site'
                />
              </Grid>
            )}
            {config.requestedSiteIds && (
              <Grid item xs={12}>
                <MultiSelectFilter
                  id='requestedSiteIds'
                  label='Gudang Asal'
                  config={config.requestedSiteIds}
                  value={requestedSiteIds}
                  onValueChange={setRequestedSiteIds}
                  emptyOptionLabel='Semua Gudang'
                />
              </Grid>
            )}
            {config.requesterSiteIds && (
              <Grid item xs={12}>
                <MultiSelectFilter
                  id='requesterSiteIds'
                  label='Gudang Tujuan'
                  config={config.requesterSiteIds}
                  value={requesterSiteIds}
                  onValueChange={setRequesterSiteIds}
                  emptyOptionLabel='Semua Gudang'
                />
              </Grid>
            )}
            {config.department && (
              <Grid item xs={12}>
                <MultiSelectFilter
                  id='department'
                  label='Departemen'
                  config={config.department}
                  value={departments}
                  onValueChange={setDepartments}
                  emptyOptionLabel='Semua Departemen'
                />
              </Grid>
            )}
            {config.project && (
              <Grid item xs={12}>
                <MultiSelectFilter
                  id='project'
                  label='Project'
                  config={config.project}
                  value={projects}
                  onValueChange={setProjects}
                  emptyOptionLabel='Semua Project'
                />
              </Grid>
            )}
          </Grid>
        </DialogContent>
        <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
          <Button onClick={handleRemoveFilter} color='secondary' className='is-full sm:is-auto'>
            Hapus Filter
          </Button>
          <Button
            onClick={handleApplyFilter}
            variant='contained'
            color='primary'
            className='px-8 is-full !ml-0 sm:is-auto'
            disabled={!filterEnabled}
          >
            Terapkan Filter
          </Button>
        </DialogActions>
      </Dialog>
    </>
  )
}

export default FilterGroupDialog
