// Next Imports

// MUI Imports
import { useTheme } from '@mui/material/styles'

// Third-party Imports
import PerfectScrollbar from 'react-perfect-scrollbar'

// Type Imports
import type { VerticalMenuContextProps } from '@/components/menu/components/Menu'

// Component Imports
import { Menu } from '@/components/menu'

// import { GenerateVerticalMenu } from '@/components/GenerateMenu'

// Hook Imports
import useVerticalNav from '@/components/menu/hooks/useVerticalNav'

// Styled Component Imports
import StyledVerticalNavExpandIcon from '@/components/menu/styles/vertical/StyledVerticalNavExpandIcon'

// Style Imports
import menuItemStyles from '@/core/styles/vertical/menuItemStyles'
import menuSectionStyles from '@/core/styles/vertical/menuSectionStyles'

import { useAuth } from '@/contexts/AuthContext'
import { MenuDataType } from '@/types/menuTypes'
import { GenerateVerticalMenu } from '@/components/GenerateMenu'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import { useMenu } from '@/components/menu/contexts/menuContext'

// Menu Data Imports
// import menuData from '@/data/navigation/verticalMenuData'

type RenderExpandIconProps = {
  open?: boolean
  transitionDuration?: VerticalMenuContextProps['transitionDuration']
}

type Props = {
  scrollMenu: (container: any, isPerfectScrollbar: boolean) => void
}

const RenderExpandIcon = ({ open, transitionDuration }: RenderExpandIconProps) => (
  <StyledVerticalNavExpandIcon open={open} transitionDuration={transitionDuration}>
    <i className='ri-arrow-right-s-line' />
  </StyledVerticalNavExpandIcon>
)

const SidebarMenu = ({ scrollMenu }: Props) => {
  const theme = useTheme()
  const verticalNavOptions = useVerticalNav()
  const { isBreakpointReached } = useVerticalNav()
  const { approvalCounts } = useAuth()

  const { menuData } = useMenu()

  // Vars
  const { transitionDuration } = verticalNavOptions

  const ScrollWrapper = isBreakpointReached ? 'div' : PerfectScrollbar

  return (
    <Menu
      popoutMenuOffset={{ mainAxis: 17 }}
      menuItemStyles={menuItemStyles(verticalNavOptions, theme)}
      renderExpandIcon={({ open }) => <RenderExpandIcon open={open} transitionDuration={transitionDuration} />}
      renderExpandedMenuItemIcon={{ icon: <i className='ri-circle-fill' /> }}
      menuSectionStyles={menuSectionStyles(verticalNavOptions, theme)}
    >
      <GenerateVerticalMenu approvalCounts={approvalCounts} menuData={menuData} />
    </Menu>
  )
}

export default SidebarMenu
