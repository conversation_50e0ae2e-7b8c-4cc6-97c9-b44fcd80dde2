// MUI Imports
import Button from '@mui/material/Button'

import type { ChildrenType } from '@/core/types'

// Layout Imports

// Component Imports
import Navigation from '@/components/layout/sidebar/Navigation'
import Navbar from '@/components/layout/sidebar/Navbar'
import ScrollToTop from '@/core/components/scroll-to-top'
import AuthGuard from '@/routes/AuthGuard'

// Config Imports

// Util Imports
import { getMode, getSystemMode } from '@/core/utils/serverHelpers'
import VerticalLayout from '../layouts/VerticalLayout'
import { NotificationContextProvider } from '@/contexts/NotificationContext'

const MainLayout = ({ children }: ChildrenType) => {
  const mode = getMode()
  const systemMode = getSystemMode()

  return (
    <AuthGuard>
      <NotificationContextProvider>
        <VerticalLayout navigation={<Navigation mode={mode} systemMode={systemMode} />} navbar={<Navbar />}>
          {children}
        </VerticalLayout>
        <ScrollToTop className='mui-fixed'>
          <Button
            variant='contained'
            className='is-10 bs-10 rounded-full p-0 min-is-0 flex items-center justify-center'
          >
            <i className='ri-arrow-up-line' />
          </Button>
        </ScrollToTop>
      </NotificationContextProvider>
    </AuthGuard>
  )
}

export default MainLayout
