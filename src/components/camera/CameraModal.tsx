/* eslint-disable @typescript-eslint/no-explicit-any */
import { useInnerHeight } from '@/utils/hooks/useInnerHeight'
import { useRef } from 'react'
import { CameraType, FacingMode } from '../camera/camera-types'
import { useCamera } from '../camera/camera-provider'
import { CameraView } from '../camera/camera-view'
import { Dialog, IconButton } from '@mui/material'

interface ModalProps {
  open: boolean
  onCaptured: (imageData: string) => void
  onClose: () => void
  facingMode: FacingMode
}

const CameraModal = ({ open, onCaptured, onClose, facingMode }: ModalProps) => {
  const innerHeight = useInnerHeight()
  const camera = useRef<CameraType>()
  const { stopStream } = useCamera()

  const handleCapture = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault()
    if (camera.current) {
      const imageData = camera.current.takePhoto()
      if (imageData) {
        stopStream()
        onCaptured(imageData)
      }
    }
  }

  const handleOnClosed = () => {
    stopStream()
    onClose()
  }

  return (
    <Dialog fullScreen open={open} onClose={handleOnClosed}>
      <div
        className='fixed bottom-0 flex w-screen flex-col items-center gap-6 overflow-auto bg-white'
        style={{
          height: innerHeight,
          maxHeight: innerHeight
        }}
      >
        <button className='hidden' />
        <IconButton className='absolute right-6 top-5 z-10 rounded-full bg-white/50 p-2' onClick={handleOnClosed}>
          <i className='ri-close-line ' />
        </IconButton>
        <CameraView ref={camera} facingMode={facingMode} />
        <IconButton className='absolute bottom-6 z-10 size-20 p-2 bg-white/50' color='inherit' onClick={handleCapture}>
          <i className='ri-camera-line text-white size-12' />
        </IconButton>
      </div>
    </Dialog>
  )
}

export default CameraModal
