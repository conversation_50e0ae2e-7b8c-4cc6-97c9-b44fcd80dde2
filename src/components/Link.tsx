// React Imports
import { forwardRef } from 'react'
import type { ComponentProps, ForwardedRef, MouseEvent } from 'react'
import { NavLink } from 'react-router-dom'

const Link = (props: ComponentProps<typeof Link>, ref: ForwardedRef<HTMLAnchorElement>) => {
  // Props
  const { href, onClick, ...rest } = props

  return (
    <NavLink
      ref={ref}
      {...rest}
      to={href || '/'}
      onClick={onClick ? e => onClick(e) : !href ? e => e.preventDefault() : undefined}
    />
  )
}

export default forwardRef(Link)
