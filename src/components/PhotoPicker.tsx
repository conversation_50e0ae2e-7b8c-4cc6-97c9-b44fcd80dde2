import React, { useEffect, useState } from 'react'
import { useFormContext } from 'react-hook-form'
import { useFilePicker } from 'use-file-picker'
import { PhotoProvider, PhotoView } from 'react-photo-view'
import 'react-photo-view/dist/react-photo-view.css'
import imageCompression from 'browser-image-compression'
import { CircularProgress } from '@mui/material'

type Props = {
  fieldName?: string
  content?: string
  onPicked?: (content: string, fileName?: string) => void
  onRemoved?: () => void
  isPreview?: boolean
  disabled?: boolean
}

const PhotoPicker = ({ fieldName, content, onPicked, onRemoved, isPreview, disabled }: Props) => {
  const formMethod = useFormContext()
  const [isLoading, setIsLoading] = useState(false)
  const contentWatcher = fieldName ? formMethod.watch(fieldName, '') : content

  const { openFilePicker, filesContent } = useFilePicker({
    multiple: false,
    accept: 'image/*',
    readAs: 'DataURL'
  })

  const compressImage = async (filesContent: any) => {
    setIsLoading(true)
    try {
      const contentFile = await imageCompression.getFilefromDataUrl(
        filesContent[0]?.content ?? '',
        'item-photo.jpg',
        Date.now()
      )
      const compressedFile = await imageCompression(contentFile, {
        maxSizeMB: 1,
        useWebWorker: true
      })
      const compressedContent = await imageCompression.getDataUrlFromFile(compressedFile)
      onPicked?.(compressedContent, filesContent[0]?.name ?? '')
    } catch (error) {
      console.log(error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (filesContent?.length > 0) {
      if (fieldName) {
        formMethod.setValue(fieldName, filesContent[0]?.content ?? '')
      }
      compressImage(filesContent)
    }
  }, [filesContent, fieldName])

  return contentWatcher ? (
    <div className='size-20 relative'>
      <PhotoProvider>
        <PhotoView src={contentWatcher}>
          <img className='size-20 rounded-3xl object-cover cursor-pointer' src={contentWatcher} />
        </PhotoView>
      </PhotoProvider>
      {!isPreview && (
        <div
          onClick={() => {
            if (!disabled) {
              if (fieldName) {
                formMethod.setValue(fieldName, '')
              } else {
                onRemoved?.()
              }
            }
          }}
          className='absolute flex cursor-pointer items-center justify-center bottom-0 right-0 size-6 rounded-full bg-red-500'
        >
          <i className='ri-delete-bin-2-fill bg-white size-4' />
        </div>
      )}
    </div>
  ) : (
    <div
      className='size-20 cursor-pointer bg-[#4c4e64]/[12%] rounded-3xl flex items-center justify-center max-sm:min-w-20'
      onClick={() => {
        if (!disabled) {
          openFilePicker()
        }
      }}
    >
      {isLoading ? <CircularProgress /> : <i className='ri-add-large-fill' />}
    </div>
  )
}

export default PhotoPicker
