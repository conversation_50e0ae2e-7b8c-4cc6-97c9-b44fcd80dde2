import React from 'react'

import type { NumericFormatProps } from 'react-number-format'
import { NumericFormat } from 'react-number-format'
import NumberField from './NumberField'

interface CustomProps extends Partial<Omit<NumericFormatProps, 'onChange'>> {
  onChange: (event: { target: { name: string; value: number } }) => void
  name: string
  prefix?: string
}

const CurrencyField = React.forwardRef<NumericFormatProps, CustomProps>(function CurrencyField(props, ref) {
  return (
    <NumberField {...props} ref={ref} decimalSeparator=',' thousandSeparator='.' prefix={`${props.prefix ?? 'Rp'} `} />
  )
})

export default CurrencyField
