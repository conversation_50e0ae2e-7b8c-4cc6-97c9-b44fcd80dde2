import React from 'react'

import type { NumericFormatProps } from 'react-number-format'
import { NumericFormat } from 'react-number-format'

interface CustomProps extends Partial<Omit<NumericFormatProps, 'onChange'>> {
  onChange: (event: { target: { name: string; value: number } }) => void
  name: string
}

const NumberField = React.forwardRef<NumericFormatProps, CustomProps>(function CurrencyField(props, ref) {
  const { onChange, ...other } = props

  return (
    <NumericFormat
      getInputRef={ref}
      onValueChange={values => {
        onChange({
          target: {
            name: props.name,
            value: values.floatValue
          }
        })
      }}
      allowLeadingZeros={false}
      valueIsNumericString={false}
      allowNegative={false}
      {...other}
    />
  )
})

export default NumberField
