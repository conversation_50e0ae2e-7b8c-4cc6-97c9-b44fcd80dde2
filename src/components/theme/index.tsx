// React Imports
import { useMemo } from 'react'

// MUI Imports
import type {} from '@mui/lab/themeAugmentation' //! Do not remove this import otherwise you will get type errors while making a production build
// import { AppRouterCacheProvider } from '@mui/material-nextjs/v14-appRouter'
import CssBaseline from '@mui/material/CssBaseline'
import {
  Experimental_CssVarsProvider as CssVarsProvider,
  experimental_extendTheme as extendTheme
} from '@mui/material/styles'
import type { Theme } from '@mui/material/styles'
import type {} from '@mui/material/themeCssVarsAugmentation' //! Do not remove this import otherwise you will get type errors while making a production build
import { deepmerge } from '@mui/utils'
import { useMedia } from 'react-use'
import stylisRTLPlugin from 'stylis-plugin-rtl'

import type { ChildrenType, SystemMode } from '@/core/types'

// Component Imports
import ModeChanger from './ModeChanger'

// Config Imports
import themeConfig from '@/configs/themeConfig'

// Hook Imports
import { useSettings } from '@/core/hooks/useSettings'
import useMobileScreen from '../dialogs/hooks/useMobileScreen'

// Core Theme Imports
import defaultCoreTheme from '@/core/theme'

// import mergedTheme from './mergedTheme'

type Props = ChildrenType & {
  systemMode: SystemMode
}

const ThemeProvider = (props: Props) => {
  // Props
  const { children, systemMode } = props

  // Hooks
  const { settings } = useSettings()
  const { isMobile } = useMobileScreen()
  const isDark = useMedia('(prefers-color-scheme: dark)', systemMode === 'dark')

  // Vars
  const isServer = typeof window === 'undefined'
  let currentMode: SystemMode

  if (isServer) {
    currentMode = systemMode
  } else {
    if (settings.mode === 'system') {
      currentMode = isDark ? 'dark' : 'light'
    } else {
      currentMode = settings.mode as SystemMode
    }
  }

  // Merge the primary color scheme override with the core theme
  const theme = useMemo(() => {
    const newColorScheme = {
      colorSchemes: {
        light: {
          palette: {
            primary: {
              main: '#4BD88B',
              light: '#4BD88B',
              dark: '#4BD88B',
              contrastText: '#fff'
            }
          }
        },
        dark: {
          palette: {
            primary: {
              main: '#4BD88B',
              light: '#4BD88B',
              dark: '#4BD88B',
              contrastText: '#fff'
            }
          }
        }
      },
      components: {
        MuiDialog: {
          defaultProps: {
            fullScreen: isMobile
          }
        },
        MuiTextField: {
          defaultProps: {
            size: isMobile ? 'small' : 'medium'
          }
        },
        MuiSelect: {
          defaultProps: {
            size: isMobile ? 'small' : 'medium'
          }
        }
      }
    }

    const coreTheme = deepmerge(defaultCoreTheme(settings, currentMode), newColorScheme)

    return extendTheme(coreTheme)

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [settings.primaryColor, settings.skin, currentMode, isMobile])

  return (
    <CssVarsProvider
      theme={theme}
      defaultMode={systemMode}
      modeStorageKey={`${themeConfig.appName.toLowerCase().split(' ').join('-')}-mui-theme-mode`}
    >
      <>
        <ModeChanger systemMode={systemMode} />
        <CssBaseline />
        {children}
      </>
    </CssVarsProvider>
  )
}

export default ThemeProvider
