// Type Imports
import { redirect } from 'react-router-dom'

import type { Locale } from '@/configs/i18n'

// Config Imports
import themeConfig from '@/configs/themeConfig'
import { usePathname } from '@/routes/hooks'

// Util Imports
import { getLocalizedUrl } from '@/utils/i18n'

const AuthRedirect = () => {
  const pathname = usePathname()

  // ℹ️ Bring me `lang`
  const redirectUrl = `/login?redirectTo=${pathname}`
  const login = `/login`
  const homePage = getLocalizedUrl(themeConfig.homePageUrl)

  return redirect(pathname === login ? login : pathname === homePage ? login : redirectUrl)
}

export default AuthRedirect
