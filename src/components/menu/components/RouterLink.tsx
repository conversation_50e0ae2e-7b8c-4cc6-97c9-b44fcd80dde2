// React Imports
import { forwardRef } from 'react'

// Type Imports
import type { ChildrenType } from '../types'
import { Link, LinkProps } from 'react-router-dom'

type RouterLinkProps = LinkProps &
  Partial<ChildrenType> & {
    className?: string
  }

export const RouterLink = forwardRef((props: RouterLinkProps, ref: any) => {
  // Props
  const { to, className, ...other } = props

  return (
    <Link ref={ref} to={to} className={className} {...other}>
      {props.children}
    </Link>
  )
})
