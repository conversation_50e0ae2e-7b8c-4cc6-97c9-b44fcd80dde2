// src/components/Loading.tsx
import React from 'react'
import CircularProgress from '@mui/material/CircularProgress'
import Box from '@mui/material/Box'
import { Typography } from '@mui/material'

const Loading: React.FC = () => {
  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        flexDirection: 'column',
        gap: '24px'
      }}
    >
      <CircularProgress />
      <Typography>Loading...</Typography>
    </Box>
  )
}

export default Loading
