import React, { forwardRef, useEffect, useState } from 'react'
import AppReactDatepicker from './libs/styles/AppReactDatepicker'
import { formatDate, isEqual, setHours, setMinutes, toDate } from 'date-fns'
import { TextField, TextFieldProps } from '@mui/material'
import useMobileScreen from './dialogs/hooks/useMobileScreen'

type CustomInputProps = TextFieldProps & {
  label?: string
  end?: string
  start?: string
  inputsize?: 'small' | 'medium'
}

const CustomInput = forwardRef((props: CustomInputProps, ref) => {
  // Vars
  const startDate = props.start ? formatDate(props.start, 'dd/MM/yyyy') : ''
  const endDate = props.end ? ` - ${formatDate(props.end, 'dd/MM/yyyy')}` : null
  const value = `${startDate}${endDate !== null ? endDate : ''}`

  return (
    <TextField
      fullWidth
      inputRef={ref}
      size={props.inputsize ?? 'small'}
      label={props.label || ''}
      {...props}
      value={value}
    />
  )
})

type Props = {
  startDate?: string
  endDate?: string
  onChange: (startDate: string, endDate: string) => void
  inputSize?: 'small' | 'medium'
}

function DateRangePicker({ startDate, endDate, onChange, inputSize }: Props) {
  const { isMobile: fullScreen } = useMobileScreen()
  const [selectedDates, setSelectedDates] = useState<Date[]>([])
  return (
    <AppReactDatepicker
      selectsRange
      monthsShown={fullScreen ? 1 : 2}
      startDate={selectedDates[0]}
      endDate={selectedDates[1]}
      id='date-range-picker'
      onChange={(dates: Date[]) => {
        setSelectedDates(dates)
        if (!!dates[0] && !!dates[1]) {
          let end = dates[1]
          end = setHours(end, 23)
          end = setMinutes(end, 59)
          onChange?.(dates[0]?.toISOString(), end?.toISOString())
        }
      }}
      shouldCloseOnSelect
      customInput={<CustomInput label='Pilih Tanggal' start={startDate} end={endDate} inputsize={inputSize} />}
      calendarClassName='max-sm:w-min'
    />
  )
}

export default DateRangePicker
