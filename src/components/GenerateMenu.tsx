// React Imports
import type { ReactNode } from 'react'

// Next Imports
import { useParams } from 'react-router-dom'

// MUI Imports
import Chip from '@mui/material/Chip'
import type { ChipProps } from '@mui/material/Chip'

// Type Imports
import type { Locale } from '@/configs/i18n'
import type { MenuDataType, SectionDataType, SubMenuDataType, MenuItemDataType } from '@/types/menuTypes'

// Component Imports
import { SubMenu as VerticalSubMenu, MenuItem as VerticalMenuItem, MenuSection } from '@/components/menu'

// Util Imports
import { getLocalizedUrl } from '@/utils/i18n'

type Props = {
  menuData: MenuDataType[]
  approvalCounts?: {
    mrCount: number
    prCount: number
    mtCount: number
    mbCount: number
    mgOutCount: number
    smCount: number
    rmaCount: number
  }
}

// Generate a menu from the menu data array
export const GenerateVerticalMenu = ({ menuData, approvalCounts }: Props) => {
  // Hooks
  const { lang: locale } = useParams()
  const { mrCount, prCount, mtCount, mbCount, mgOutCount, smCount, rmaCount } = approvalCounts

  const renderMenuItems = (data: MenuDataType[]) => {
    // Use the map method to iterate through the array of menu data
    return data.map((item: MenuDataType, index) => {
      const menuSectionItem = item as SectionDataType
      const subMenuItem = item as SubMenuDataType
      const menuItem = item as MenuItemDataType

      // Check if the current item is a section
      if (menuSectionItem.isSection) {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { children, isSection, ...rest } = menuSectionItem

        // If it is, return a MenuSection component and call generateMenu with the current menuSectionItem's children
        return (menuSectionItem.children?.length ?? 0) > 0 ? (
          <MenuSection key={index} {...rest}>
            {children && renderMenuItems(children)}
          </MenuSection>
        ) : (
          <></>
        )
      }

      // Check if the current item is a sub menu
      if (subMenuItem.children) {
        const { children, icon, prefix, suffix, ...rest } = subMenuItem

        const Icon = icon ? <i className={icon} /> : null

        const subMenuPrefix: ReactNode =
          prefix && (prefix as ChipProps).label ? (
            <Chip size='small' {...(prefix as ChipProps)} />
          ) : (
            (prefix as ReactNode)
          )

        const subMenuSuffix: ReactNode =
          suffix && (suffix as ChipProps).label ? (
            <Chip size='small' {...(suffix as ChipProps)} />
          ) : (
            (suffix as ReactNode)
          )

        // If it is, return a SubMenu component and call generateMenu with the current subMenuItem's children
        return subMenuItem.children.length > 0 ? (
          <VerticalSubMenu
            key={index}
            prefix={subMenuPrefix}
            suffix={subMenuSuffix}
            {...rest}
            {...(Icon && { icon: Icon })}
          >
            {children && renderMenuItems(children)}
          </VerticalSubMenu>
        ) : (
          <></>
        )
      }

      // If the current item is neither a section nor a sub menu, return a MenuItem component
      const { label, excludeLang, icon, prefix, suffix, ...rest } = menuItem

      // Localize the href
      const href = rest.href?.startsWith('http')
        ? rest.href
        : rest.href && (excludeLang ? rest.href : getLocalizedUrl(rest.href))

      const Icon = icon ? <i className={icon} /> : null

      const menuItemPrefix: ReactNode =
        prefix && (prefix as ChipProps).label ? <Chip size='small' {...(prefix as ChipProps)} /> : (prefix as ReactNode)

      const menuItemSuffix: ReactNode =
        suffix && (suffix as ChipProps).label ? <Chip size='small' {...(suffix as ChipProps)} /> : (suffix as ReactNode)

      return (
        <VerticalMenuItem
          key={index}
          prefix={menuItemPrefix}
          suffix={menuItemSuffix}
          {...rest}
          href={href}
          {...(Icon && { icon: Icon })}
          exactMatch={false}
          activeUrl={href}
        >
          {label}
        </VerticalMenuItem>
      )
    })
  }

  return <>{renderMenuItems(menuData)}</>
}
