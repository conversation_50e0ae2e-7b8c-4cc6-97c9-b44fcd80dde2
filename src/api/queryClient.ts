import { ErrorData, ErrorResponse } from '@/types/api'
import { Mutation, Mutation<PERSON>ache, QueryCache, QueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { toast } from 'react-toastify'

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 10000
    }
  },
  mutationCache: new MutationCache({
    onError: (
      error: AxiosError<ErrorResponse>,
      _variables: unknown,
      _context: unknown,
      mutation: Mutation<unknown, unknown, unknown, unknown>
    ): void => {
      let message = error.message
      if (error.response?.data?.message) message = error.response?.data?.message
      if (message) {
        toast.error(message)
      }
    }
  }),
  queryCache: new QueryCache({
    onError: (error: AxiosError<ErrorResponse>) => {
      const message = error.response?.data?.message
      if (message) {
        toast.error(message)
      }
    }
  })
})

export const defaultListData = { items: [], page: 0, totalItems: 0, totalPages: 0 }
