import { ApiResponse } from '@/types/api'
import {
  CreateWpDto,
  StuffRequestType,
  UpdateApprovalStuffRequestPayload,
  WpDto,
  WpReportDto,
  WpReportType,
  WpStartDto,
  WpType
} from '@/types/wpTypes'
import { useMutation, UseMutationResult } from '@tanstack/react-query'
import WpService from './service'
import { StuffReqPayload } from '@/types/payload'

export const useCreateWp = (): UseMutationResult<ApiResponse<WpType>, Error, CreateWpDto, void> => {
  return useMutation({
    mutationFn: (payload: CreateWpDto) => WpService.createWp(payload)
  })
}

export const useStartProcessWp = (): UseMutationResult<ApiResponse<WpType>, Error, WpDto & WpStartDto, void> => {
  return useMutation({
    mutationFn: (payload: WpDto & WpStartDto) => WpService.startProcessWp(payload)
  })
}

export const useStopProcessWp = (): UseMutationResult<ApiResponse<WpType>, Error, WpDto, void> => {
  return useMutation({
    mutationFn: (payload: WpDto) => WpService.stopProcessWp(payload)
  })
}

export const useAddReportWp = (): UseMutationResult<ApiResponse<WpReportType>, Error, WpDto & WpReportDto, void> => {
  return useMutation({
    mutationFn: (payload: WpDto & WpReportDto) => WpService.addReportWp(payload)
  })
}

export const useUpdateReportWp = (): UseMutationResult<ApiResponse<WpReportType>, Error, WpDto & WpReportDto, void> => {
  return useMutation({
    mutationFn: (payload: WpDto & WpReportDto) => WpService.updateWpReport(payload)
  })
}

export const useDeleteReportWp = (): UseMutationResult<ApiResponse<any>, Error, WpDto & { reportId: string }, void> => {
  return useMutation({
    mutationFn: (payload: WpDto & { reportId: string }) => WpService.deleteWpReport(payload)
  })
}

export const useApproveWp = (): UseMutationResult<ApiResponse<WpType>, Error, string, void> => {
  return useMutation({
    mutationFn: (id: string) => WpService.approveWp(id)
  })
}

export const useRejectWp = (): UseMutationResult<
  ApiResponse<WpType>,
  Error,
  WpDto & { rejectReason: string },
  void
> => {
  return useMutation({
    mutationFn: (payload: WpDto & { rejectReason: string }) => WpService.rejectWp(payload)
  })
}

export const useStartTimerWp = (): UseMutationResult<ApiResponse<WpType>, Error, WpDto & WpStartDto, void> => {
  return useMutation({
    mutationFn: (payload: WpDto & WpStartDto) => WpService.startTimeWp(payload)
  })
}

export const useStopTimerWp = (): UseMutationResult<ApiResponse<WpType>, Error, WpDto, void> => {
  return useMutation({
    mutationFn: (payload: WpDto) => WpService.stopTimeWp(payload)
  })
}

export const useCreateStuffReq = (): UseMutationResult<ApiResponse<StuffRequestType>, Error, StuffReqPayload, void> => {
  return useMutation({
    mutationFn: (payload: StuffReqPayload) => WpService.createStuffReq(payload)
  })
}

export const useReadStuffApproval = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  { stuffId: string; approvalId: number; isRead: boolean },
  void
> => {
  return useMutation({
    mutationFn: ({ stuffId, approvalId, isRead }) => WpService.readStuffApproval({ stuffId, approvalId, isRead })
  })
}

export const useUpdateStuffApprovalStatus = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  UpdateApprovalStuffRequestPayload,
  void
> => {
  return useMutation({
    mutationFn: (payload: UpdateApprovalStuffRequestPayload) => {
      return WpService.updateStuffApprovalStatus(payload)
    }
  })
}
