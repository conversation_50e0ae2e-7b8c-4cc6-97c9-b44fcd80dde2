import request from '@/api/request'
import { ApiResponse, ListResponse } from '@/types/api'
import { ApprovalsCountType } from '@/types/appTypes'
import { ListParams, StuffReqPayload } from '@/types/payload'
import {
  CreateWpDto,
  StuffRequestParams,
  StuffRequestType,
  UpdateApprovalStuffRequestPayload,
  WpDto,
  WpLogType,
  WpParams,
  WpReportDto,
  WpReportType,
  WpStartDto,
  WpType
} from '@/types/wpTypes'

export const BASE_URL_WP = 'work-processes'
export const BASE_URL_STUFF_REQ = 'stuff-requests'

export default class WpService {
  public static getWpList = (params: WpParams): Promise<ApiResponse<ListResponse<WpType>>> => {
    return request({
      method: 'GET',
      instance: 'CORE',
      url: `${BASE_URL_WP}`,
      params
    })
  }

  public static getWpIn = (params: WpParams): Promise<ApiResponse<ListResponse<WpType>>> => {
    return request({
      method: 'GET',
      instance: 'CORE',
      url: `${BASE_URL_WP}/to-me`,
      params
    })
  }

  public static getWpDetail = (id: string): Promise<ApiResponse<WpType>> => {
    return request({
      method: 'GET',
      instance: 'CORE',
      url: `${BASE_URL_WP}/${id}`,
      params: { limit: Number.MAX_SAFE_INTEGER }
    })
  }

  public static getWpLogs = (id: string): Promise<ApiResponse<ListResponse<WpLogType>>> => {
    return request({
      method: 'GET',
      instance: 'CORE',
      url: `${BASE_URL_WP}/${id}/logs`
    })
  }

  public static startProcessWp = (data: WpDto & WpStartDto): Promise<ApiResponse<WpType>> => {
    const { workProcessId, laborUserIds } = data
    return request({
      method: 'PATCH',
      instance: 'CORE',
      url: `${BASE_URL_WP}/${data.workProcessId}/process-starts`,
      data: { laborUserIds }
    })
  }

  public static stopProcessWp = (data: WpDto): Promise<ApiResponse<WpType>> => {
    return request({
      method: 'PATCH',
      instance: 'CORE',
      url: `${BASE_URL_WP}/${data.workProcessId}/process-ends`,
      data
    })
  }

  public static startTimeWp = (data: WpDto & WpStartDto): Promise<ApiResponse<WpType>> => {
    return request({
      method: 'PATCH',
      instance: 'CORE',
      url: `${BASE_URL_WP}/${data.workProcessId}/timer-starts`,
      data: { laborUserIds: data.laborUserIds }
    })
  }

  public static stopTimeWp = (data: WpDto): Promise<ApiResponse<WpType>> => {
    return request({
      method: 'PATCH',
      instance: 'CORE',
      url: `${BASE_URL_WP}/${data.workProcessId}/timer-stops`,
      data: { note: data.note }
    })
  }

  public static createWp = (data: CreateWpDto): Promise<ApiResponse<WpType>> => {
    return request({
      method: 'POST',
      instance: 'CORE',
      url: `${BASE_URL_WP}`,
      data
    })
  }

  public static addReportWp = (data: WpDto & WpReportDto): Promise<ApiResponse<WpReportType>> => {
    return request({
      method: 'POST',
      instance: 'CORE',
      url: `${BASE_URL_WP}/${data.workProcessId}/reports`,
      data
    })
  }

  public static getWpReports = (params: WpDto & ListParams): Promise<ApiResponse<ListResponse<WpReportType>>> => {
    return request({
      method: 'GET',
      instance: 'CORE',
      url: `${BASE_URL_WP}/${params.workProcessId}/reports`,
      params: params
    })
  }

  public static getWpReport = (params: WpDto & { reportId: string }): Promise<ApiResponse<WpReportType>> => {
    return request({
      method: 'GET',
      instance: 'CORE',
      url: `${BASE_URL_WP}/${params.workProcessId}/reports/${params.reportId}`
    })
  }

  public static deleteWpReport = (params: WpDto & { reportId: string }): Promise<ApiResponse<any>> => {
    return request({
      method: 'DELETE',
      instance: 'CORE',
      url: `${BASE_URL_WP}/${params.workProcessId}/reports/${params.reportId}`
    })
  }

  public static updateWpReport = (params: WpDto & WpReportDto): Promise<ApiResponse<WpReportType>> => {
    return request({
      method: 'PATCH',
      instance: 'CORE',
      url: `${BASE_URL_WP}/${params.workProcessId}/reports/${params.reportId}`,
      data: params
    })
  }

  public static approveWp = (id: string): Promise<ApiResponse<WpType>> => {
    return request({
      method: 'PATCH',
      instance: 'CORE',
      url: `${BASE_URL_WP}/${id}/approve`,
      data: {}
    })
  }

  public static rejectWp = (params: WpDto & { rejectReason: string }): Promise<ApiResponse<WpType>> => {
    return request({
      method: 'PATCH',
      instance: 'CORE',
      url: `${BASE_URL_WP}/${params.workProcessId}/reject`,
      data: {
        rejectReason: params.rejectReason
      }
    })
  }

  public static createStuffReq = (data: StuffReqPayload): Promise<ApiResponse<StuffRequestType>> => {
    return request({
      method: 'POST',
      instance: 'CORE',
      url: `${BASE_URL_STUFF_REQ}`,
      data
    })
  }

  public static getStuffReq = (id: string): Promise<ApiResponse<StuffRequestType>> => {
    return request({
      method: 'GET',
      instance: 'CORE',
      url: `${BASE_URL_STUFF_REQ}/${id}`
    })
  }

  public static getStuffReqList = (
    params: StuffRequestParams
  ): Promise<ApiResponse<ListResponse<StuffRequestType>>> => {
    return request({
      method: 'GET',
      instance: 'CORE',
      url: `${BASE_URL_STUFF_REQ}`,
      params
    })
  }

  public static getStuffReqToMe = (
    params: StuffRequestParams
  ): Promise<ApiResponse<ListResponse<StuffRequestType>>> => {
    return request({
      method: 'GET',
      instance: 'CORE',
      url: `${BASE_URL_STUFF_REQ}/to-me`,
      params
    })
  }

  public static getCountApprovals = (type: 'TAKE' | 'RETURN'): Promise<ApiResponse<ApprovalsCountType>> => {
    return request({
      method: 'GET',
      instance: 'CORE',
      url: `${BASE_URL_STUFF_REQ}/to-me/waitings-count`,
      params: {
        type
      }
    })
  }

  public static readonly readStuffApproval = ({
    stuffId,
    approvalId,
    isRead
  }: {
    stuffId: string
    approvalId: number
    isRead: boolean
  }): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_URL_STUFF_REQ}/${stuffId}/approvals/${approvalId}/read`,
      instance: 'CORE',
      method: 'patch',
      data: {
        isRead
      }
    })
  }

  public static readonly updateStuffApprovalStatus = ({
    stuffId,
    approvalId,
    status,
    ...payload
  }: UpdateApprovalStuffRequestPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_URL_STUFF_REQ}/${stuffId}/approvals/${approvalId}/status`,
      instance: 'CORE',
      method: 'patch',
      data: {
        ...payload,
        status
      }
    })
  }
}
