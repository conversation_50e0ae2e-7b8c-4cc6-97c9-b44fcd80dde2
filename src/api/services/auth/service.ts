import request from '@/api/request'
import { ApiResponse, LoginResponse } from '@/types/api'
import { ResetPasswordPayload, LoginPayload } from '@/types/payload'
import { uuidv4 } from 'uuidv7'

export const BASE_AUTH_URL = 'auth'

export default class AuthService {
  public static readonly login = (payload: LoginPayload): Promise<ApiResponse<LoginResponse>> => {
    return request({
      url: `${BASE_AUTH_URL}/login`,
      method: 'post',
      data: payload,
      instance: 'ACCOUNT'
    })
  }

  public static readonly refreshToken = (refreshToken: string): Promise<ApiResponse<LoginResponse>> => {
    return request({
      url: `${BASE_AUTH_URL}/refresh-token`,
      method: 'post',
      instance: 'ACCOUNT',
      data: {
        refreshToken
      }
    })
  }

  public static readonly requestForgotPassword = (
    payload: Omit<LoginPayload, 'password'>
  ): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_AUTH_URL}/forgot-password`,
      method: 'post',
      instance: 'ACCOUNT',
      data: payload
    })
  }

  public static readonly resetPassword = (payload: ResetPasswordPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_AUTH_URL}/reset-password`,
      method: 'post',
      instance: 'ACCOUNT',
      data: payload
    })
  }
}
