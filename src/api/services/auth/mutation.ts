import { ApiResponse, LoginResponse } from '@/types/api'
import { LoginPayload, ResetPasswordPayload } from '@/types/payload'
import { UseMutationResult, useMutation } from '@tanstack/react-query'
import AuthService from './service'

export const useLogin = (): UseMutationResult<ApiResponse<LoginResponse>, Error, LoginPayload, void> => {
  return useMutation({
    mutationFn: (payload: LoginPayload) => {
      return AuthService.login(payload)
    }
  })
}

export const useForgotPassword = (): UseMutationResult<
  ApiResponse<LoginResponse>,
  Error,
  Omit<LoginPayload, 'password'>,
  void
> => {
  return useMutation({
    mutationFn: (payload: Omit<LoginPayload, 'password'>) => {
      return AuthService.requestForgotPassword(payload)
    }
  })
}

export const useResetPassword = (): UseMutationResult<
  ApiResponse<LoginResponse>,
  Error,
  ResetPasswordPayload,
  void
> => {
  return useMutation({
    mutationFn: (payload: ResetPasswordPayload) => {
      return AuthService.resetPassword(payload)
    }
  })
}
