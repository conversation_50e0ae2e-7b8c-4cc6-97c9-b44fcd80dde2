import { ApiResponse } from '@/types/api'
import { ImPayload, ImType, MgApprovalPayload, MgOutPayload, MgOutType } from '@/types/mgTypes'
import { UseMutationResult, useMutation } from '@tanstack/react-query'
import MgService from './service'
import { ItemPayload } from '@/types/payload'

export const useAddIncomingMaterial = (): UseMutationResult<ApiResponse<ImType>, Error, ImPayload, void> => {
  return useMutation({
    mutationFn: (payload: ImPayload) => {
      return MgService.addIncomingMaterial(payload)
    }
  })
}

export const useAddOutcomingMaterial = (): UseMutationResult<ApiResponse<MgOutType>, Error, MgOutPayload, void> => {
  return useMutation({
    mutationFn: (payload: MgOutPayload) => {
      return MgService.addOutcomingMaterial(payload)
    }
  })
}

export const useUpdateMgOutApprovalStatus = (): UseMutationResult<ApiResponse<any>, Error, MgApprovalPayload, void> => {
  return useMutation({
    mutationFn: (payload: MgApprovalPayload) => {
      return MgService.updateOutcomingMaterialApprovalStatus(payload)
    }
  })
}

export const useReadMgOutApproval = (): UseMutationResult<ApiResponse<any>, Error, MgApprovalPayload, void> => {
  return useMutation({
    mutationFn: (payload: MgApprovalPayload) => {
      return MgService.readOutcomingMaterialApproval(payload)
    }
  })
}

export const useUpdateStockItemNote = (): UseMutationResult<ApiResponse<any>, Error, Partial<ItemPayload>, void> => {
  return useMutation({
    mutationFn: (payload: Partial<ItemPayload>) => {
      return MgService.updateStockItemNote(payload)
    }
  })
}
