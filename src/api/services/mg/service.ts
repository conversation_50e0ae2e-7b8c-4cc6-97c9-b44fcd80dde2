import request from '@/api/request'
import { ApiResponse, ListResponse } from '@/types/api'
import {
  ImParams,
  ImPayload,
  ImType,
  MgApprovalPayload,
  MgOutPayload,
  MgOutType,
  StockLogType,
  StockType
} from '@/types/mgTypes'
import { ItemParams, ItemPayload, ListParams } from '@/types/payload'
import { BASE_ITEMS_URL } from '../company/service'
import { ItemType } from '@/types/companyTypes'
import { ApprovalsCountType } from '@/types/appTypes'

export const BASE_MG_URL = 'core/material-goods'

const BASE_MG_IM_URL = 'incoming-materials'
const BASE_MG_OUT_URL = 'outgoing-materials'

export default class MgService {
  public static readonly getIncomingMaterial = (id: string): Promise<ApiResponse<ImType>> => {
    return request({
      url: `${BASE_MG_IM_URL}/${id}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly getIncomingMaterialList = (params: ImParams): Promise<ApiResponse<ListResponse<ImType>>> => {
    return request({
      url: `${BASE_MG_IM_URL}`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly addIncomingMaterial = (payload: ImPayload): Promise<ApiResponse<ImType>> => {
    return request({
      url: `${BASE_MG_IM_URL}`,
      instance: 'CORE',
      method: 'post',
      data: payload
    })
  }

  public static readonly updateStockItemNote = (payload: Partial<ItemPayload>): Promise<ApiResponse<ItemType>> => {
    return request({
      url: `${BASE_ITEMS_URL}/${payload.itemId}/stocks/${payload.stockId}`,
      instance: 'CORE',
      method: 'patch',
      data: {
        note: payload.stockNote
      }
    })
  }

  public static readonly getStockLogList = (params: ItemParams): Promise<ApiResponse<ListResponse<StockLogType>>> => {
    return request({
      url: `${BASE_ITEMS_URL}/${params.itemId}/stock-logs`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getStockDetails = (params: ItemParams): Promise<ApiResponse<ListResponse<StockType>>> => {
    return request({
      url: `${BASE_ITEMS_URL}/${params.itemId}/stocks`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getOutcomingMaterial = (id: string): Promise<ApiResponse<MgOutType>> => {
    return request({
      url: `${BASE_MG_OUT_URL}/${id}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly getOutcomingMaterialList = (
    params: ImParams
  ): Promise<ApiResponse<ListResponse<MgOutType>>> => {
    return request({
      url: `${BASE_MG_OUT_URL}`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getToMeOutcomingMaterialList = (
    params: ImParams
  ): Promise<ApiResponse<ListResponse<MgOutType>>> => {
    return request({
      url: `${BASE_MG_OUT_URL}/to-me`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly addOutcomingMaterial = (payload: MgOutPayload): Promise<ApiResponse<MgOutType>> => {
    return request({
      url: `${BASE_MG_OUT_URL}`,
      instance: 'CORE',
      method: 'post',
      data: payload
    })
  }

  public static readonly updateOutcomingMaterialApprovalStatus = ({
    mgId,
    approvalId,
    status,
    takenBy,
    takenImageUploadId
  }: MgApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_MG_OUT_URL}/${mgId}/approvals/${approvalId}/status`,
      instance: 'CORE',
      method: 'patch',
      data: {
        status,
        ...(takenBy
          ? {
              takenBy,
              takenImageUploadId
            }
          : {})
      }
    })
  }

  public static readonly readOutcomingMaterialApproval = ({
    mgId,
    approvalId,
    isRead
  }: MgApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_MG_OUT_URL}/${mgId}/approvals/${approvalId}/read`,
      instance: 'CORE',
      method: 'patch',
      data: {
        isRead
      }
    })
  }

  public static readonly getCountMgOutApprovals = async (): Promise<ApiResponse<ApprovalsCountType>> => {
    return request({
      method: 'get',
      url: `${BASE_MG_OUT_URL}/to-me/waitings-count`,
      instance: 'CORE'
    })
  }
}
