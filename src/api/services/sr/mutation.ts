import { ApiResponse } from '@/types/api'
import { UseMutationResult, useMutation } from '@tanstack/react-query'
import SrService from './service'
import { SrApprovalPayload, SrPayload, SrType } from '@/types/srTypes'

export const useAddSr = (): UseMutationResult<ApiResponse<SrType>, Error, SrPayload, void> => {
  return useMutation({
    mutationFn: (payload: SrPayload) => {
      return SrService.addSr(payload)
    }
  })
}

export const useUpdateSrStatus = (): UseMutationResult<ApiResponse<any>, Error, SrApprovalPayload, void> => {
  return useMutation({
    mutationFn: (payload: SrApprovalPayload) => {
      return SrService.updateSrApprovalStatus(payload)
    }
  })
}

export const useReadSrApproval = (): UseMutationResult<ApiResponse<any>, Error, SrApprovalPayload, void> => {
  return useMutation({
    mutationFn: (payload: SrApprovalPayload) => {
      return SrService.readSrApproval(payload)
    }
  })
}
