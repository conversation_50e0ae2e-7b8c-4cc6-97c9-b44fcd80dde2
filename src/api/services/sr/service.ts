import request from '@/api/request'
import { ApiResponse, ListResponse } from '@/types/api'
import { ApprovalsCountType } from '@/types/appTypes'
import { SrApprovalPayload, SrParams, SrPayload, SrType } from '@/types/srTypes'

const BASE_SR_URL = 'stock-returns'

export default class SrService {
  public static readonly addSr = (payload: SrPayload): Promise<ApiResponse<SrType>> => {
    return request({
      url: `${BASE_SR_URL}`,
      instance: 'CORE',
      method: 'post',
      data: payload
    })
  }

  public static readonly getSrList = (params: SrParams): Promise<ApiResponse<ListResponse<SrType>>> => {
    return request({
      url: `${BASE_SR_URL}`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getToMeSrList = (params: SrParams): Promise<ApiResponse<ListResponse<SrType>>> => {
    return request({
      url: `${BASE_SR_URL}/to-me`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getSr = (id: string): Promise<ApiResponse<SrType>> => {
    return request({
      url: `${BASE_SR_URL}/${id}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly readSrApproval = ({
    stockReturnId,
    approvalId,
    isRead
  }: SrApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_SR_URL}/${stockReturnId}/approvals/${approvalId}/read`,
      instance: 'CORE',
      method: 'patch',
      data: {
        isRead
      }
    })
  }

  public static readonly updateSrApprovalStatus = ({
    stockReturnId,
    approvalId,
    status
  }: SrApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_SR_URL}/${stockReturnId}/approvals/${approvalId}/status`,
      instance: 'CORE',
      method: 'patch',
      data: {
        status
      }
    })
  }

  public static readonly getApprovalsCount = (): Promise<ApiResponse<ApprovalsCountType>> => {
    return request({
      url: `${BASE_SR_URL}/to-me/waitings-count`,
      instance: 'CORE',
      method: 'get'
    })
  }
}
