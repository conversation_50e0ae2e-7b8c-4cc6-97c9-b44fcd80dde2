import request from '@/api/request'
import { ApiResponse } from '@/types/api'
import { BASE_PO_URL } from '../po/service'
import { PoValidationDoc } from '@/types/validationDocTypes'
import { BASE_SO_URL } from '../service-order/service'

export default class ValidateService {
  public static readonly getPoDetail = (id: string, signId: string): Promise<ApiResponse<PoValidationDoc>> => {
    return request({
      url: `${BASE_PO_URL}/${id}/validation`,
      instance: 'CORE',
      method: 'GET',
      headers: {
        'X-Validation-Signature': signId
      }
    })
  }

  public static readonly getSoDetail = (id: string, signId: string): Promise<ApiResponse<PoValidationDoc>> => {
    return request({
      url: `${BASE_SO_URL}/${id}/validation`,
      instance: 'CORE',
      method: 'GET',
      headers: {
        'X-Validation-Signature': signId
      }
    })
  }
}
