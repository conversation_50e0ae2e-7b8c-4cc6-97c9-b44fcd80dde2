import { PoValidationDoc } from '@/types/validationDocTypes'
import ValidateService from './service'

export const VALIDATE_QUERY_KEY = 'VALIDATE_QUERY_KEY'

export default class ValidateQueryMethods {
  public static readonly getPoDetail = async (id: string, signId: string): Promise<PoValidationDoc> => {
    const { data } = await ValidateService.getPoDetail(id, signId)
    return data
  }

  public static readonly getSoDetail = async (id: string, signId: string): Promise<PoValidationDoc> => {
    const { data } = await ValidateService.getSoDetail(id, signId)
    return data
  }
}
