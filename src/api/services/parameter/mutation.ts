import { ParameterType, PreReleaseTemplateDto } from '@/types/parameterTypes'
import { useMutation, UseMutationResult } from '@tanstack/react-query'
import ParameterServices from './service'
import { ApiResponse } from '@/types/api'

export const useCreatePreReleaseTemplate = (): UseMutationResult<
  ApiResponse<ParameterType>,
  Error,
  PreReleaseTemplateDto,
  void
> => {
  return useMutation({
    mutationFn: dto => ParameterServices.createPreReleaseTemplate(dto)
  })
}
