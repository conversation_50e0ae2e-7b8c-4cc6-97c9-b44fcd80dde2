import { ApiResponse, ListResponse } from '@/types/api'
import { ParameterParams, ParameterType } from '@/types/parameterTypes'
import ParameterServices from './service'
import { defaultListData } from '@/api/queryClient'

export default class ParameterQueryMethods {
  public static readonly getParameters = async (params: ParameterParams): Promise<ListResponse<ParameterType>> => {
    const res = await ParameterServices.getParameters(params)
    return res.data ?? (defaultListData as ListResponse<ParameterType>)
  }

  public static readonly getPreReleaseTemplates = async (
    params: ParameterParams
  ): Promise<ListResponse<ParameterType>> => {
    const res = await ParameterServices.getPreReleaseTemplates(params)
    return res.data ?? (defaultListData as ListResponse<ParameterType>)
  }

  public static readonly getPreReleaseTemplate = async (id: string): Promise<ParameterType> => {
    const res = await ParameterServices.getPreReleaseTemplate(id)
    return res.data
  }
}
