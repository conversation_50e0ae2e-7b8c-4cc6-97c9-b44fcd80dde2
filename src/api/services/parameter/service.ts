import request from '@/api/request'
import { ApiResponse, ListResponse } from '@/types/api'
import { ParameterParams, ParameterType, PreReleaseTemplateDto } from '@/types/parameterTypes'

export const PARAMETER_QUERY_KEY = 'PARAMETER_QUERY_KEY'
export const FORMAT_QUERY_LIST_KEY = 'FORMAT_QUERY_LIST_KEY'

const BASE_PARAMETER_URL = 'parameters'
const BASE_PRE_RELEASE_TEMPLATE_URL = 'pre-release-templates'

export default class ParameterServices {
  public static readonly getParameters = (
    params: ParameterParams
  ): Promise<ApiResponse<ListResponse<ParameterType>>> => {
    return request({
      url: `${BASE_PARAMETER_URL}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly getPreReleaseTemplates = (
    params: ParameterParams
  ): Promise<ApiResponse<ListResponse<ParameterType>>> => {
    return request({
      url: `${BASE_PRE_RELEASE_TEMPLATE_URL}`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getPreReleaseTemplate = (id: string): Promise<ApiResponse<ParameterType>> => {
    return request({
      url: `${BASE_PRE_RELEASE_TEMPLATE_URL}/${id}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly createPreReleaseTemplate = (
    dto: PreReleaseTemplateDto
  ): Promise<ApiResponse<ParameterType>> => {
    return request({
      url: `${BASE_PRE_RELEASE_TEMPLATE_URL}`,
      instance: 'CORE',
      method: 'post',
      data: dto
    })
  }
}
