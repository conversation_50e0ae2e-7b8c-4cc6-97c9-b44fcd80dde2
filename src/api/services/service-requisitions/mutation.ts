import { ApiResponse } from '@/types/api'
import { UseMutationResult, useMutation } from '@tanstack/react-query'
import ServiceRequisitionService from './service'
import {
  ServiceRequisitionPayload,
  ServiceRequisition,
  ServiceRequisitionItemPayload,
  ServiceRequisitionApprovalPayload,
  CancelServiceRequisitionPayload
} from '@/types/serviceRequisitionsTypes'

export const useAddSr = (): UseMutationResult<
  ApiResponse<ServiceRequisition>,
  Error,
  ServiceRequisitionPayload,
  void
> => {
  return useMutation({
    mutationFn: (payload: ServiceRequisitionPayload) => {
      return ServiceRequisitionService.createSr(payload)
    }
  })
}

export const useUpdateSr = (): UseMutationResult<
  ApiResponse<ServiceRequisition>,
  Error,
  ServiceRequisitionPayload,
  void
> => {
  return useMutation({
    mutationFn: (payload: ServiceRequisitionPayload) => {
      return ServiceRequisitionService.updateSr(payload)
    }
  })
}

export const useDeleteSr = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (srId: string) => {
      return ServiceRequisitionService.deleteSr(srId)
    }
  })
}

export const useUpdateSrItem = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  { srId: string; payload: ServiceRequisitionItemPayload },
  void
> => {
  return useMutation({
    mutationFn: ({ srId, payload }) => {
      return ServiceRequisitionService.updateSrItem(srId, payload)
    }
  })
}

export const useDeleteSrItem = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  { srId: string; itemId: number },
  void
> => {
  return useMutation({
    mutationFn: ({ srId, itemId }) => {
      return ServiceRequisitionService.deleteSrItem(srId, itemId)
    }
  })
}

export const useUpdateSrApprover = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  ServiceRequisitionApprovalPayload,
  void
> => {
  return useMutation({
    mutationFn: (payload: ServiceRequisitionApprovalPayload) => {
      return ServiceRequisitionService.updateSrApprover(payload)
    }
  })
}

export const useUpdateSrApprovalStatus = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  ServiceRequisitionApprovalPayload,
  void
> => {
  return useMutation({
    mutationFn: (payload: ServiceRequisitionApprovalPayload) => {
      return ServiceRequisitionService.updateSrApprovalStatus(payload)
    }
  })
}

export const useReadSrApproval = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  ServiceRequisitionApprovalPayload,
  void
> => {
  return useMutation({
    mutationFn: (payload: ServiceRequisitionApprovalPayload) => {
      return ServiceRequisitionService.readSrApproval(payload)
    }
  })
}

export const useCancelSr = (): UseMutationResult<ApiResponse<any>, Error, CancelServiceRequisitionPayload, void> => {
  return useMutation({
    mutationFn: (payload: CancelServiceRequisitionPayload) => {
      return ServiceRequisitionService.cancelSr(payload)
    }
  })
}

export const useCloseSr = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (srId: string) => {
      return ServiceRequisitionService.closeSr(srId)
    }
  })
}
