import request from '@/api/request'
import { ApiResponse, ListResponse } from '@/types/api'
import { ApprovalsCountType } from '@/types/appTypes'
import { ListParams } from '@/types/payload'
import {
  CancelServiceRequisitionPayload,
  ServiceRequisition,
  ServiceRequisitionApprovalPayload,
  ServiceRequisitionItemPayload,
  ServiceRequisitionLog,
  ServiceRequisitionParams,
  ServiceRequisitionPayload
} from '@/types/serviceRequisitionsTypes'

const SR_BASE_URL = 'service-requisitions'

export default class ServiceRequisitionService {
  public static readonly createSr = (payload: ServiceRequisitionPayload): Promise<ApiResponse<ServiceRequisition>> => {
    return request({
      url: SR_BASE_URL,
      instance: 'CORE',
      method: 'post',
      data: payload
    })
  }

  public static readonly updateSr = ({
    srId,
    ...payload
  }: ServiceRequisitionPayload): Promise<ApiResponse<ServiceRequisition>> => {
    return request({
      url: `${SR_BASE_URL}/${srId}`,
      instance: 'CORE',
      method: 'patch',
      data: payload
    })
  }

  public static readonly deleteSr = (srId: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${SR_BASE_URL}/${srId}`,
      instance: 'CORE',
      method: 'delete'
    })
  }

  public static readonly getSr = (id: string): Promise<ApiResponse<ServiceRequisition>> => {
    return request({
      url: `${SR_BASE_URL}/${id}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly getSrList = (
    params?: ServiceRequisitionParams
  ): Promise<ApiResponse<ListResponse<ServiceRequisition>>> => {
    return request({
      url: SR_BASE_URL,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getByMeSrList = (
    params?: ServiceRequisitionParams
  ): Promise<ApiResponse<ListResponse<ServiceRequisition>>> => {
    return request({
      url: `${SR_BASE_URL}/by-me`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getToMeSrList = (
    params?: ServiceRequisitionParams
  ): Promise<ApiResponse<ListResponse<ServiceRequisition>>> => {
    return request({
      url: `${SR_BASE_URL}/to-me`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly cancelSr = ({
    srId,
    cancelationNote
  }: CancelServiceRequisitionPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${SR_BASE_URL}/${srId}/cancel`,
      instance: 'CORE',
      method: 'patch',
      data: { cancelationNote }
    })
  }

  public static readonly closeSr = (srId: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${SR_BASE_URL}/${srId}/close`,
      instance: 'CORE',
      method: 'patch'
    })
  }

  public static readonly updateSrItem = (
    srId: string,
    { id, ...itemPayload }: ServiceRequisitionItemPayload
  ): Promise<ApiResponse<any>> => {
    return request({
      url: `${SR_BASE_URL}/${srId}/items/${id}`,
      instance: 'CORE',
      method: 'patch',
      data: itemPayload
    })
  }

  public static readonly deleteSrItem = (srId: string, itemId: number): Promise<ApiResponse<any>> => {
    return request({
      url: `${SR_BASE_URL}/${srId}/items/${itemId}`,
      instance: 'CORE',
      method: 'delete'
    })
  }

  public static readonly updateSrApprover = ({
    srId,
    approvalId,
    ...payload
  }: ServiceRequisitionApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${SR_BASE_URL}/${srId}/approvals/${approvalId}`,
      instance: 'CORE',
      method: 'patch',
      data: payload
    })
  }

  public static readonly updateSrApprovalStatus = ({
    srId,
    approvalId,
    status
  }: ServiceRequisitionApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${SR_BASE_URL}/${srId}/approvals/${approvalId}/status`,
      instance: 'CORE',
      method: 'patch',
      data: {
        status
      }
    })
  }

  public static readonly readSrApproval = ({
    srId,
    approvalId,
    isRead
  }: ServiceRequisitionApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${SR_BASE_URL}/${srId}/approvals/${approvalId}/read`,
      instance: 'CORE',
      method: 'patch',
      data: {
        isRead
      }
    })
  }

  public static readonly getSrLog = (id: string): Promise<ApiResponse<ServiceRequisitionLog>> => {
    return request({
      url: `${SR_BASE_URL}/logs/${id}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly getSrLogList = (
    srId: string,
    params?: ListParams
  ): Promise<ApiResponse<ListResponse<ServiceRequisitionLog>>> => {
    return request({
      url: `${SR_BASE_URL}/${srId}/logs`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getCountApprovals = (): Promise<ApiResponse<ApprovalsCountType>> => {
    return request({
      method: 'get',
      url: `${SR_BASE_URL}/to-me/waitings-count`,
      instance: 'CORE'
    })
  }
}
