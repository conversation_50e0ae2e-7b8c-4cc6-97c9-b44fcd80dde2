import ServiceRequisitionService from './service'
import { ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import { ServiceRequisition, ServiceRequisitionLog, ServiceRequisitionParams } from '@/types/serviceRequisitionsTypes'
import { ListParams } from '@/types/payload'
import { ApprovalsCountType } from '@/types/appTypes'

export const SR_QUERY_KEY = 'SR_QUERY_KEY'
export const SR_LIST_QUERY_KEY = 'SR_LIST_QUERY_KEY'
export const SR_BY_ME_LIST_QUERY_KEY = 'SR_BY_ME_LIST_QUERY_KEY'
export const SR_TO_ME_LIST_QUERY_KEY = 'SR_TO_ME_LIST_QUERY_KEY'
export const SR_LOG_QUERY_KEY = 'SR_LOG_QUERY_KEY'
export const SR_LOG_LIST_QUERY_KEY = 'SR_LOG_LIST_QUERY_KEY'

export default class ServiceRequisitionQueryMethods {
  public static readonly getSr = async (id: string): Promise<ServiceRequisition> => {
    const { data } = await ServiceRequisitionService.getSr(id)
    return data
  }

  public static readonly getSrList = async (
    params?: ServiceRequisitionParams
  ): Promise<ListResponse<ServiceRequisition>> => {
    const res = await ServiceRequisitionService.getSrList(params)
    return res.data ?? defaultListData
  }

  public static readonly getByMeSrList = async (
    params?: ServiceRequisitionParams
  ): Promise<ListResponse<ServiceRequisition>> => {
    const res = await ServiceRequisitionService.getByMeSrList(params)
    return res.data ?? defaultListData
  }

  public static readonly getToMeSrList = async (
    params?: ServiceRequisitionParams
  ): Promise<ListResponse<ServiceRequisition>> => {
    const res = await ServiceRequisitionService.getToMeSrList(params)
    return res.data ?? defaultListData
  }

  public static readonly getSrLog = async (id: string): Promise<ServiceRequisitionLog> => {
    const { data } = await ServiceRequisitionService.getSrLog(id)
    return data
  }

  public static readonly getSrLogList = async (srId: string, params?: ListParams): Promise<ServiceRequisitionLog[]> => {
    const { data } = await ServiceRequisitionService.getSrLogList(srId, params)
    return data.items ?? []
  }

  public static readonly getCountApprovals = async (): Promise<ApprovalsCountType> => {
    const { data } = await ServiceRequisitionService.getCountApprovals()
    return data
  }
}
