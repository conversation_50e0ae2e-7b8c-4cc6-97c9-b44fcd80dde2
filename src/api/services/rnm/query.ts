import { CodeParams } from '@/types/codes'
import RnMService from './service'
import { FrParams } from '@/types/frTypes'
import { WoParams } from '@/types/woTypes'
import { ListParams } from '@/types/payload'
import { MrParams } from '@/types/mrTypes'

export default class RnMQueryMethods {
  public static readonly getCodeList = async (params: CodeParams) => {
    const { data } = await RnMService.getCodeList(params)
    return data
  }
  public static readonly getFrList = async (params: FrParams) => {
    const { data } = await RnMService.getFrList(params)
    return data
  }

  public static readonly getFrDetail = async (frId: string) => {
    const { data } = await RnMService.getFrDetail(frId)
    return data
  }

  public static readonly getFrDetailLogs = async (frId: string) => {
    const { data } = await RnMService.getFrLogs(frId)
    return data
  }

  public static readonly getWoList = async (params: WoParams) => {
    const { data } = await RnMService.getWolist(params)
    return data
  }

  public static readonly getWoDetail = async (id: string) => {
    const { data } = await RnMService.getWoDetail(id)
    return data
  }

  public static readonly getWoDetailLogs = async (id: string, params?: ListParams) => {
    const { data } = await RnMService.getWoDetailLogs(id, params)
    return data
  }

  public static readonly getWoSegments = async (id: string, params?: ListParams) => {
    const { data } = await RnMService.getWoSegments(id, params)
    return data
  }

  public static readonly getWoSegment = async (woId: string, segmentId: string) => {
    const { data } = await RnMService.getWoSegment(woId, segmentId)
    return data
  }

  public static readonly getSrList = async (params?: MrParams) => {
    const { data } = await RnMService.getSrList(params)
    return data
  }
}
