import { ApiResponse } from '@/types/api'
import { UseMutationResult, useMutation } from '@tanstack/react-query'
import ServiceOrderService from './service'
import {
  CancelServiceOrderPayload,
  ServiceOrderApprovalPayload,
  ServiceOrderItemPayload,
  ServiceOrderPayload,
  ServiceOrder
} from '@/types/serviceOrderTypes'

export const useAddSo = (): UseMutationResult<ApiResponse<ServiceOrder>, Error, ServiceOrderPayload, void> => {
  return useMutation({
    mutationFn: (payload: ServiceOrderPayload) => {
      return ServiceOrderService.addSo(payload)
    }
  })
}

export const useUpdateSo = (): UseMutationResult<ApiResponse<ServiceOrder>, Error, ServiceOrderPayload, void> => {
  return useMutation({
    mutationFn: (payload: ServiceOrderPayload) => {
      return ServiceOrderService.updateSo(payload)
    }
  })
}

export const useDeleteSo = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (serviceOrderId: string) => {
      return ServiceOrderService.deleteSo(serviceOrderId)
    }
  })
}

export const useUpdateSoItem = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  { serviceOrderId: string; payload: ServiceOrderItemPayload },
  void
> => {
  return useMutation({
    mutationFn: ({ serviceOrderId, payload }) => {
      return ServiceOrderService.updateSoItem(serviceOrderId, payload)
    }
  })
}

export const useDeleteSoItem = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  { serviceOrderId: string; itemId: number },
  void
> => {
  return useMutation({
    mutationFn: ({ serviceOrderId, itemId }) => {
      return ServiceOrderService.deleteSoItem(serviceOrderId, itemId)
    }
  })
}

export const useUpdateSoApprover = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  ServiceOrderApprovalPayload,
  void
> => {
  return useMutation({
    mutationFn: (payload: ServiceOrderApprovalPayload) => {
      return ServiceOrderService.updateSoApprover(payload)
    }
  })
}

export const useUpdateSoApprovalStatus = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  ServiceOrderApprovalPayload,
  void
> => {
  return useMutation({
    mutationFn: (payload: ServiceOrderApprovalPayload) => {
      return ServiceOrderService.updateSoApprovalStatus(payload)
    }
  })
}

export const useUpdateSoCancelationStatus = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  ServiceOrderApprovalPayload,
  void
> => {
  return useMutation({
    mutationFn: (payload: ServiceOrderApprovalPayload) => {
      return ServiceOrderService.updateSoCancelationStatus(payload)
    }
  })
}

export const useReadSoApproval = (): UseMutationResult<ApiResponse<any>, Error, ServiceOrderApprovalPayload, void> => {
  return useMutation({
    mutationFn: (payload: ServiceOrderApprovalPayload) => {
      return ServiceOrderService.readSoApproval(payload)
    }
  })
}

export const useCancelSo = (): UseMutationResult<ApiResponse<any>, Error, CancelServiceOrderPayload, void> => {
  return useMutation({
    mutationFn: (payload: CancelServiceOrderPayload) => {
      return ServiceOrderService.cancelSo(payload)
    }
  })
}

export const useCloseSo = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (serviceOrderId: string) => {
      return ServiceOrderService.closeSo(serviceOrderId)
    }
  })
}
