import request from '@/api/request'
import { ApiResponse, ListResponse } from '@/types/api'
import { ApprovalsCountType } from '@/types/appTypes'
import { ListParams } from '@/types/payload'
import {
  CancelServiceOrderPayload,
  ServiceOrder,
  ServiceOrderApprovalPayload,
  ServiceOrderItemPayload,
  ServiceOrderLog,
  ServiceOrderLogType,
  ServiceOrderParams,
  ServiceOrderPayload
} from '@/types/serviceOrderTypes'

export const BASE_SO_URL = 'service-orders'

export default class ServiceOrderService {
  public static readonly addSo = (payload: ServiceOrderPayload): Promise<ApiResponse<ServiceOrder>> => {
    return request({
      url: `${BASE_SO_URL}`,
      instance: 'CORE',
      method: 'post',
      data: payload
    })
  }

  public static readonly updateSo = ({ soId, ...payload }: ServiceOrderPayload): Promise<ApiResponse<ServiceOrder>> => {
    return request({
      url: `${BASE_SO_URL}/${soId}`,
      instance: 'CORE',
      method: 'patch',
      data: payload
    })
  }

  public static readonly deleteSo = (soId: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_SO_URL}/${soId}`,
      instance: 'CORE',
      method: 'delete'
    })
  }

  public static readonly getSo = (id: string, isCancelation?: boolean): Promise<ApiResponse<ServiceOrder>> => {
    return request({
      url: `${BASE_SO_URL}/${id}`,
      instance: 'CORE',
      method: 'get',
      params: isCancelation
        ? {
            isCancelation
          }
        : {}
    })
  }

  public static readonly getSoList = (
    params?: ServiceOrderParams
  ): Promise<ApiResponse<ListResponse<ServiceOrder>>> => {
    return request({
      url: `${BASE_SO_URL}`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getByMeSoList = (
    params?: ServiceOrderParams
  ): Promise<ApiResponse<ListResponse<ServiceOrder>>> => {
    return request({
      url: `${BASE_SO_URL}/by-me`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getToMeSoList = (
    params?: ServiceOrderParams
  ): Promise<ApiResponse<ListResponse<ServiceOrder>>> => {
    return request({
      url: `${BASE_SO_URL}/to-me`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly cancelSo = ({ soId, ...payload }: CancelServiceOrderPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_SO_URL}/${soId}/cancel`,
      instance: 'CORE',
      method: 'patch',
      data: payload
    })
  }

  public static readonly closeSo = (soId: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_SO_URL}/${soId}/close`,
      instance: 'CORE',
      method: 'patch'
    })
  }

  public static readonly updateSoItem = (
    soId: string,
    { id, ...itemPayload }: ServiceOrderItemPayload
  ): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_SO_URL}/${soId}/items/${id}`,
      instance: 'CORE',
      method: 'patch',
      data: itemPayload
    })
  }

  public static readonly deleteSoItem = (soId: string, itemId: number): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_SO_URL}/${soId}/items/${itemId}`,
      instance: 'CORE',
      method: 'delete'
    })
  }

  public static readonly updateSoApprover = ({
    soId,
    approvalId,
    ...payload
  }: ServiceOrderApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_SO_URL}/${soId}/approvals/${approvalId}`,
      instance: 'CORE',
      method: 'patch',
      data: payload
    })
  }

  public static readonly updateSoApprovalStatus = ({
    soId,
    approvalId,
    status
  }: ServiceOrderApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_SO_URL}/${soId}/approvals/${approvalId}/status`,
      instance: 'CORE',
      method: 'patch',
      data: {
        status
      }
    })
  }

  public static readonly updateSoCancelationStatus = ({
    soId,
    approvalId,
    status
  }: ServiceOrderApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_SO_URL}/${soId}/approvals/${approvalId}/cancel-status`,
      instance: 'CORE',
      method: 'patch',
      data: {
        status
      }
    })
  }

  public static readonly readSoApproval = ({
    soId,
    approvalId,
    isRead
  }: ServiceOrderApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_SO_URL}/${soId}/approvals/${approvalId}/read`,
      instance: 'CORE',
      method: 'patch',
      data: {
        isRead
      }
    })
  }

  public static readonly getSoLog = (id: string): Promise<ApiResponse<ServiceOrderLog>> => {
    return request({
      url: `${BASE_SO_URL}/logs/${id}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly getSoLogList = (
    soId: string,
    params?: ListParams
  ): Promise<ApiResponse<ListResponse<ServiceOrderLog>>> => {
    return request({
      url: `${BASE_SO_URL}/${soId}/logs`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getCountApprovals = (): Promise<ApiResponse<ApprovalsCountType>> => {
    return request({
      url: `${BASE_SO_URL}/to-me/waitings-count`,
      instance: 'CORE',
      method: 'get'
    })
  }
}
