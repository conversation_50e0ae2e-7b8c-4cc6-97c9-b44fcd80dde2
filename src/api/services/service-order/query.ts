import ServiceOrderService from './service'
import { ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import { ListParams } from '@/types/payload'
import { ServiceOrderLogType, ServiceOrderParams, ServiceOrder, ServiceOrderLog } from '@/types/serviceOrderTypes'
import { ApprovalsCountType } from '@/types/appTypes'

export const SERVICE_ORDER_QUERY_KEY = 'SERVICE_ORDER_QUERY_KEY'
export const SERVICE_ORDER_LIST_QUERY_KEY = 'SERVICE_ORDER_LIST_QUERY_KEY'
export const SERVICE_ORDER_BY_ME_LIST_QUERY_KEY = 'SERVICE_ORDER_BY_ME_LIST_QUERY_KEY'
export const SERVICE_ORDER_TO_ME_LIST_QUERY_KEY = 'SERVICE_ORDER_TO_ME_LIST_QUERY_KEY'
export const SERVICE_ORDER_LOG_QUERY_KEY = 'SERVICE_ORDER_LOG_QUERY_KEY'
export const SERVICE_ORDER_LOG_LIST_QUERY_KEY = 'SERVICE_ORDER_LOG_LIST_QUERY_KEY'

export default class ServiceOrderQueryMethods {
  public static readonly getSo = async (id: string, isCancelation?: boolean): Promise<ServiceOrder> => {
    const { data } = await ServiceOrderService.getSo(id, isCancelation)
    return data
  }

  public static readonly getSoList = async (params?: ServiceOrderParams): Promise<ListResponse<ServiceOrder>> => {
    const res = await ServiceOrderService.getSoList(params)
    return res.data ?? defaultListData
  }

  public static readonly getByMeSoList = async (params?: ServiceOrderParams): Promise<ListResponse<ServiceOrder>> => {
    const res = await ServiceOrderService.getByMeSoList(params)
    return res.data ?? defaultListData
  }

  public static readonly getToMeSoList = async (params?: ServiceOrderParams): Promise<ListResponse<ServiceOrder>> => {
    const res = await ServiceOrderService.getToMeSoList(params)
    return res.data ?? defaultListData
  }

  public static readonly getSoLog = async (id: string): Promise<ServiceOrderLog> => {
    const { data } = await ServiceOrderService.getSoLog(id)
    return data
  }

  public static readonly getSoLogList = async (
    serviceOrderId: string,
    params?: ListParams
  ): Promise<ServiceOrderLog[]> => {
    const { data } = await ServiceOrderService.getSoLogList(serviceOrderId, params)
    return data.items ?? []
  }

  public static readonly getCountApprovals = async (): Promise<ApprovalsCountType> => {
    const { data } = await ServiceOrderService.getCountApprovals()
    return data
  }
}
