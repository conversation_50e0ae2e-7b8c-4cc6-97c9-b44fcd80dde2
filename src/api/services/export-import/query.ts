import { ApiResponse, ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import FileExportImportService from './service'
import { ExportDownloadUrl, ExportImportScope, ExportParams, ExportType } from '@/types/exportImportTypes'

export const EXPORT_QUERY_KEY = 'EXPORT_QUERY_KEY'
export const EXPORT_LIST_QUERY_KEY = 'EXPORT_LIST_QUERY_KEY'
export const IMPORT_TEMPLATE_QUERY_KEY = 'IMPORT_TEMPLATE_QUERY_KEY'
export const IMPORT_LIST_QUERY_KEY = 'IMPORT_LIST_QUERY_KEY'

export default class ExportImportQueryMethods {
  public static readonly getExports = async (params: ExportParams): Promise<ListResponse<ExportType>> => {
    return (await FileExportImportService.getExports(params)).data ?? defaultListData
  }

  public static readonly getExportDownloadUrl = async (id: string): Promise<ApiResponse<ExportDownloadUrl>> => {
    const data = await FileExportImportService.getExportDownloadUrl(id)
    return data
  }

  public static readonly getImportTemplate = async (scope: ExportImportScope): Promise<Blob> => {
    return await FileExportImportService.getImportTemplate(scope)
  }

  public static readonly getImports = async (params: ExportParams): Promise<ListResponse<ExportType>> => {
    return (await FileExportImportService.getImports(params)).data ?? defaultListData
  }
}
