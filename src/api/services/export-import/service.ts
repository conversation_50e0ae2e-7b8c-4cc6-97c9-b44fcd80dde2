import request from '@/api/request'
import { ApiResponse, ListResponse } from '@/types/api'
import {
  ExportPayload,
  ExportType,
  ImportPayload,
  ExportImportScope,
  ImportType,
  ExportDownloadUrl,
  ExportParams
} from '@/types/exportImportTypes'
import { convertBase64ToBlob } from '@/utils/helper'
import { AxiosResponse } from 'axios'

export const BASE_FILE_EXPORT_URL = 'exports'
export const BASE_FILE_IMPORT_URL = 'imports'

export default class FileExportImportService {
  public static readonly getExports = (params: ExportParams): Promise<ApiResponse<ListResponse<ExportType>>> => {
    return request({
      url: `${BASE_FILE_EXPORT_URL}`,
      instance: 'FILE',
      method: 'get',
      params
    })
  }

  public static readonly getExportDownloadUrl = (id: string): Promise<ApiResponse<ExportDownloadUrl>> => {
    return request({
      url: `${BASE_FILE_EXPORT_URL}/${id}/links`,
      instance: 'FILE',
      method: 'get'
    })
  }

  public static readonly initiateExport = <T>(
    scope: ExportImportScope,
    payload: ExportPayload<T>
  ): Promise<ApiResponse<ExportType>> => {
    return request({
      url: `${BASE_FILE_EXPORT_URL}/${scope}`,
      instance: 'FILE',
      method: 'post',
      data: payload
    })
  }

  public static readonly initiateImport = (payload: ImportPayload): Promise<ApiResponse<ImportType>> => {
    const form = new FormData()
    form.append('file', convertBase64ToBlob(payload.file), payload.fileName)
    payload.type && form.append('type', payload.type)
    return request({
      url: `${BASE_FILE_IMPORT_URL}/${payload.scope}`,
      instance: 'FILE',
      method: 'post',
      data: form,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  public static readonly getImportTemplate = (scope: ExportImportScope): Promise<Blob> => {
    return request(
      {
        url: `${BASE_FILE_IMPORT_URL}/templates`,
        instance: 'FILE',
        method: 'get',
        params: {
          moduleType: scope.moduleType
        },
        responseType: 'blob'
      },
      true
    ).then((res: AxiosResponse) => {
      return new Blob([res.data], { type: res.headers['content-type'] })
    })
  }

  public static readonly getImports = (params: ExportParams): Promise<ApiResponse<ListResponse<ExportType>>> => {
    return request({
      url: `${BASE_FILE_IMPORT_URL}`,
      instance: 'FILE',
      method: 'get',
      params
    })
  }
}
