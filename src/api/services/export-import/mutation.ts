import { ApiResponse } from '@/types/api'
import { UseMutationOptions, UseMutationResult, useMutation } from '@tanstack/react-query'
import { ExportType, ImportPayload, ImportType, ScopedExportPayload } from '@/types/exportImportTypes'
import FileExportImportService from './service'

export const useImportMutation = (
  options: UseMutationOptions<ApiResponse<ImportType>, Error, ImportPayload, void> = {}
) => {
  return useMutation({
    ...options,
    mutationFn: (payload: ImportPayload) => {
      return FileExportImportService.initiateImport(payload)
    }
  })
}

export const useExportMutation = <T>(): UseMutationResult<
  ApiResponse<ExportType>,
  Error,
  ScopedExportPayload<T>,
  void
> => {
  return useMutation({
    mutationFn: ({ scope, payload }: ScopedExportPayload<T>) => {
      return FileExportImportService.initiateExport(scope, payload)
    }
  })
}
