import {
  CarrierType,
  CategoryAccountType,
  CategoryType,
  CompanyType,
  DepartmentType,
  DocumentUnitType,
  ItemAccountType,
  ItemType,
  SiteType,
  UnitLogDocument,
  UnitLogType,
  UnitType,
  VendorAdressesType,
  VendorType
} from '@/types/companyTypes'
import CompanyDataService from './service'
import { DocumentParams, ItemParams, ListParams } from '@/types/payload'
import { ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import { SerialNumberType, SnParams } from '@/types/serialNumber'
import { CurrenciesType } from '@/types/currenciesTypes'
import { CustomerType } from '@/types/customerTypes'
import { ProjectParams, ProjectType } from '@/types/projectTypes'

export const CATEGORY_LIST_QUERY_KEY = 'CATEGORY_LIST_QUERY_KEY'
export const CATEGORY_QUERY_KEY = 'CATEGORY_QUERY_KEY'
export const CATEGORY_PAGED_LIST_QUERY_KEY = 'CATEGORY_PAGED_LIST_QUERY_KEY'
export const ITEM_LIST_QUERY_KEY = 'ITEM_LIST_QUERY_KEY'
export const ITEM_QUERY_KEY = 'ITEM_QUERY_KEY'
export const VENDOR_LIST_QUERY_KEY = 'VENDOR_LIST_QUERY_KEY'
export const VENDOR_QUERY_KEY = 'VENDOR_QUERY_KEY'
export const UNIT_LIST_QUERY_KEY = 'UNIT_LIST_QUERY_KEY'
export const UNIT_QUERY_KEY = 'UNIT_QUERY_KEY'
export const UNIT_LOG_LIST_QUERY_KEY = 'UNIT_LOG_LIST_QUERY_KEY'
export const SITE_LIST_QUERY_KEY = 'SITE_LIST_QUERY_KEY'
export const SITE_QUERY_KEY = 'SITE_QUERY_KEY'
export const DEPARTMENT_LIST_QUERY_KEY = 'DEPARTMENT_LIST_QUERY_KEY'
export const DEPARTMENT_QUERY_KEY = 'DEPARTMENT_QUERY_KEY'
export const SERIAL_NUMBER_LIST_QUERY_KEY = 'SERIAL_NUMBER_LIST_QUERY_KEY'
export const CURRENCIES_LIST_QUERY_KEY = 'CURRENCIES_LIST_QUERY_KEY'
export const CURRENCIES_QUERY_KEY = 'CURRENCIES_QUERY_KEY'
export const CUSTOMER_LIST_QUERY_KEY = 'CUSTOMER_LIST_QUERY_KEY'
export const CUSTOMER_QUERY_KEY = 'CUSTOMER_QUERY_KEY'
export const PROJECT_LIST_QUERY_KEY = 'PROJECT_LIST_QUERY_KEY'
export const PROJECT_QUERY_KEY = 'PROJECT_QUERY_KEY'
export const CARRIER_LIST_QUERY_KEY = 'CARRIER_LIST_QUERY_KEY'
export const COMPANY_QUERY_KEY = 'COMPANY_QUERY_KEY'
export const COMPANY_LIST_QUERY_KEY = 'COMPANY_LIST_QUERY_KEY'

export default class CompanyQueryMethods {
  public static readonly getCategoryList = async (params?: ListParams): Promise<CategoryType[]> => {
    const { data } = await CompanyDataService.getCategoryList({
      limit: 1000,
      ...params
    })

    return data.items ?? []
  }

  public static readonly getCategory = async (id: string): Promise<CategoryType> => {
    const { data } = await CompanyDataService.getCategory(id)
    return data
  }

  public static readonly getCategoryAccount = async (id: string): Promise<CategoryAccountType> => {
    const { data } = await CompanyDataService.getCategoryAccount(id)
    return data
  }

  public static readonly getCategoryPagedList = async (params: ListParams): Promise<ListResponse<CategoryType>> => {
    return (await CompanyDataService.getCategoryList(params))?.data ?? defaultListData
  }

  public static readonly getItemList = async (params?: ItemParams): Promise<ListResponse<ItemType>> => {
    const res = await CompanyDataService.getItemList(params)

    return res?.data ?? defaultListData
  }

  public static readonly getItem = async (id: string): Promise<ItemType> => {
    const { data } = await CompanyDataService.getItem(id)

    return data
  }

  public static readonly getAccountItem = async (id: string): Promise<ItemAccountType> => {
    const { data } = await CompanyDataService.getAccountItem(id)

    return data
  }

  public static readonly getVendorList = async (params?: ListParams): Promise<ListResponse<VendorType>> => {
    const res = await CompanyDataService.getVendorList(params)

    return res.data ?? defaultListData
  }

  public static readonly getVendor = async (id: string): Promise<VendorType> => {
    const { data } = await CompanyDataService.getVendor(id)

    return data
  }

  public static readonly getVendorAddress = async (id: string, vendorId: string): Promise<VendorAdressesType> => {
    const response = await CompanyDataService.getVendorAddress(vendorId, id)

    return response.data
  }

  public static readonly getUnitList = async (params?: ListParams): Promise<ListResponse<UnitType>> => {
    const res = await CompanyDataService.getUnitList(params)

    return res.data ?? defaultListData
  }

  public static readonly getUnit = async (id: string): Promise<UnitType> => {
    const { data } = await CompanyDataService.getUnit(id)

    return data
  }

  public static readonly getUnitLogs = async (id: string): Promise<ListResponse<UnitLogType>> => {
    const res = await CompanyDataService.getUnitLogs(id)

    return res.data ?? defaultListData
  }

  public static readonly getDocumentUnitLog = async (
    unitId: string,
    documentType: string
  ): Promise<ListResponse<UnitLogDocument>> => {
    const res = await CompanyDataService.getDocumentUnitLog(unitId, documentType)

    return res.data
  }

  public static readonly getDocumentList = async (params?: DocumentParams): Promise<ListResponse<DocumentUnitType>> => {
    const res = await CompanyDataService.getDocumentList(params)

    return res.data ?? defaultListData
  }

  public static readonly getSiteList = async (params?: ListParams): Promise<ListResponse<SiteType>> => {
    const res = await CompanyDataService.getSiteList(params)

    return res.data ?? defaultListData
  }

  public static readonly getSite = async (id: string): Promise<SiteType> => {
    const { data } = await CompanyDataService.getSite(id)

    return data
  }

  public static readonly getDepartmentList = async (params?: ListParams): Promise<ListResponse<DepartmentType>> => {
    const res = await CompanyDataService.getDepartmentList(params)

    return res.data ?? defaultListData
  }

  public static readonly getDepartment = async (id: string): Promise<DepartmentType> => {
    const { data } = await CompanyDataService.getDepartment(id)

    return data
  }

  public static readonly getSerialNumberList = async (params?: SnParams): Promise<ListResponse<SerialNumberType>> => {
    const { data } = await CompanyDataService.getSerialNumberList(params)

    return data ?? defaultListData
  }

  public static readonly getCurrenciesList = async (params?: ListParams): Promise<ListResponse<CurrenciesType>> => {
    const { data } = await CompanyDataService.getCurrenciesList(params)

    return data ?? defaultListData
  }

  public static readonly getCustomerList = async (params?: ListParams): Promise<ListResponse<CustomerType>> => {
    const { data } = await CompanyDataService.getCustomerList(params)

    return data ?? defaultListData
  }

  public static readonly getCustomer = async (id: string): Promise<CustomerType> => {
    const { data } = await CompanyDataService.getCustomer(id)

    return data
  }

  public static readonly getProjectList = async (params?: ProjectParams): Promise<ListResponse<ProjectType>> => {
    const { data } = await CompanyDataService.getProjectList(params)

    return data ?? defaultListData
  }

  public static readonly getProject = async (id: string): Promise<ProjectType> => {
    const { data } = await CompanyDataService.getProject(id)

    return data
  }

  public static readonly getCarrierList = async (params?: ListParams): Promise<ListResponse<CarrierType>> => {
    const { data } = await CompanyDataService.getCarrierList(params)

    return data ?? defaultListData
  }

  public static readonly getCarrier = async (id: string): Promise<CarrierType> => {
    const { data } = await CompanyDataService.getCarrier(id)

    return data
  }

  public static readonly getCompanyList = async (params?: ListParams): Promise<ListResponse<CompanyType>> => {
    const { data } = await CompanyDataService.getCompanyList(params)

    return data ?? defaultListData
  }

  public static readonly getCompany = async (id: string): Promise<CompanyType> => {
    const { data } = await CompanyDataService.getCompany(id)

    return data
  }
}
