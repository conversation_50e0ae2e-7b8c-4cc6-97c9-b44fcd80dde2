import request from '@/api/request'
import { ApiResponse, ListResponse } from '@/types/api'
import {
  SjPayload,
  SjReceiptPayload,
  SjReceiptType,
  SmSjType,
  SmApprovalPayload,
  SmLogType,
  SmParams,
  SmReceiptPayload,
  SmReceiptType,
  StockMovementPayload,
  StockMovementType
} from '@/pages/material-goods/stock-movement/config/type'
import { ListParams } from '@/types/payload'
import { ApprovalsCountType } from '@/types/appTypes'

export const BASE_MG_URL = 'core/material-goods'

const BASE_SM_URL = 'stock-movements'

export default class SmService {
  public static readonly getStockMovement = (id: string): Promise<ApiResponse<StockMovementType>> => {
    return request({
      url: `${BASE_SM_URL}/${id}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly getStockMovementList = (
    params: SmParams
  ): Promise<ApiResponse<ListResponse<StockMovementType>>> => {
    return request({
      url: `${BASE_SM_URL}`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly addStockMovement = (
    payload: StockMovementPayload
  ): Promise<ApiResponse<StockMovementType>> => {
    return request({
      url: `${BASE_SM_URL}`,
      instance: 'CORE',
      method: 'post',
      data: payload
    })
  }

  public static readonly getToMeStockMovementList = (
    params: SmParams
  ): Promise<ApiResponse<ListResponse<StockMovementType>>> => {
    return request({
      url: `${BASE_SM_URL}/to-me`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly updateStockMovementApprovalStatus = ({
    smId,
    approvalId,
    status,
    takenBy,
    takenImageUploadId
  }: SmApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_SM_URL}/${smId}/approvals/${approvalId}/status`,
      instance: 'CORE',
      method: 'patch',
      data: {
        status,
        ...(takenBy
          ? {
              takenBy,
              takenImageUploadId
            }
          : {})
      }
    })
  }

  public static readonly readStockMovementApproval = ({
    smId,
    approvalId,
    isRead
  }: SmApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_SM_URL}/${smId}/approvals/${approvalId}/read`,
      instance: 'CORE',
      method: 'patch',
      data: {
        isRead
      }
    })
  }

  public static readonly getSmLog = (id: string): Promise<ApiResponse<SmLogType>> => {
    return request({
      url: `${BASE_SM_URL}/logs/${id}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly getSmLogList = (
    smId: string,
    params?: ListParams
  ): Promise<ApiResponse<ListResponse<SmLogType>>> => {
    return request({
      url: `${BASE_SM_URL}/${smId}/logs`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly addSj = (smId: string, payload: SjPayload): Promise<ApiResponse<SmSjType>> => {
    return request({
      url: `${BASE_SM_URL}/${smId}/delivery-notes`,
      instance: 'CORE',
      method: 'post',
      data: payload
    })
  }

  public static readonly getSj = (id: string, smId: string): Promise<ApiResponse<SmSjType>> => {
    return request({
      url: `${BASE_SM_URL}/${smId}/delivery-notes/${id}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly getSjList = (
    smId: string,
    params?: SmParams
  ): Promise<ApiResponse<ListResponse<SmSjType>>> => {
    return request({
      url: `${BASE_SM_URL}/${smId}/delivery-notes`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly receiveSj = (
    mtId: string,
    sjId: string,
    payload: SjReceiptPayload
  ): Promise<ApiResponse<SjReceiptType>> => {
    return request({
      url: `${BASE_SM_URL}/${mtId}/delivery-notes/${sjId}/receipts`,
      instance: 'CORE',
      method: 'patch',
      data: payload
    })
  }

  public static readonly getStockMovementReceiptsList = (
    smId: string,
    params: SmParams
  ): Promise<ApiResponse<ListResponse<SmReceiptType>>> => {
    return request({
      url: `${BASE_SM_URL}/${smId}/receipts`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly receiptStockMovement = (payload: SmReceiptPayload): Promise<ApiResponse<SmReceiptType>> => {
    return request({
      url: `${BASE_SM_URL}/${payload.smId}/receipts`,
      instance: 'CORE',
      method: 'post',
      data: payload
    })
  }

  public static readonly getCountApprovals = (): Promise<ApiResponse<ApprovalsCountType>> => {
    return request({
      method: 'get',
      url: `${BASE_SM_URL}/to-me/waitings-count`,
      instance: 'CORE'
    })
  }
}
