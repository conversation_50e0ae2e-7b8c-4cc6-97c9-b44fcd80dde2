// mutation.ts
import { ApiResponse } from '@/types/api'
import { UseMutationResult, useMutation } from '@tanstack/react-query'
import SmService from './service'
import {
  SjPayload,
  SjReceiptPayload,
  SjReceiptType,
  SmSjType,
  SmApprovalPayload,
  SmReceiptPayload,
  SmReceiptType,
  StockMovementPayload,
  StockMovementType
} from '@/pages/material-goods/stock-movement/config/type'

// New mutations
export const useAddStockMovement = (): UseMutationResult<
  ApiResponse<StockMovementType>,
  Error,
  StockMovementPayload,
  void
> => {
  return useMutation({
    mutationFn: (payload: StockMovementPayload) => {
      return SmService.addStockMovement(payload)
    }
  })
}

export const useUpdateStockMovementApprovalStatus = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  SmApprovalPayload,
  void
> => {
  return useMutation({
    mutationFn: (payload: SmApprovalPayload) => {
      return SmService.updateStockMovementApprovalStatus(payload)
    }
  })
}

export const useReadStockMovementApproval = (): UseMutationResult<ApiResponse<any>, Error, SmApprovalPayload, void> => {
  return useMutation({
    mutationFn: (payload: SmApprovalPayload) => {
      return SmService.readStockMovementApproval(payload)
    }
  })
}

export const useAddSj = (): UseMutationResult<ApiResponse<SmSjType>, Error, [string, SjPayload], void> => {
  return useMutation({
    mutationFn: ([smId, payload]: [string, SjPayload]) => {
      return SmService.addSj(smId, payload)
    }
  })
}

export const useReceiveSj = (): UseMutationResult<
  ApiResponse<SjReceiptType>,
  Error,
  [string, string, SjPayload],
  void
> => {
  return useMutation({
    mutationFn: ([smId, sjId, payload]: [string, string, SjReceiptPayload]) => {
      return SmService.receiveSj(smId, sjId, payload)
    }
  })
}

export const useReceiptStockMovement = (): UseMutationResult<
  ApiResponse<SmReceiptType>,
  Error,
  SmReceiptPayload,
  void
> => {
  return useMutation({
    mutationFn: (payload: SmReceiptPayload) => {
      return SmService.receiptStockMovement(payload)
    }
  })
}
