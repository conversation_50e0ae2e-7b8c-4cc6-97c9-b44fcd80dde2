import SmService from './service'
import { ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import {
  SmSjType,
  SmLogType,
  SmParams,
  SmReceiptType,
  StockMovementType
} from '@/pages/material-goods/stock-movement/config/type'
import { ListParams } from '@/types/payload'
import { ApprovalsCountType } from '@/types/appTypes'

export const SM_QUERY_KEY = 'SM_QUERY_KEY'
export const SM_LIST_QUERY_KEY = 'SM_LIST_QUERY_KEY'
export const SM_TO_ME_LIST_QUERY_KEY = 'SM_TO_ME_LIST_QUERY_KEY'
export const SM_RECEIPT_LIST_QUERY_KEY = 'SM_RECEIPT_LIST_QUERY_KEY'
export const SM_LOG_LIST_QUERY_KEY = 'SM_LOG_LIST_QUERY_KEY'

export default class SmQueryMethods {
  public static readonly getStockMovement = async (id: string): Promise<StockMovementType> => {
    const { data } = await SmService.getStockMovement(id)
    return data
  }

  public static readonly getStockMovementList = async (params?: SmParams): Promise<ListResponse<StockMovementType>> => {
    const res = await SmService.getStockMovementList(params)
    return res.data ?? defaultListData
  }

  public static readonly getToMeStockMovementList = async (
    params?: SmParams
  ): Promise<ListResponse<StockMovementType>> => {
    const res = await SmService.getToMeStockMovementList(params)
    return res.data ?? defaultListData
  }

  public static readonly getSmLog = async (id: string): Promise<SmLogType> => {
    const { data } = await SmService.getSmLog(id)
    return data
  }

  public static readonly getSmLogList = async (smId: string, params?: ListParams): Promise<SmLogType[]> => {
    const { data } = await SmService.getSmLogList(smId, params)
    return data.items ?? []
  }

  public static readonly getSj = async (id: string, smId: string): Promise<SmSjType> => {
    const { data } = await SmService.getSj(id, smId)
    return data
  }

  public static readonly getSjList = async (smId: string, params?: SmParams): Promise<SmSjType[]> => {
    const { data } = await SmService.getSjList(smId, params)
    return data?.items ?? []
  }

  public static readonly getStockMovementReceiptList = async (
    smId: string,
    params?: SmParams
  ): Promise<SmReceiptType[]> => {
    const { data } = await SmService.getStockMovementReceiptsList(smId, params)
    return data.items ?? []
  }

  public static readonly getCountApprovals = async (): Promise<ApprovalsCountType> => {
    const { data } = await SmService.getCountApprovals()
    return data
  }
}
