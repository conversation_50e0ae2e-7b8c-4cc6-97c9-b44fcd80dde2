import { defaultListData } from '@/api/queryClient'
import SoService from './service'
import { SoItemParams, SoItemType, SoParams, SoType } from '@/types/soTypes'
import { ListResponse } from '@/types/api'

export const SO_QUERY_KEY = 'SO_QUERY_KEY'
export const SO_LIST_QUERY_KEY = 'SO_LIST_QUERY_KEY'
export const SO_ITEM_LIST_QUERY_KEY = 'SO_ITEM_LIST_QUERY_KEY'
export const SO_ITEM_QUERY_KEY = 'SO_ITEM_QUERY_KEY'

export default class SoQueryMethods {
  public static readonly getSo = async (id: string): Promise<SoType> => {
    const { data } = await SoService.getSo(id)
    return data
  }

  public static readonly getSoList = async (params: SoParams): Promise<ListResponse<SoType>> => {
    const { data } = await SoService.getSoList(params)
    return data ?? defaultListData
  }

  public static readonly getSoItemList = async (
    soId: string,
    params: SoItemParams
  ): Promise<ListResponse<SoItemType>> => {
    const { data } = await SoService.getSoItemList(soId, params)
    return data ?? defaultListData
  }

  public static readonly getSoItem = async (soId: string, itemId: string): Promise<SoItemType> => {
    const { data } = await SoService.getSoItem(soId, itemId)
    return data
  }
}
