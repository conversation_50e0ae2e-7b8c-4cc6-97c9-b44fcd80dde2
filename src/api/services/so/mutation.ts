import { ApiResponse } from '@/types/api'
import { SoPayload, SoType } from '@/types/soTypes'
import { useMutation, UseMutationResult } from '@tanstack/react-query'
import SoService from './service'

export const useAddSo = (): UseMutationResult<ApiResponse<SoType>, Error, SoPayload, void> => {
  return useMutation({
    mutationFn: (payload: SoPayload) => {
      return SoService.addSo(payload)
    }
  })
}
