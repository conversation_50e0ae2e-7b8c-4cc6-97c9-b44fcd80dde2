import request from '@/api/request'
import { ApiResponse, ListResponse } from '@/types/api'
import { SoItemParams, SoItemType, SoParams, SoPayload, SoType } from '@/types/soTypes'

const BASE_URL = 'stock-opnames'

export default class SoService {
  public static readonly addSo = (payload: SoPayload): Promise<ApiResponse<SoType>> => {
    return request({
      url: BASE_URL,
      instance: 'CORE',
      method: 'post',
      data: payload
    })
  }

  public static readonly getSoList = (params: SoParams): Promise<ApiResponse<ListResponse<SoType>>> => {
    return request({
      url: BASE_URL,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getSo = (soId: string): Promise<ApiResponse<SoType>> => {
    return request({
      url: `${BASE_URL}/${soId}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly getSoItemList = (
    soId: string,
    params: SoItemParams
  ): Promise<ApiResponse<ListResponse<SoItemType>>> => {
    return request({
      url: `${BASE_URL}/${soId}/items`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getSoItem = (soId: string, itemId: string): Promise<ApiResponse<SoItemType>> => {
    return request({
      url: `${BASE_URL}/${soId}/items/${itemId}`,
      instance: 'CORE',
      method: 'get'
    })
  }
}
