import { ApiResponse } from '@/types/api'
import { UseMutationResult, useMutation } from '@tanstack/react-query'
import DpService from './service'
import { DirectPurchasePayload, DirectPurchaseType, DpApprovalPayload } from '@/pages/direct-purchase/config/types'

export const useAddDirectPurchase = (): UseMutationResult<
  ApiResponse<DirectPurchaseType>,
  Error,
  DirectPurchasePayload,
  void
> => {
  return useMutation({
    mutationFn: (payload: DirectPurchasePayload) => {
      return DpService.addDirectPurchase(payload)
    }
  })
}

export const useUpdateDirectPurchase = (): UseMutationResult<
  ApiResponse<DirectPurchaseType>,
  Error,
  DirectPurchasePayload,
  void
> => {
  return useMutation({
    mutationFn: (payload: DirectPurchasePayload) => {
      return DpService.updateDirectPurchase(payload)
    }
  })
}

export const useUpdateDirectPurchaseApprover = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  DpApprovalPayload,
  void
> => {
  return useMutation({
    mutationFn: (payload: DpApprovalPayload) => {
      return DpService.updateDirectPurchaseApprover(payload)
    }
  })
}

export const useUpdateDirectPurchaseApprovalStatus = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  DpApprovalPayload,
  void
> => {
  return useMutation({
    mutationFn: (payload: DpApprovalPayload) => {
      return DpService.updateDirectPurchaseApprovalStatus(payload)
    }
  })
}

export const useReadDirectPurchaseApproval = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  DpApprovalPayload,
  void
> => {
  return useMutation({
    mutationFn: (payload: DpApprovalPayload) => {
      return DpService.readDirectPurchaseApproval(payload)
    }
  })
}
