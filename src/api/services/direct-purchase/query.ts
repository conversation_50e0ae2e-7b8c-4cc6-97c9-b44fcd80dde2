import DpService from './service'
import { ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import { DirectPurchaseType, DpParams } from '@/pages/direct-purchase/config/types'

export const DP_QUERY_KEY = 'DP_QUERY_KEY'
export const DP_LIST_QUERY_KEY = 'DP_LIST_QUERY_KEY'
export const DP_BY_ME_LIST_QUERY_KEY = 'DP_BY_ME_LIST_QUERY_KEY'
export const DP_TO_ME_LIST_QUERY_KEY = 'DP_TO_ME_LIST_QUERY_KEY'
export const MR_LOG_QUERY_KEY = 'MR_LOG_QUERY_KEY'
export const MR_LOG_LIST_QUERY_KEY = 'MR_LOG_LIST_QUERY_KEY'

export default class DpQueryMethods {
  public static readonly getDirectPurchase = async (id: string): Promise<DirectPurchaseType> => {
    const { data } = await DpService.getDirectPurchase(id)
    return data
  }

  public static readonly getDirectPurchaseList = async (
    params?: DpParams
  ): Promise<ListResponse<DirectPurchaseType>> => {
    const res = await DpService.getDirectPurchaseList(params)
    return res.data ?? defaultListData
  }

  public static readonly getByMeDirectPurchaseList = async (
    params?: DpParams
  ): Promise<ListResponse<DirectPurchaseType>> => {
    const res = await DpService.getByMeDirectPurchaseList(params)
    return res.data ?? defaultListData
  }

  public static readonly getToMeDirectPurchaseList = async (
    params?: DpParams
  ): Promise<ListResponse<DirectPurchaseType>> => {
    const res = await DpService.getToMeDirectPurchaseList(params)
    return res.data ?? defaultListData
  }

  // public static readonly getMrLog = async (id: string): Promise<MrLogType> => {
  //   const { data } = await DpService.getMrLog(id)
  //   return data
  // }

  // public static readonly getMrLogList = async (mrId: string, params?: ListParams): Promise<MrLogType[]> => {
  //   const { data } = await DpService.getMrLogList(mrId, params)
  //   return data.items ?? []
  // }
}
