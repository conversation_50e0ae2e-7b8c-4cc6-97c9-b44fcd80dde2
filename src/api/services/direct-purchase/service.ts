import request from '@/api/request'
import {
  DirectPurchasePayload,
  DirectPurchaseType,
  DpApprovalPayload,
  DpParams
} from '@/pages/direct-purchase/config/types'
import { ApiResponse, ListResponse } from '@/types/api'

export const BASE_DP_URL = 'direct-purchases'

export default class DpService {
  public static readonly addDirectPurchase = (
    payload: DirectPurchasePayload
  ): Promise<ApiResponse<DirectPurchaseType>> => {
    return request({
      url: `${BASE_DP_URL}`,
      instance: 'CORE',
      method: 'post',
      data: payload
    })
  }

  public static readonly updateDirectPurchase = ({
    dpId,
    ...payload
  }: DirectPurchasePayload): Promise<ApiResponse<DirectPurchaseType>> => {
    return request({
      url: `${BASE_DP_URL}/${dpId}`,
      instance: 'CORE',
      method: 'patch',
      data: payload
    })
  }

  public static readonly getDirectPurchase = (id: string): Promise<ApiResponse<DirectPurchaseType>> => {
    return request({
      url: `${BASE_DP_URL}/${id}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly getDirectPurchaseList = (
    params?: DpParams
  ): Promise<ApiResponse<ListResponse<DirectPurchaseType>>> => {
    return request({
      url: `${BASE_DP_URL}`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getByMeDirectPurchaseList = (
    params?: DpParams
  ): Promise<ApiResponse<ListResponse<DirectPurchaseType>>> => {
    return request({
      url: `${BASE_DP_URL}/by-me`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getToMeDirectPurchaseList = (
    params?: DpParams
  ): Promise<ApiResponse<ListResponse<DirectPurchaseType>>> => {
    return request({
      url: `${BASE_DP_URL}/to-me`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly updateDirectPurchaseApprover = ({
    dpId: mrId,
    approvalId,
    ...payload
  }: DpApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_DP_URL}/${mrId}/approvals/${approvalId}`,
      instance: 'CORE',
      method: 'patch',
      data: payload
    })
  }

  public static readonly updateDirectPurchaseApprovalStatus = ({
    dpId: mrId,
    approvalId,
    status
  }: DpApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_DP_URL}/${mrId}/approvals/${approvalId}/status`,
      instance: 'CORE',
      method: 'patch',
      data: {
        status
      }
    })
  }

  public static readonly readDirectPurchaseApproval = ({
    dpId: mrId,
    approvalId,
    isRead
  }: DpApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      instance: 'CORE',
      url: `${BASE_DP_URL}/${mrId}/approvals/${approvalId}/read`,
      method: 'patch',
      data: {
        isRead
      }
    })
  }

  // public static readonly getMrLog = (id: string): Promise<ApiResponse<MrLogType>> => {
  //   return request({
  //     url: `${BASE_DP_URL}/logs/${id}`,
  //     method: 'get'
  //   })
  // }

  // public static readonly getMrLogList = (
  //   mrId: string,
  //   params?: ListParams
  // ): Promise<ApiResponse<ListResponse<MrLogType>>> => {
  //   return request({
  //     url: `${BASE_DP_URL}/${mrId}/logs`,
  //     method: 'get',
  //     params
  //   })
  // }
}
