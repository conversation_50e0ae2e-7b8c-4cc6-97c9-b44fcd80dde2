import request from '@/api/request'
import { ApiResponse, ListResponse } from '@/types/api'
import { NumberFormatDto, NumberFormatType } from '@/types/numberingTypes'

const BASE_URL = 'number-formats'

export default class NumberingService {
  public static readonly getNumberFormat = (scope: string): Promise<ApiResponse<NumberFormatType>> => {
    return request({
      url: BASE_URL + '/' + scope,
      instance: 'CORE',
      method: 'get'
    })
  }
  public static readonly upsertNumberFormat = (
    scope: string,
    data: NumberFormatDto
  ): Promise<ApiResponse<NumberFormatType>> => {
    return request({
      url: BASE_URL + '/' + scope,
      instance: 'CORE',
      method: 'put',
      data
    })
  }
}
