import { ApiResponse } from '@/types/api'
import { SoPayload, SoType } from '@/types/soTypes'
import { useMutation, UseMutationResult } from '@tanstack/react-query'
import NumberService from './service'
import { NumberFormatDto, NumberFormatType } from '@/types/numberingTypes'

export const useUpsertNumberFormat = (): UseMutationResult<
  ApiResponse<NumberFormatType>,
  Error,
  { scope: string; payload: NumberFormatDto },
  void
> => {
  return useMutation({
    mutationFn: ({ scope, payload }: { scope: string; payload: NumberFormatDto }) => {
      return NumberService.upsertNumberFormat(scope, payload)
    }
  })
}
