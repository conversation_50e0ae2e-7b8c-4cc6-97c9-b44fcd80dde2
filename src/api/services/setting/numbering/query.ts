import { defaultListData } from '@/api/queryClient'
import NumberingService from './service'
import { NumberFormatType } from '@/types/numberingTypes'

export const NumberFormat_QUERY_KEY = 'NumberFormat_QUERY_KEY'

export default class NumberFormatQueryMethods {
  public static readonly getNumberFormat = async (scope: string): Promise<NumberFormatType> => {
    const { data } = await NumberingService.getNumberFormat(scope)
    return data
  }
}
