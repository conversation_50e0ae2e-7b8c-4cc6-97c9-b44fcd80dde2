import request from '@/api/request'
import {
  CreatePaymentPayload,
  PaymentParams,
  PaymentType,
  PaymentApprovalPayload
} from '@/pages/cash-bank/payments/config/types'
import { CashReceiptParams, CashReceiptType, CreateCashReceiptPayload } from '@/pages/cash-bank/receipt/config/types'
import { ApiResponse, ListResponse } from '@/types/api'
import { ApprovalsCountType } from '@/types/appTypes'

const BASE_URL_PAYMENTS = 'payments'
const BASE_URL_CASH_RECEIPT = 'cash-receipts'

export default class CashBankService {
  public static getPaymentsList = (params: PaymentParams): Promise<ApiResponse<ListResponse<PaymentType>>> => {
    return request({
      method: 'get',
      instance: 'CORE',
      url: `${BASE_URL_PAYMENTS}`,
      params
    })
  }

  public static getPayment = (id: string): Promise<ApiResponse<PaymentType>> => {
    return request({
      method: 'get',
      instance: 'CORE',
      url: `${BASE_URL_PAYMENTS}/${id}`
    })
  }

  public static createPayment = (payload: CreatePaymentPayload): Promise<ApiResponse<PaymentType>> => {
    return request({
      method: 'post',
      instance: 'CORE',
      url: `${BASE_URL_PAYMENTS}`,
      data: payload
    })
  }

  public static getPaymentToMe = (params: PaymentParams): Promise<ApiResponse<ListResponse<PaymentType>>> => {
    return request({
      method: 'get',
      instance: 'CORE',
      url: `${BASE_URL_PAYMENTS}/to-me`,
      params
    })
  }

  public static updatePaymentApprovalStatus = ({
    paymentId,
    approvalId,
    status
  }: PaymentApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_URL_PAYMENTS}/${paymentId}/approvals/${approvalId}/status`,
      instance: 'CORE',
      method: 'patch',
      data: {
        status
      }
    })
  }

  public static readonly readPaymentApproval = ({
    paymentId,
    approvalId,
    isRead
  }: Omit<PaymentApprovalPayload, 'status'> & { isRead: boolean }): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_URL_PAYMENTS}/${paymentId}/approvals/${approvalId}/read`,
      instance: 'CORE',
      method: 'patch',
      data: {
        isRead
      }
    })
  }

  public static readonly getCountApprovals = (): Promise<ApiResponse<ApprovalsCountType>> => {
    return request({
      method: 'get',
      url: `${BASE_URL_PAYMENTS}/to-me/waitings-count`,
      instance: 'CORE'
    })
  }

  public static readonly createCashReceipt = (
    payload: CreateCashReceiptPayload
  ): Promise<ApiResponse<CashReceiptType>> => {
    return request({
      method: 'post',
      instance: 'CORE',
      url: `${BASE_URL_CASH_RECEIPT}`,
      data: payload
    })
  }

  public static readonly getCashReceiptList = (
    params: CashReceiptParams
  ): Promise<ApiResponse<ListResponse<CashReceiptType>>> => {
    return request({
      method: 'get',
      instance: 'CORE',
      url: `${BASE_URL_CASH_RECEIPT}`,
      params
    })
  }

  public static readonly getCashReceiptToMe = (
    params: CashReceiptParams
  ): Promise<ApiResponse<ListResponse<CashReceiptType>>> => {
    return request({
      method: 'get',
      instance: 'CORE',
      url: `${BASE_URL_CASH_RECEIPT}/to-me`,
      params
    })
  }

  public static readonly getCashReceipt = (id: string): Promise<ApiResponse<CashReceiptType>> => {
    return request({
      method: 'get',
      instance: 'CORE',
      url: `${BASE_URL_CASH_RECEIPT}/${id}`
    })
  }

  public static readonly readCashReceipt = ({
    id,
    approvalId,
    isRead
  }: {
    id: string
    isRead: boolean
    approvalId: number
  }): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_URL_CASH_RECEIPT}/${id}/approvals/${approvalId}/read`,
      instance: 'CORE',
      method: 'patch',
      data: {
        isRead
      }
    })
  }

  public static readonly updateStatusCashReceipt = ({
    id,
    approvalId,
    status
  }: {
    id: string
    approvalId: number
    status: string
  }): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_URL_CASH_RECEIPT}/${id}/approvals/${approvalId}/status`,
      instance: 'CORE',
      method: 'patch',
      data: {
        status
      }
    })
  }

  public static readonly getCountCashReceipt = (): Promise<ApiResponse<ApprovalsCountType>> => {
    return request({
      method: 'get',
      url: `${BASE_URL_CASH_RECEIPT}/to-me/waitings-count`,
      instance: 'CORE'
    })
  }
}
