import { CreatePaymentPayload, PaymentType, PaymentApprovalPayload } from '@/pages/cash-bank/payments/config/types'
import { ApiResponse } from '@/types/api'
import { UseMutationResult, useMutation } from '@tanstack/react-query'
import CashBankService from './service'
import { CashReceiptType, CreateCashReceiptPayload } from '@/pages/cash-bank/receipt/config/types'

export const useCreatePayment = (): UseMutationResult<ApiResponse<PaymentType>, Error, CreatePaymentPayload, void> => {
  return useMutation({
    mutationFn: data => CashBankService.createPayment(data)
  })
}

export const useUpdateApprovalPaymentStatus = (): UseMutationResult<
  ApiResponse<PaymentType>,
  Error,
  PaymentApprovalPayload,
  void
> => {
  return useMutation({
    mutationFn: data => CashBankService.updatePaymentApprovalStatus(data)
  })
}

export const useReadPaymentApproval = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  Omit<PaymentApprovalPayload, 'status'> & { isRead: boolean },
  void
> => {
  return useMutation({
    mutationFn: data => CashBankService.readPaymentApproval(data)
  })
}

export const useCreateCashReceipt = (): UseMutationResult<
  ApiResponse<CashReceiptType>,
  Error,
  CreateCashReceiptPayload,
  void
> => {
  return useMutation({
    mutationFn: data => CashBankService.createCashReceipt(data)
  })
}

export const useUpdateCashReceiptStatus = (): UseMutationResult<
  ApiResponse<CashReceiptType>,
  Error,
  { id: string; approvalId: number; status: string },
  void
> => {
  return useMutation({
    mutationFn: data => CashBankService.updateStatusCashReceipt(data)
  })
}

export const useReadCashReceipt = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  { id: string; approvalId: number; isRead: boolean },
  void
> => {
  return useMutation({
    mutationFn: data => CashBankService.readCashReceipt(data)
  })
}
