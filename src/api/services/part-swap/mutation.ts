import { ApiResponse } from '@/types/api'
import { PartSwapType, WoPartSwapDtoType } from '@/types/partSwapTypes'
import { useMutation, UseMutationResult } from '@tanstack/react-query'
import PartSwapServices from './service'

export const useCreatePartSwap = (): UseMutationResult<ApiResponse<PartSwapType>, Error, WoPartSwapDtoType, void> => {
  return useMutation({
    mutationFn: (payload: WoPartSwapDtoType) => {
      return PartSwapServices.createPartSwap(payload)
    }
  })
}

export const useUpdatePartSwapCancelState = (): UseMutationResult<
  ApiResponse<PartSwapType>,
  Error,
  { partSwapId: string; cancelationNote: string },
  void
> => {
  return useMutation({
    mutationFn: (payload: { partSwapId: string; cancelationNote: string }) => {
      return PartSwapServices.updatePartSwapCancel(payload)
    }
  })
}

export const useUpdatePartSwapApprovalStatus = (): UseMutationResult<
  ApiResponse<PartSwapType>,
  Error,
  { partSwapId: string; approvalId: number; status: string; rejectionNote: string },
  void
> => {
  return useMutation({
    mutationFn: (payload: { partSwapId: string; approvalId: number; status: string; rejectionNote: string }) => {
      return PartSwapServices.updatePartSwapApprovalStatus(payload)
    }
  })
}

export const useUpdatePartSwapApprover = (): UseMutationResult<
  ApiResponse<PartSwapType>,
  Error,
  { partSwapId: string; approvalId: number; userId: string; note: string },
  void
> => {
  return useMutation({
    mutationFn: (payload: { partSwapId: string; approvalId: number; userId: string; note: string }) => {
      return PartSwapServices.updatePartSwapApprover(payload)
    }
  })
}
