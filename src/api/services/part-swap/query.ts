import { ApiResponse, ListResponse } from '@/types/api'
import PartSwapServices from './service'
import { PartSwapLogType, PartSwapType } from '@/types/partSwapTypes'
import { ListParams } from '@/types/payload'
import { ApprovalsCountType } from '@/types/appTypes'

export default class PartSwapQueryMethods {
  public static readonly getPartSwapList = async (params: any): Promise<ListResponse<PartSwapType>> => {
    const res = await PartSwapServices.getPartSwapList(params)
    return res.data
  }

  public static readonly getPartSwapListToMe = async (params: any): Promise<ListResponse<PartSwapType>> => {
    const res = await PartSwapServices.getPartSwapListToMe(params)
    return res.data
  }

  public static readonly getPartSwap = async (id: string): Promise<PartSwapType> => {
    const res = await PartSwapServices.getPartSwap(id)
    return res.data
  }

  public static readonly getPartSwapLogs = async (
    params: ListParams & { partSwapId: string }
  ): Promise<ListResponse<PartSwapLogType>> => {
    const res = await PartSwapServices.getPartSwapLogs(params)
    return res.data
  }

  public static readonly getCountApprovals = async (): Promise<ApprovalsCountType> => {
    const res = await PartSwapServices.getCountApprovals()
    return res.data
  }
}
