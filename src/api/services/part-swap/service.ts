import request from '@/api/request'
import { ApiResponse, ListResponse } from '@/types/api'
import { ApprovalsCountType } from '@/types/appTypes'
import { PartSwapLogType, PartSwapParams, PartSwapType, WoPartSwapDtoType } from '@/types/partSwapTypes'
import { ListParams } from '@/types/payload'

export const PART_SWAP_QUERY_KEY = 'PART_SWAP_QUERY_KEY'
export const PART_SWAP_QUERY_LIST_KEY = 'PART_SWAP_QUERY_LIST_KEY'

const BASE_URL = 'part-swaps'

export default class PartSwapServices {
  public static readonly createPartSwap = (payload: WoPartSwapDtoType): Promise<ApiResponse<PartSwapType>> => {
    return request({
      url: BASE_URL,
      instance: 'CORE',
      method: 'post',
      data: payload
    })
  }
  public static readonly getPartSwapList = (
    payload: PartSwapParams
  ): Promise<ApiResponse<ListResponse<PartSwapType>>> => {
    return request({
      url: `${BASE_URL}`,
      instance: 'CORE',
      method: 'get',
      params: payload
    })
  }

  public static readonly getPartSwapListToMe = (
    payload: PartSwapParams
  ): Promise<ApiResponse<ListResponse<PartSwapType>>> => {
    return request({
      url: `${BASE_URL}/to-me`,
      instance: 'CORE',
      method: 'get',
      params: payload
    })
  }

  public static readonly getPartSwap = (id: string): Promise<ApiResponse<PartSwapType>> => {
    return request({
      url: `${BASE_URL}/${id}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly getPartSwapLogs = (
    params: ListParams & { partSwapId: string }
  ): Promise<ApiResponse<ListResponse<PartSwapLogType>>> => {
    return request({
      url: `${BASE_URL}/${params.partSwapId}/logs`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly updatePartSwapCancel = ({
    partSwapId,
    cancelationNote
  }: {
    partSwapId: string
    cancelationNote: string
  }) => {
    return request({
      url: `${BASE_URL}/${partSwapId}/cancel`,
      instance: 'CORE',
      method: 'patch',
      data: {
        cancelationNote
      }
    })
  }

  public static readonly updatePartSwapApprovalStatus = ({
    partSwapId,
    approvalId,
    status,
    rejectionNote
  }: {
    partSwapId: string
    approvalId: number
    status: string
    rejectionNote: string
  }) => {
    return request({
      url: `${BASE_URL}/${partSwapId}/approvals/${approvalId}/status`,
      instance: 'CORE',
      method: 'patch',
      data: {
        status,
        rejectionNote
      }
    })
  }

  public static readonly updatePartSwapApprover = ({
    partSwapId,
    approvalId,
    userId,
    note
  }: {
    partSwapId: string
    approvalId: number
    userId: string
    note: string
  }) => {
    return request({
      url: `${BASE_URL}/${partSwapId}/approvals/${approvalId}`,
      instance: 'CORE',
      method: 'patch',
      data: {
        userId,
        note
      }
    })
  }

  public static readonly getCountApprovals = (): Promise<ApiResponse<ApprovalsCountType>> => {
    return request({
      url: `${BASE_URL}/to-me/waitings-count`,
      instance: 'CORE',
      method: 'get'
    })
  }
}
