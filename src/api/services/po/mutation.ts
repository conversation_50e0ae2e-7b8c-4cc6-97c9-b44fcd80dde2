import { ApiResponse } from '@/types/api'
import { UseMutationResult, useMutation } from '@tanstack/react-query'
import PoService from './service'
import { CancelPoPayload, PoApprovalPayload, PoItemType, PoPayload, PoType } from '@/pages/purchase-order/config/types'

export const useAddPo = (): UseMutationResult<ApiResponse<PoType>, Error, PoPayload, void> => {
  return useMutation({
    mutationFn: (payload: PoPayload) => {
      return PoService.addPo(payload)
    }
  })
}

export const useUpdatePo = (): UseMutationResult<ApiResponse<PoType>, Error, PoPayload, void> => {
  return useMutation({
    mutationFn: (payload: PoPayload) => {
      return PoService.updatePo(payload)
    }
  })
}

export const useDeletePo = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (poId: string) => {
      return PoService.deletePo(poId)
    }
  })
}

export const useUpdatePoItem = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  { poId: string; payload: PoItemType },
  void
> => {
  return useMutation({
    mutationFn: ({ poId, payload }) => {
      return PoService.updatePoItem(poId, payload)
    }
  })
}

export const useDeletePoItem = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  { poId: string; itemId: number },
  void
> => {
  return useMutation({
    mutationFn: ({ poId, itemId }) => {
      return PoService.deletePoItem(poId, itemId)
    }
  })
}

export const useUpdatePoApprover = (): UseMutationResult<ApiResponse<any>, Error, PoApprovalPayload, void> => {
  return useMutation({
    mutationFn: (payload: PoApprovalPayload) => {
      return PoService.updatePoApprover(payload)
    }
  })
}

export const useUpdatePoApprovalStatus = (): UseMutationResult<ApiResponse<any>, Error, PoApprovalPayload, void> => {
  return useMutation({
    mutationFn: (payload: PoApprovalPayload) => {
      return PoService.updatePoApprovalStatus(payload)
    }
  })
}

export const useUpdatePoCancelationStatus = (): UseMutationResult<ApiResponse<any>, Error, PoApprovalPayload, void> => {
  return useMutation({
    mutationFn: (payload: PoApprovalPayload) => {
      return PoService.updatePoCancelationStatus(payload)
    }
  })
}

export const useReadPoApproval = (): UseMutationResult<ApiResponse<any>, Error, PoApprovalPayload, void> => {
  return useMutation({
    mutationFn: (payload: PoApprovalPayload) => {
      return PoService.readPoApproval(payload)
    }
  })
}

export const useCancelPo = (): UseMutationResult<ApiResponse<any>, Error, CancelPoPayload, void> => {
  return useMutation({
    mutationFn: (payload: CancelPoPayload) => {
      return PoService.cancelPo(payload)
    }
  })
}

export const useClosePo = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (poId: string) => {
      return PoService.closePo(poId)
    }
  })
}
