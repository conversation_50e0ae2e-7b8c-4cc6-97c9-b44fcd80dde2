import PoService from './service'
import { ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import { ListParams } from '@/types/payload'
import { PoLogType, PoParams, PoType } from '@/pages/purchase-order/config/types'
import { ApprovalsCountType } from '@/types/appTypes'

export const PO_QUERY_KEY = 'PO_QUERY_KEY'
export const PO_LIST_QUERY_KEY = 'PO_LIST_QUERY_KEY'
export const PO_BY_ME_LIST_QUERY_KEY = 'PO_BY_ME_LIST_QUERY_KEY'
export const PO_TO_ME_LIST_QUERY_KEY = 'PO_TO_ME_LIST_QUERY_KEY'
export const PO_LOG_QUERY_KEY = 'PO_LOG_QUERY_KEY'
export const PO_LOG_LIST_QUERY_KEY = 'PO_LOG_LIST_QUERY_KEY'

export default class PoQueryMethods {
  public static readonly getPo = async (id: string, isCancelation?: boolean): Promise<PoType> => {
    const { data } = await PoService.getPo(id, isCancelation)
    return data
  }

  public static readonly getPoList = async (params?: PoParams): Promise<ListResponse<PoType>> => {
    const res = await PoService.getPoList(params)
    return res.data ?? defaultListData
  }

  public static readonly getByMePoList = async (params?: PoParams): Promise<ListResponse<PoType>> => {
    const res = await PoService.getByMePoList(params)
    return res.data ?? defaultListData
  }

  public static readonly getToMePoList = async (params?: PoParams): Promise<ListResponse<PoType>> => {
    const res = await PoService.getToMePoList(params)
    return res.data ?? defaultListData
  }

  public static readonly getPoLog = async (id: string): Promise<PoLogType> => {
    const { data } = await PoService.getPoLog(id)
    return data
  }

  public static readonly getPoLogList = async (prId: string, params?: ListParams): Promise<PoLogType[]> => {
    const { data } = await PoService.getPoLogList(prId, params)
    return data.items ?? []
  }

  public static readonly getCountApprovals = async (): Promise<ApprovalsCountType> => {
    const { data } = await PoService.getCountApprovals()
    return data
  }
}
