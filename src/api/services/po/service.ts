import request from '@/api/request'
import {
  CancelPoPayload,
  PoApprovalPayload,
  PoItemType,
  PoLogType,
  PoParams,
  PoPayload,
  PoType
} from '@/pages/purchase-order/config/types'
import { ApiResponse, ListResponse } from '@/types/api'
import { ApprovalsCountType } from '@/types/appTypes'
import { ListParams } from '@/types/payload'

export const BASE_PO_URL = 'purchase-orders'

export default class PoService {
  public static readonly addPo = (payload: PoPayload): Promise<ApiResponse<PoType>> => {
    return request({
      url: `${BASE_PO_URL}`,
      instance: 'CORE',
      method: 'post',
      data: payload
    })
  }

  public static readonly updatePo = ({ poId, ...payload }: PoPayload): Promise<ApiResponse<PoType>> => {
    return request({
      url: `${BASE_PO_URL}/${poId}`,
      instance: 'CORE',
      method: 'patch',
      data: payload
    })
  }

  public static readonly deletePo = (poId: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_PO_URL}/${poId}`,
      instance: 'CORE',
      method: 'delete'
    })
  }

  public static readonly getPo = (id: string, isCancelation?: boolean): Promise<ApiResponse<PoType>> => {
    return request({
      url: `${BASE_PO_URL}/${id}`,
      instance: 'CORE',
      method: 'get',
      params: isCancelation
        ? {
            isCancelation
          }
        : {}
    })
  }

  public static readonly getPoList = (params?: PoParams): Promise<ApiResponse<ListResponse<PoType>>> => {
    return request({
      url: `${BASE_PO_URL}`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getByMePoList = (params?: PoParams): Promise<ApiResponse<ListResponse<PoType>>> => {
    return request({
      url: `${BASE_PO_URL}/by-me`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getToMePoList = (params?: PoParams): Promise<ApiResponse<ListResponse<PoType>>> => {
    return request({
      url: `${BASE_PO_URL}/to-me`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly cancelPo = ({ poId, ...payload }: CancelPoPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_PO_URL}/${poId}/cancel`,
      instance: 'CORE',
      method: 'patch',
      data: payload
    })
  }

  public static readonly closePo = (poId: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_PO_URL}/${poId}/close`,
      instance: 'CORE',
      method: 'patch'
    })
  }

  public static readonly updatePoItem = (
    poId: string,
    { id, ...itemPayload }: PoItemType
  ): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_PO_URL}/${poId}/items/${id}`,
      instance: 'CORE',
      method: 'patch',
      data: itemPayload
    })
  }

  public static readonly deletePoItem = (poId: string, itemId: number): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_PO_URL}/${poId}/items/${itemId}`,
      instance: 'CORE',
      method: 'delete'
    })
  }

  public static readonly updatePoApprover = ({
    poId,
    approvalId,
    ...payload
  }: PoApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_PO_URL}/${poId}/approvals/${approvalId}`,
      instance: 'CORE',
      method: 'patch',
      data: payload
    })
  }

  public static readonly updatePoApprovalStatus = ({
    poId,
    approvalId,
    status
  }: PoApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_PO_URL}/${poId}/approvals/${approvalId}/status`,
      instance: 'CORE',
      method: 'patch',
      data: {
        status
      }
    })
  }

  public static readonly updatePoCancelationStatus = ({
    poId,
    approvalId,
    status
  }: PoApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_PO_URL}/${poId}/approvals/${approvalId}/cancel-status`,
      instance: 'CORE',
      method: 'patch',
      data: {
        status
      }
    })
  }

  public static readonly readPoApproval = ({
    poId,
    approvalId,
    isRead
  }: PoApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_PO_URL}/${poId}/approvals/${approvalId}/read`,
      instance: 'CORE',
      method: 'patch',
      data: {
        isRead
      }
    })
  }

  public static readonly getPoLog = (id: string): Promise<ApiResponse<PoLogType>> => {
    return request({
      url: `${BASE_PO_URL}/logs/${id}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly getPoLogList = (
    poId: string,
    params?: ListParams
  ): Promise<ApiResponse<ListResponse<PoLogType>>> => {
    return request({
      url: `${BASE_PO_URL}/${poId}/logs`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getCountApprovals = (): Promise<ApiResponse<ApprovalsCountType>> => {
    return request({
      url: `${BASE_PO_URL}/to-me/waitings-count`,
      instance: 'CORE',
      method: 'get'
    })
  }
}
