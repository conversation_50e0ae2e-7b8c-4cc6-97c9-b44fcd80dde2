import { PreReleaseParams, PreReleaseType } from '@/types/preReleaseTypes'
import PreReleaseService from './service'
import { ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import { ApprovalsCountType } from '@/types/appTypes'

export const PRE_RELEASE_QUERY_KEY = 'PRE_RELEASE_QUERY_KEY'

export default class PreReleaseQueryMethods {
  public static readonly getPreReleases = async (params: PreReleaseParams): Promise<ListResponse<PreReleaseType>> => {
    const { data } = await PreReleaseService.getPreReleases(params)
    return data ?? defaultListData
  }

  public static readonly getPreReleasesToMe = async (
    params: PreReleaseParams
  ): Promise<ListResponse<PreReleaseType>> => {
    const { data } = await PreReleaseService.getPreReleasesTome(params)
    return data ?? defaultListData
  }

  public static readonly getOnePreRelease = async (prId: string): Promise<PreReleaseType> => {
    const { data } = await PreReleaseService.getOnePreRelease(prId)
    return data
  }

  public static readonly getCountApprovals = async (): Promise<ApprovalsCountType> => {
    const { data } = await PreReleaseService.getCountApprovals()
    return data
  }
}
