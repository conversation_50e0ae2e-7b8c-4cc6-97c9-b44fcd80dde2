import request from '@/api/request'
import { ApiResponse, ListResponse } from '@/types/api'
import { ApprovalsCountType } from '@/types/appTypes'
import {
  PreReleaseApprovalDtoType,
  PreReleaseChecklistDto,
  PreReleaseDtoType,
  PreReleaseParams,
  PreReleaseType,
  ReleaseDtoType
} from '@/types/preReleaseTypes'
import { WorkOrderType } from '@/types/woTypes'

export const PRE_RELEASE_QUERY_LIST_KEY = 'PRE_RELEASE_QUERY_LIST_KEY'
export const PRE_RELEASE_QUERY_KEY = 'PRE_RELEASE_QUERY_KEY'

const BASE_PRE_RELEASE = 'pre-releases'
const BASE_WORK_ORDER = 'work-orders'

export default class PreReleaseService {
  public static readonly createPreRelease = (payload: PreReleaseDtoType): Promise<ApiResponse<PreReleaseType>> => {
    return request({
      url: BASE_PRE_RELEASE,
      method: 'post',
      data: payload,
      instance: 'CORE'
    })
  }

  public static readonly getPreReleases = (
    params: PreReleaseParams
  ): Promise<ApiResponse<ListResponse<PreReleaseType>>> => {
    return request({
      url: `${BASE_PRE_RELEASE}`,
      method: 'get',
      instance: 'CORE',
      params
    })
  }

  public static readonly getPreReleasesTome = (
    params: PreReleaseParams
  ): Promise<ApiResponse<ListResponse<PreReleaseType>>> => {
    return request({
      url: `${BASE_PRE_RELEASE}/to-me`,
      method: 'get',
      instance: 'CORE',
      params
    })
  }

  public static readonly getOnePreRelease = (prId: string): Promise<ApiResponse<PreReleaseType>> => {
    return request({
      url: `${BASE_PRE_RELEASE}/${prId}`,
      method: 'get',
      instance: 'CORE'
    })
  }

  public static readonly updatePreReleaseChecklist = (
    params: PreReleaseChecklistDto & { prId: string }
  ): Promise<ApiResponse<PreReleaseType>> => {
    return request({
      url: `${BASE_PRE_RELEASE}/${params.prId}/check-points`,
      method: 'patch',
      instance: 'CORE',
      data: params
    })
  }

  public static readonly updatePreReleaseApprovalStatus = (
    props: PreReleaseApprovalDtoType & { preReleaseId: string; approvalId: number }
  ): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_PRE_RELEASE}/${props.preReleaseId}/approvals/${props.approvalId}/status`,
      method: 'patch',
      instance: 'CORE',
      data: props
    })
  }

  public static readonly readPreReleaseApproval = (params: {
    preReleaseId: string
    approvalId: number
  }): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_PRE_RELEASE}/${params.preReleaseId}/approvals/${params.approvalId}/read`,
      method: 'patch',
      instance: 'CORE',
      data: {
        isRead: true
      }
    })
  }

  public static readonly unitRelease = (
    data: ReleaseDtoType & { workOrderId: string }
  ): Promise<ApiResponse<WorkOrderType>> => {
    return request({
      instance: 'CORE',
      url: `${BASE_WORK_ORDER}/${data.workOrderId}/release`,
      method: 'patch',
      data
    })
  }

  public static readonly getCountApprovals = (): Promise<ApiResponse<ApprovalsCountType>> => {
    return request({
      method: 'get',
      url: `${BASE_PRE_RELEASE}/to-me/waitings-count`,
      instance: 'CORE'
    })
  }
}
