import { ApiResponse } from '@/types/api'
import {
  PreReleaseApprovalDtoType,
  PreReleaseChecklistDto,
  PreReleaseDtoType,
  PreReleaseType,
  ReleaseDtoType
} from '@/types/preReleaseTypes'
import { useMutation, UseMutationResult } from '@tanstack/react-query'
import PreReleaseService from './service'

export const useCreatePreRelease = (): UseMutationResult<
  ApiResponse<PreReleaseType>,
  Error,
  PreReleaseDtoType,
  void
> => {
  return useMutation({
    mutationFn: (payload: any) => {
      return PreReleaseService.createPreRelease(payload)
    }
  })
}

export const useUpdatePreReleaseApprovalStatus = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  PreReleaseApprovalDtoType & { preReleaseId: string; approvalId: number },
  void
> => {
  return useMutation({
    mutationFn: (payload: any) => {
      return PreReleaseService.updatePreReleaseApprovalStatus(payload)
    }
  })
}

export const useReadPreReleaseApproval = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  { preReleaseId: string; approvalId: number },
  void
> => {
  return useMutation({
    mutationFn: (payload: any) => {
      return PreReleaseService.readPreReleaseApproval(payload)
    }
  })
}

export const useUnitRelease = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  ReleaseDtoType & { workOrderId: string },
  void
> => {
  return useMutation({
    mutationFn: (payload: any) => {
      return PreReleaseService.unitRelease(payload)
    }
  })
}

export const useUpdatePreReleaseChecklist = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  PreReleaseChecklistDto & { prId: string },
  void
> => {
  return useMutation({
    mutationFn: (payload: any) => {
      return PreReleaseService.updatePreReleaseChecklist(payload)
    }
  })
}
