import { ApiResponse } from '@/types/api'
import { RolePayload, UserPayload, DefaultApprovalPayload } from '@/types/payload'
import { UseMutationResult, useMutation } from '@tanstack/react-query'
import UsersService from './service'
import { ApproverType, RoleType, UserType } from '@/types/userTypes'

export const useAddUser = (): UseMutationResult<ApiResponse<UserType>, Error, UserPayload, void> => {
  return useMutation({
    mutationFn: (payload: UserPayload) => {
      return UsersService.addUser(payload)
    }
  })
}

export const useUpdateUser = (): UseMutationResult<ApiResponse<UserType>, Error, UserPayload, void> => {
  return useMutation({
    mutationFn: (payload: UserPayload) => {
      return UsersService.updateUser(payload)
    }
  })
}

export const useDeleteUser = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (userId: string) => {
      return UsersService.deleteUser(userId)
    }
  })
}

export const useDeleteRole = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (roleId: string) => {
      return UsersService.deleteRole(roleId)
    }
  })
}

export const useAddRole = (): UseMutationResult<ApiResponse<RoleType>, Error, RolePayload, void> => {
  return useMutation({
    mutationFn: (payload: RolePayload) => {
      return UsersService.addRole(payload)
    }
  })
}

export const useUpdateRole = (): UseMutationResult<ApiResponse<RoleType>, Error, RolePayload, void> => {
  return useMutation({
    mutationFn: (payload: RolePayload) => {
      return UsersService.updateRole(payload)
    }
  })
}

export const useAddDefaultApproval = (): UseMutationResult<
  ApiResponse<ApproverType>,
  Error,
  DefaultApprovalPayload,
  void
> => {
  return useMutation({
    mutationFn: (payload: DefaultApprovalPayload) => {
      return UsersService.addDefaultApproval(payload)
    }
  })
}
