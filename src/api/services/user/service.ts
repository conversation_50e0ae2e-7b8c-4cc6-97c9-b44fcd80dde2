import request from '@/api/request'
import { ApiResponse, ListResponse } from '@/types/api'
import { ApproverType, PermissionType, RoleType, UserType } from '@/types/userTypes'
import {
  RolePayload,
  UserPayload,
  ApproverParams,
  DefaultA<PERSON>rovalPayload,
  ListParams,
  PermissionParams,
  UserParams
} from '@/types/payload'

export const BASE_USER_URL = 'users'
export const BASE_ROLE_URL = 'permission-groups'
export const BASE_PERMISSIONS_URL = 'permissions'
export const BASE_DEFAULT_APPROVALS_URL = 'default-approvals'

export default class UsersService {
  public static readonly addUser = (payload: UserPayload): Promise<ApiResponse<UserType>> => {
    return request({
      url: `${BASE_USER_URL}`,
      instance: 'ACCOUNT',
      method: 'post',
      data: payload
    })
  }

  public static readonly updateUser = ({ userId, ...payload }: UserPayload): Promise<ApiResponse<UserType>> => {
    return request({
      url: `${BASE_USER_URL}/${userId}`,
      instance: 'ACCOUNT',
      method: 'patch',
      data: payload
    })
  }

  public static readonly deleteUser = (userId: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_USER_URL}/${userId}`,
      instance: 'ACCOUNT',
      method: 'delete'
    })
  }

  public static readonly getUser = (id: string): Promise<ApiResponse<UserType>> => {
    return request({
      url: `${BASE_USER_URL}/${id}`,
      instance: 'ACCOUNT',
      method: 'get'
    })
  }

  public static readonly getUserList = (params?: UserParams): Promise<ApiResponse<ListResponse<UserType>>> => {
    return request({
      url: `${BASE_USER_URL}`,
      instance: 'ACCOUNT',
      method: 'get',
      params
    })
  }

  public static readonly getRole = (id: string): Promise<ApiResponse<RoleType>> => {
    return request({
      url: `${BASE_ROLE_URL}/${id}`,
      instance: 'ACCOUNT',
      method: 'get'
    })
  }

  public static readonly getRoleList = (params?: ListParams): Promise<ApiResponse<ListResponse<RoleType>>> => {
    return request({
      url: `${BASE_ROLE_URL}`,
      instance: 'ACCOUNT',
      method: 'get',
      params
    })
  }

  public static readonly addRole = (payload: RolePayload): Promise<ApiResponse<RoleType>> => {
    return request({
      url: `${BASE_ROLE_URL}`,
      instance: 'ACCOUNT',
      method: 'post',
      data: payload
    })
  }

  public static readonly updateRole = ({ roleId, ...payload }: RolePayload): Promise<ApiResponse<RoleType>> => {
    return request({
      url: `${BASE_ROLE_URL}/${roleId}`,
      instance: 'ACCOUNT',
      method: 'patch',
      data: payload
    })
  }

  public static readonly deleteRole = (roleId: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_ROLE_URL}/${roleId}`,
      instance: 'ACCOUNT',
      method: 'delete'
    })
  }

  public static readonly getPermissionList = (
    params?: PermissionParams
  ): Promise<ApiResponse<ListResponse<PermissionType>>> => {
    return request({
      url: `${BASE_PERMISSIONS_URL}`,
      instance: 'ACCOUNT',
      method: 'get',
      params
    })
  }

  public static readonly getPermissionsByRole = (roleId: string): Promise<ApiResponse<string[]>> => {
    return request({
      url: `${BASE_ROLE_URL}/${roleId}/permissions`,
      instance: 'ACCOUNT',
      method: 'get'
    })
  }

  public static readonly getDefaultApproverList = (
    params?: ApproverParams
  ): Promise<ApiResponse<ListResponse<ApproverType>>> => {
    return request({
      url: `${BASE_DEFAULT_APPROVALS_URL}`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly addDefaultApproval = (payload: DefaultApprovalPayload): Promise<ApiResponse<ApproverType>> => {
    return request({
      url: `${BASE_DEFAULT_APPROVALS_URL}`,
      instance: 'CORE',
      method: 'put',
      data: payload
    })
  }
}
