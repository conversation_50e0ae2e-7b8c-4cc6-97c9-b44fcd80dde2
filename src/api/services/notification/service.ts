import request from '@/api/request'
import { ApiResponse } from '@/types/api'
import {
  NotificationParams,
  NotificationListResponse,
  NotificationResponseType,
  NotificationUnreadCountType
} from '@/types/notificationType'

export const BASE_NOTIFICATION_URL = 'notifications'

export default class NotificationMessageService {
  public static readonly getNotificationMessage = (
    params: NotificationParams
  ): Promise<ApiResponse<NotificationListResponse<NotificationResponseType>>> => {
    return request({
      url: `${BASE_NOTIFICATION_URL}`,
      instance: 'MESSAGE',
      method: 'get',
      params
    })
  }

  public static readonly getNotificationUnreadCount = (
    params: NotificationParams
  ): Promise<ApiResponse<NotificationUnreadCountType>> => {
    return request({
      url: `${BASE_NOTIFICATION_URL}/unread-count`,
      instance: 'MESSAGE',
      method: 'get',
      params
    })
  }

  public static readonly markAllNotificationAsRead = (params: NotificationParams): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_NOTIFICATION_URL}/read-all`,
      instance: 'MESSAGE',
      method: 'patch',
      params
    })
  }

  public static readonly markNotificationAsRead = (notificationId: number): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_NOTIFICATION_URL}/${notificationId}/read`,
      instance: 'MESSAGE',
      method: 'patch'
    })
  }
}
