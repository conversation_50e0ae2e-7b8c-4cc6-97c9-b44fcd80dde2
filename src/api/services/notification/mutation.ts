import { ApiResponse } from '@/types/api'
import { UseMutationResult, useMutation } from '@tanstack/react-query'
import { NotificationParams, NotificationResponseType } from '@/types/notificationType'
import NotificationMessageService from './service'


export const useNotificationReadAll = (): UseMutationResult<ApiResponse<NotificationResponseType>, Error, NotificationParams, void> => {
  return useMutation({
    mutationFn: (params: NotificationParams) => {
      return NotificationMessageService.markAllNotificationAsRead(params)
    }
  })
}

export const useNotificationRead = (): UseMutationResult<ApiResponse<NotificationResponseType>, Error, number, void> => {
  return useMutation({
    mutationFn: (id: number) => {
      return NotificationMessageService.markNotificationAsRead(id)
    }
  })
}

