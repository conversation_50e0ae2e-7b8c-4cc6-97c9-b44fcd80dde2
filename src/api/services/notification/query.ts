import { ApiResponse } from '@/types/api'
import NotificationMessageService from './service'
import {
  NotificationParams,
  NotificationListResponse,
  NotificationResponseType,
  NotificationUnreadCountType
} from '@/types/notificationType'

export const NOTIFICATION_MESSAGE_QUERY_KEY = 'NOTIFICATION_MESSAGE_QUERY_KEY'
export const NOTIFICATION_UNREAD_COUNT_QUERY_KEY = 'NOTIFICATION_UNREAD_COUNT_QUERY_KEY'

export default class NotificationMessageQueryMethods {
  public static readonly getNotifications = async (
    params: NotificationParams
  ): Promise<NotificationListResponse<NotificationResponseType>> => {
    return (await NotificationMessageService.getNotificationMessage(params)).data
  }

  public static readonly getNotificationsUnreadCount = async (
    params: NotificationParams
  ): Promise<ApiResponse<NotificationUnreadCountType>> => {
    const data = await NotificationMessageService.getNotificationUnreadCount(params)
    return data
  }
}
