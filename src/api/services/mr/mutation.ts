import { ApiResponse } from '@/types/api'
import { CancelMrPayload, MrApprovalPayload, MrItemPayload, MrItemType, MrPayload, MrType } from '@/types/mrTypes'
import { UseMutationResult, useMutation } from '@tanstack/react-query'
import MrService from './service'

export const useAddMr = (): UseMutationResult<ApiResponse<MrType>, Error, MrPayload, void> => {
  return useMutation({
    mutationFn: (payload: MrPayload) => {
      return MrService.addMr(payload)
    }
  })
}

export const useUpdateMr = (): UseMutationResult<ApiResponse<MrType>, Error, MrPayload, void> => {
  return useMutation({
    mutationFn: (payload: MrPayload) => {
      return MrService.updateMr(payload)
    }
  })
}

export const useDeleteMr = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (mrId: string) => {
      return MrService.deleteMr(mrId)
    }
  })
}

export const useUpdateMrItem = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  { mrId: string; payload: MrItemPayload },
  void
> => {
  return useMutation({
    mutationFn: ({ mrId, payload }) => {
      return MrService.updateMrItem(mrId, payload)
    }
  })
}

export const useDeleteMrItem = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  { mrId: string; itemId: number },
  void
> => {
  return useMutation({
    mutationFn: ({ mrId, itemId }) => {
      return MrService.deleteMrItem(mrId, itemId)
    }
  })
}

export const useUpdateMrApprover = (): UseMutationResult<ApiResponse<any>, Error, MrApprovalPayload, void> => {
  return useMutation({
    mutationFn: (payload: MrApprovalPayload) => {
      return MrService.updateMrApprover(payload)
    }
  })
}

export const useUpdateMrApprovalStatus = (): UseMutationResult<ApiResponse<any>, Error, MrApprovalPayload, void> => {
  return useMutation({
    mutationFn: (payload: MrApprovalPayload) => {
      return MrService.updateMrApprovalStatus(payload)
    }
  })
}

export const useReadMrApproval = (): UseMutationResult<ApiResponse<any>, Error, MrApprovalPayload, void> => {
  return useMutation({
    mutationFn: (payload: MrApprovalPayload) => {
      return MrService.readMrApproval(payload)
    }
  })
}

export const useCancelMr = (): UseMutationResult<ApiResponse<any>, Error, CancelMrPayload, void> => {
  return useMutation({
    mutationFn: (payload: CancelMrPayload) => {
      return MrService.cancelMr(payload)
    }
  })
}

export const useCloseMr = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (mrId: string) => {
      return MrService.closeMr(mrId)
    }
  })
}
