import MrService from './service'
import { Mr<PERSON><PERSON>ilType, Mr<PERSON>ogType, Mr<PERSON><PERSON><PERSON>, MrType } from '@/types/mrTypes'
import { ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import { ListParams } from '@/types/payload'
import { ApprovalsCountType } from '@/types/appTypes'

export const MR_QUERY_KEY = 'MR_QUERY_KEY'
export const MR_LIST_QUERY_KEY = 'MR_LIST_QUERY_KEY'
export const MR_BY_ME_LIST_QUERY_KEY = 'MR_BY_ME_LIST_QUERY_KEY'
export const MR_TO_ME_LIST_QUERY_KEY = 'MR_TO_ME_LIST_QUERY_KEY'
export const MR_LOG_QUERY_KEY = 'MR_LOG_QUERY_KEY'
export const MR_LOG_LIST_QUERY_KEY = 'MR_LOG_LIST_QUERY_KEY'

export default class MrQueryMethods {
  public static readonly getMr = async (id: string): Promise<MrType> => {
    const { data } = await MrService.getMr(id)
    return data
  }

  public static readonly getMrList = async (params?: MrParams): Promise<ListResponse<MrType>> => {
    const res = await MrService.getMrList(params)
    return res.data ?? defaultListData
  }

  public static readonly getByMeMrList = async (params?: MrParams): Promise<ListResponse<MrType>> => {
    const res = await MrService.getByMeMrList(params)
    return res.data ?? defaultListData
  }

  public static readonly getToMeMrList = async (params?: MrParams): Promise<ListResponse<MrType>> => {
    const res = await MrService.getToMeMrList(params)
    return res.data ?? defaultListData
  }

  public static readonly getMrLog = async (id: string): Promise<MrLogType> => {
    const { data } = await MrService.getMrLog(id)
    return data
  }

  public static readonly getMrLogList = async (mrId: string, params?: ListParams): Promise<MrLogType[]> => {
    const { data } = await MrService.getMrLogList(mrId, params)
    return data.items ?? []
  }

  public static readonly getCountApprovals = async (): Promise<ApprovalsCountType> => {
    const { data } = await MrService.getCountApprovals()
    return data
  }
}
