import request from '@/api/request'
import { ApiResponse, ListResponse } from '@/types/api'
import { MrItemType } from '@/types/apps/itemType'
import { ApprovalsCountType } from '@/types/appTypes'
import {
  CancelMrPayload,
  MrA<PERSON>rovalPayload,
  MrDetailType,
  MrItemPayload,
  MrLogType,
  MrParams,
  MrPayload,
  MrType
} from '@/types/mrTypes'
import { ListParams } from '@/types/payload'

export const BASE_MR_URL = 'material-requests'

export default class MrService {
  public static readonly addMr = (payload: MrPayload): Promise<ApiResponse<MrType>> => {
    return request({
      url: `${BASE_MR_URL}`,
      instance: 'CORE',
      method: 'post',
      data: payload
    })
  }

  public static readonly updateMr = ({ mrId, ...payload }: MrPayload): Promise<ApiResponse<MrType>> => {
    return request({
      url: `${BASE_MR_URL}/${mrId}`,
      instance: 'CORE',
      method: 'patch',
      data: payload
    })
  }

  public static readonly deleteMr = (mrId: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_MR_URL}/${mrId}`,
      instance: 'CORE',
      method: 'delete'
    })
  }

  public static readonly getMr = (id: string): Promise<ApiResponse<MrType>> => {
    return request({
      url: `${BASE_MR_URL}/${id}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly getMrList = (params?: MrParams): Promise<ApiResponse<ListResponse<MrType>>> => {
    return request({
      url: `${BASE_MR_URL}`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getByMeMrList = (params?: MrParams): Promise<ApiResponse<ListResponse<MrType>>> => {
    return request({
      url: `${BASE_MR_URL}/by-me`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getToMeMrList = (params?: MrParams): Promise<ApiResponse<ListResponse<MrType>>> => {
    return request({
      url: `${BASE_MR_URL}/to-me`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly cancelMr = ({ mrId, cancelationNote }: CancelMrPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_MR_URL}/${mrId}/cancel`,
      instance: 'CORE',
      method: 'patch',
      data: { cancelationNote }
    })
  }

  public static readonly closeMr = (mrId: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_MR_URL}/${mrId}/close`,
      instance: 'CORE',
      method: 'patch'
    })
  }

  public static readonly updateMrItem = (
    mrId: string,
    { id, ...itemPayload }: MrItemPayload
  ): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_MR_URL}/${mrId}/items/${id}`,
      instance: 'CORE',
      method: 'patch',
      data: itemPayload
    })
  }

  public static readonly deleteMrItem = (mrId: string, itemId: number): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_MR_URL}/${mrId}/items/${itemId}`,
      instance: 'CORE',
      method: 'delete'
    })
  }

  public static readonly updateMrApprover = ({
    mrId,
    approvalId,
    ...payload
  }: MrApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_MR_URL}/${mrId}/approvals/${approvalId}`,
      instance: 'CORE',
      method: 'patch',
      data: payload
    })
  }

  public static readonly updateMrApprovalStatus = ({
    mrId,
    approvalId,
    status
  }: MrApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_MR_URL}/${mrId}/approvals/${approvalId}/status`,
      instance: 'CORE',
      method: 'patch',
      data: {
        status
      }
    })
  }

  public static readonly readMrApproval = ({
    mrId,
    approvalId,
    isRead
  }: MrApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_MR_URL}/${mrId}/approvals/${approvalId}/read`,
      method: 'patch',
      instance: 'CORE',
      data: {
        isRead
      }
    })
  }

  public static readonly getMrLog = (id: string): Promise<ApiResponse<MrLogType>> => {
    return request({
      url: `${BASE_MR_URL}/logs/${id}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly getMrLogList = (
    mrId: string,
    params?: ListParams
  ): Promise<ApiResponse<ListResponse<MrLogType>>> => {
    return request({
      url: `${BASE_MR_URL}/${mrId}/logs`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getCountApprovals = (): Promise<ApiResponse<ApprovalsCountType>> => {
    return request({
      method: 'get',
      url: `${BASE_MR_URL}/to-me/waitings-count`,
      instance: 'CORE'
    })
  }
}
