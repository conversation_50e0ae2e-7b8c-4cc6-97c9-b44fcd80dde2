import { ApiResponse } from '@/types/api'
import { UploadPayload } from '@/types/payload'
import { UseMutationResult, useMutation } from '@tanstack/react-query'
import { FileType } from '@/types/fileTypes'
import FileService from './service'

export const useUploadImage = (): UseMutationResult<ApiResponse<FileType>, Error, UploadPayload, void> => {
  return useMutation({
    mutationFn: (payload: UploadPayload) => {
      return FileService.uploadImage(payload)
    }
  })
}

export const useUploadDocument = (): UseMutationResult<ApiResponse<FileType>, Error, UploadPayload, void> => {
  return useMutation({
    mutationFn: (payload: UploadPayload) => {
      return FileService.uploadDocument(payload)
    }
  })
}
