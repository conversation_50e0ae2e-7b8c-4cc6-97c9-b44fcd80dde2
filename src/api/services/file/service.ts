import request from '@/api/request'
import { ApiResponse, ListResponse } from '@/types/api'
import { CategoryType, DepartmentType, ItemType, SiteType } from '@/types/companyTypes'
import { FileType } from '@/types/fileTypes'
import { ListParams, UploadPayload } from '@/types/payload'
import { convertBase64ToBlob } from '@/utils/helper'
import { uuidv4 } from 'uuidv7'

export const BASE_FILE_UPLOAD_URL = 'uploads'

export default class FileService {
  public static readonly uploadImage = (payload: UploadPayload): Promise<ApiResponse<FileType>> => {
    const form = new FormData()
    form.append('fieldName', payload.fieldName)
    form.append('scope', payload.scope)
    form.append('file', convertBase64ToBlob(payload.file), payload.fileName)
    return request({
      url: `${BASE_FILE_UPLOAD_URL}/images`,
      instance: 'FILE',
      method: 'post',
      data: form,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  public static readonly uploadDocument = (payload: UploadPayload): Promise<ApiResponse<FileType>> => {
    const form = new FormData()
    form.append('fieldName', payload.fieldName)
    form.append('scope', payload.scope)
    form.append('file', convertBase64ToBlob(payload.file), payload.fileName)
    return request({
      url: `${BASE_FILE_UPLOAD_URL}/documents`,
      instance: 'FILE',
      method: 'post',
      data: form,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}
