import request from '@/api/request'
import { ApiResponse, ListResponse } from '@/types/api'
import {
  CancelPrPayload,
  PrApprovalPayload,
  PrDetailType,
  PrItemPayload,
  PrLogType,
  PrParams,
  PrPayload,
  PrType
} from '@/types/prTypes'
import { ListParams } from '@/types/payload'
import { ApprovalsCountType } from '@/types/appTypes'

export const BASE_PR_URL = 'purchase-requisitions'

export default class PrService {
  public static readonly addPr = (payload: PrPayload): Promise<ApiResponse<PrType>> => {
    return request({
      url: `${BASE_PR_URL}`,
      instance: 'CORE',
      method: 'post',
      data: payload
    })
  }

  public static readonly updatePr = ({ prId, ...payload }: PrPayload): Promise<ApiResponse<PrType>> => {
    return request({
      url: `${BASE_PR_URL}/${prId}`,
      instance: 'CORE',
      method: 'patch',
      data: payload
    })
  }

  public static readonly deletePr = (prId: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_PR_URL}/${prId}`,
      instance: 'CORE',
      method: 'delete'
    })
  }

  public static readonly getPr = (id: string): Promise<ApiResponse<PrType>> => {
    return request({
      url: `${BASE_PR_URL}/${id}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly getPrList = (params?: PrParams): Promise<ApiResponse<ListResponse<PrType>>> => {
    return request({
      url: `${BASE_PR_URL}`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getByMePrList = (params?: PrParams): Promise<ApiResponse<ListResponse<PrType>>> => {
    return request({
      url: `${BASE_PR_URL}/by-me`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getToMePrList = (params?: PrParams): Promise<ApiResponse<ListResponse<PrType>>> => {
    return request({
      url: `${BASE_PR_URL}/to-me`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly cancelPr = ({ prId, cancelationNote }: CancelPrPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_PR_URL}/${prId}/cancel`,
      instance: 'CORE',
      method: 'patch',
      data: { cancelationNote }
    })
  }

  public static readonly closePr = (prId: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_PR_URL}/${prId}/close`,
      instance: 'CORE',
      method: 'patch'
    })
  }

  public static readonly updatePrItem = (
    prId: string,
    { id, ...itemPayload }: PrItemPayload
  ): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_PR_URL}/${prId}/items/${id}`,
      instance: 'CORE',
      method: 'patch',
      data: itemPayload
    })
  }

  public static readonly deletePrItem = (prId: string, itemId: number): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_PR_URL}/${prId}/items/${itemId}`,
      instance: 'CORE',
      method: 'delete'
    })
  }

  public static readonly updatePrApprover = ({
    prId,
    approvalId,
    ...payload
  }: PrApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_PR_URL}/${prId}/approvals/${approvalId}`,
      instance: 'CORE',
      method: 'patch',
      data: payload
    })
  }

  public static readonly updatePrApprovalStatus = ({
    prId,
    approvalId,
    status
  }: PrApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_PR_URL}/${prId}/approvals/${approvalId}/status`,
      instance: 'CORE',
      method: 'patch',
      data: {
        status
      }
    })
  }

  public static readonly readPrApproval = ({
    prId,
    approvalId,
    isRead
  }: PrApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_PR_URL}/${prId}/approvals/${approvalId}/read`,
      instance: 'CORE',
      method: 'patch',
      data: {
        isRead
      }
    })
  }

  public static readonly getPrLog = (id: string): Promise<ApiResponse<PrLogType>> => {
    return request({
      url: `${BASE_PR_URL}/logs/${id}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly getPrLogList = (
    prId: string,
    params?: ListParams
  ): Promise<ApiResponse<ListResponse<PrLogType>>> => {
    return request({
      url: `${BASE_PR_URL}/${prId}/logs`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getCountApprovals = (): Promise<ApiResponse<ApprovalsCountType>> => {
    return request({
      method: 'get',
      url: `${BASE_PR_URL}/to-me/waitings-count`,
      instance: 'CORE'
    })
  }
}
