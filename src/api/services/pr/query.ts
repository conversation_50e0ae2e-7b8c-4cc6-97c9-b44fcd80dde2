import PrService from './service'
import { PrDetailType, PrLogType, PrParams, PrType } from '@/types/prTypes'
import { ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import { ListParams } from '@/types/payload'
import { ApprovalsCountType } from '@/types/appTypes'

export const PR_QUERY_KEY = 'PR_QUERY_KEY'
export const PR_LIST_QUERY_KEY = 'PR_LIST_QUERY_KEY'
export const PR_BY_ME_LIST_QUERY_KEY = 'PR_BY_ME_LIST_QUERY_KEY'
export const PR_TO_ME_LIST_QUERY_KEY = 'PR_TO_ME_LIST_QUERY_KEY'
export const PR_LOG_QUERY_KEY = 'PR_LOG_QUERY_KEY'
export const PR_LOG_LIST_QUERY_KEY = 'PR_LOG_LIST_QUERY_KEY'

export default class PrQueryMethods {
  public static readonly getPr = async (id: string): Promise<PrType> => {
    const { data } = await PrService.getPr(id)
    return data
  }

  public static readonly getPrList = async (params?: PrParams): Promise<ListResponse<PrType>> => {
    const res = await PrService.getPrList(params)
    return res.data ?? defaultListData
  }

  public static readonly getByMePrList = async (params?: PrParams): Promise<ListResponse<PrType>> => {
    const res = await PrService.getByMePrList(params)
    return res.data ?? defaultListData
  }

  public static readonly getToMePrList = async (params?: PrParams): Promise<ListResponse<PrType>> => {
    const res = await PrService.getToMePrList(params)
    return res.data ?? defaultListData
  }

  public static readonly getPrLog = async (id: string): Promise<PrLogType> => {
    const { data } = await PrService.getPrLog(id)
    return data
  }

  public static readonly getPrLogList = async (prId: string, params?: ListParams): Promise<PrLogType[]> => {
    const { data } = await PrService.getPrLogList(prId, params)
    return data.items ?? []
  }

  public static readonly getCountApprovals = async (): Promise<ApprovalsCountType> => {
    const { data } = await PrService.getCountApprovals()
    return data
  }
}
