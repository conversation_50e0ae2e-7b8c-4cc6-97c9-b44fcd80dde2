import { ApiResponse } from '@/types/api'
import { CancelPrPayload, PrApprovalPayload, PrItemPayload, PrItemType, PrPayload, PrType } from '@/types/prTypes'
import { UseMutationResult, useMutation } from '@tanstack/react-query'
import PrService from './service'

export const useAddPr = (): UseMutationResult<ApiResponse<PrType>, Error, PrPayload, void> => {
  return useMutation({
    mutationFn: (payload: PrPayload) => {
      return PrService.addPr(payload)
    }
  })
}

export const useUpdatePr = (): UseMutationResult<ApiResponse<PrType>, Error, PrPayload, void> => {
  return useMutation({
    mutationFn: (payload: PrPayload) => {
      return PrService.updatePr(payload)
    }
  })
}

export const useDeletePr = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (prId: string) => {
      return PrService.deletePr(prId)
    }
  })
}

export const useUpdatePrItem = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  { prId: string; payload: PrItemPayload },
  void
> => {
  return useMutation({
    mutationFn: ({ prId, payload }) => {
      return PrService.updatePrItem(prId, payload)
    }
  })
}

export const useDeletePrItem = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  { prId: string; itemId: number },
  void
> => {
  return useMutation({
    mutationFn: ({ prId, itemId }) => {
      return PrService.deletePrItem(prId, itemId)
    }
  })
}

export const useUpdatePrApprover = (): UseMutationResult<ApiResponse<any>, Error, PrApprovalPayload, void> => {
  return useMutation({
    mutationFn: (payload: PrApprovalPayload) => {
      return PrService.updatePrApprover(payload)
    }
  })
}

export const useUpdatePrApprovalStatus = (): UseMutationResult<ApiResponse<any>, Error, PrApprovalPayload, void> => {
  return useMutation({
    mutationFn: (payload: PrApprovalPayload) => {
      return PrService.updatePrApprovalStatus(payload)
    }
  })
}

export const useReadPrApproval = (): UseMutationResult<ApiResponse<any>, Error, PrApprovalPayload, void> => {
  return useMutation({
    mutationFn: (payload: PrApprovalPayload) => {
      return PrService.readPrApproval(payload)
    }
  })
}

export const useCancelPr = (): UseMutationResult<ApiResponse<any>, Error, CancelPrPayload, void> => {
  return useMutation({
    mutationFn: (payload: CancelPrPayload) => {
      return PrService.cancelPr(payload)
    }
  })
}

export const useClosePr = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (prId: string) => {
      return PrService.closePr(prId)
    }
  })
}
