import { ApiResponse } from '@/types/api'
import { UseMutationResult, useMutation } from '@tanstack/react-query'
import RmaService from './service'
import { RmaApprovalPayload, RmaPayload, RmaType } from '@/pages/rma/config/types'

export const useAddRma = (): UseMutationResult<ApiResponse<RmaType>, Error, RmaPayload, void> => {
  return useMutation({
    mutationFn: (payload: RmaPayload) => {
      return RmaService.addRma(payload)
    }
  })
}

export const useUpdateRmaStatus = (): UseMutationResult<ApiResponse<any>, Error, RmaApprovalPayload, void> => {
  return useMutation({
    mutationFn: (payload: RmaApprovalPayload) => {
      return RmaService.updateRmaApprovalStatus(payload)
    }
  })
}

export const useReadRmaApproval = (): UseMutationResult<ApiResponse<any>, <PERSON><PERSON><PERSON>, RmaApprovalPayload, void> => {
  return useMutation({
    mutationFn: (payload: RmaApprovalPayload) => {
      return RmaService.readRmaApproval(payload)
    }
  })
}
