import request from '@/api/request'
import { ApiResponse, ListResponse } from '@/types/api'
import { ImParams, ImPayload, ImType, MgApprovalPayload, MgOutPayload, MgOutType, StockLogType } from '@/types/mgTypes'
import { ListParams } from '@/types/payload'
import { BASE_ITEMS_URL } from '../company/service'
import { RmaApprovalPayload, RmaPayload, RmaType } from '@/pages/rma/config/types'
import { ApprovalsCountType } from '@/types/appTypes'

export const BASE_MG_URL = 'core/material-goods'

const BASE_RMA_URL = 'rmas'

export default class RmaService {
  public static readonly getRma = (id: string): Promise<ApiResponse<RmaType>> => {
    return request({
      url: `${BASE_RMA_URL}/${id}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly getRmaList = (params: ImParams): Promise<ApiResponse<ListResponse<RmaType>>> => {
    return request({
      url: `${BASE_RMA_URL}`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly addRma = (payload: RmaPayload): Promise<ApiResponse<RmaType>> => {
    return request({
      url: `${BASE_RMA_URL}`,
      instance: 'CORE',
      method: 'post',
      data: payload
    })
  }

  public static readonly getToMeRmaList = (params: ImParams): Promise<ApiResponse<ListResponse<RmaType>>> => {
    return request({
      url: `${BASE_RMA_URL}/to-me`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly updateRmaApprovalStatus = ({
    rmaId,
    approvalId,
    status
  }: RmaApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_RMA_URL}/${rmaId}/approvals/${approvalId}/status`,
      instance: 'CORE',
      method: 'patch',
      data: {
        status
      }
    })
  }

  public static readonly readRmaApproval = ({
    rmaId,
    approvalId,
    isRead
  }: RmaApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_RMA_URL}/${rmaId}/approvals/${approvalId}/read`,
      instance: 'CORE',
      method: 'patch',
      data: {
        isRead
      }
    })
  }

  public static readonly getApprovalsCount = (): Promise<ApiResponse<ApprovalsCountType>> => {
    return request({
      url: `${BASE_RMA_URL}/to-me/waitings-count`,
      instance: 'CORE',
      method: 'get'
    })
  }
}
