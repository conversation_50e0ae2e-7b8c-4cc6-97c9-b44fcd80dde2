import RmaService from './service'
import { ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import { RmaType } from '@/pages/rma/config/types'
import { ImParams } from '@/types/mgTypes'
import { ApprovalsCountType } from '@/types/appTypes'

export const RMA_QUERY_KEY = 'RMA_QUERY_KEY'
export const RMA_LIST_QUERY_KEY = 'RMA_LIST_QUERY_KEY'
export const RMA_TO_ME_LIST_QUERY_KEY = 'RMA_TO_ME_LIST_QUERY_KEY'

export default class RmaQueryMethods {
  public static readonly getRma = async (id: string): Promise<RmaType> => {
    const { data } = await RmaService.getRma(id)
    return data
  }

  public static readonly getRmaList = async (params?: ImParams): Promise<RmaType[]> => {
    const {
      data: { items }
    } = await RmaService.getRmaList(params)
    return items ?? []
  }

  public static readonly getRmaListPaginated = async (params?: ImParams): Promise<ListResponse<RmaType>> => {
    const res = await RmaService.getRmaList(params)
    return res.data ?? defaultListData
  }

  public static readonly getToMeRmaList = async (params?: ImParams): Promise<ListResponse<RmaType>> => {
    const res = await RmaService.getToMeRmaList(params)
    return res.data ?? defaultListData
  }

  public static readonly getCountApprovals = async (): Promise<ApprovalsCountType> => {
    const { data } = await RmaService.getApprovalsCount()
    return data
  }
}
