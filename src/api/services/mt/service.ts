import request from '@/api/request'
import {
  CancelMtPayload,
  MtApprovalPayload,
  MtItemType,
  MtLogType,
  MtParams,
  MtPayload,
  MtType,
  SjPayload,
  SjReceiptPayload,
  SjReceiptType,
  SjType
} from '@/pages/material-transfer/config/types'
import { ApiResponse, ListResponse } from '@/types/api'
import { ApprovalsCountType } from '@/types/appTypes'
import { ListParams } from '@/types/payload'

export const BASE_MT_URL = 'material-transfers'

export default class MtService {
  public static readonly addMt = (payload: MtPayload): Promise<ApiResponse<MtType>> => {
    return request({
      url: `${BASE_MT_URL}`,
      instance: 'CORE',
      method: 'post',
      data: payload
    })
  }

  public static readonly updateMt = ({ mtId, ...payload }: MtPayload): Promise<ApiResponse<MtType>> => {
    return request({
      url: `${BASE_MT_URL}/${mtId}`,
      instance: 'CORE',
      method: 'patch',
      data: payload
    })
  }

  public static readonly deleteMt = (mtId: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_MT_URL}/${mtId}`,
      instance: 'CORE',
      method: 'delete'
    })
  }

  public static readonly getMt = (id: string, isCancelation?: boolean): Promise<ApiResponse<MtType>> => {
    return request({
      url: `${BASE_MT_URL}/${id}`,
      instance: 'CORE',
      method: 'get',
      params: isCancelation
        ? {
            isCancelation
          }
        : {}
    })
  }

  public static readonly getMtList = (params?: MtParams): Promise<ApiResponse<ListResponse<MtType>>> => {
    return request({
      url: `${BASE_MT_URL}`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getByMeMtList = (params?: MtParams): Promise<ApiResponse<ListResponse<MtType>>> => {
    return request({
      url: `${BASE_MT_URL}/by-me`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getToMeMtList = (params?: MtParams): Promise<ApiResponse<ListResponse<MtType>>> => {
    return request({
      url: `${BASE_MT_URL}/to-me`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly cancelMt = ({ mtId, ...payload }: CancelMtPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_MT_URL}/${mtId}/cancel`,
      instance: 'CORE',
      method: 'patch',
      data: payload
    })
  }

  public static readonly closeMt = (mtId: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_MT_URL}/${mtId}/close`,
      instance: 'CORE',
      method: 'patch'
    })
  }

  public static readonly updateMtItem = (
    mtId: string,
    { id, ...itemPayload }: MtItemType
  ): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_MT_URL}/${mtId}/items/${id}`,
      instance: 'CORE',
      method: 'patch',
      data: itemPayload
    })
  }

  public static readonly deleteMtItem = (mtId: string, itemId: number): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_MT_URL}/${mtId}/items/${itemId}`,
      instance: 'CORE',
      method: 'delete'
    })
  }

  public static readonly updateMtApprover = ({
    mtId,
    approvalId,
    ...payload
  }: MtApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_MT_URL}/${mtId}/approvals/${approvalId}`,
      instance: 'CORE',
      method: 'patch',
      data: payload
    })
  }

  public static readonly updateMtApprovalStatus = ({
    mtId,
    approvalId,
    status
  }: MtApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_MT_URL}/${mtId}/approvals/${approvalId}/status`,
      instance: 'CORE',
      method: 'patch',
      data: {
        status
      }
    })
  }

  public static readonly updateMtCancelationStatus = ({
    mtId,
    approvalId,
    status
  }: MtApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_MT_URL}/${mtId}/approvals/${approvalId}/cancel-status`,
      instance: 'CORE',
      method: 'patch',
      data: {
        status
      }
    })
  }

  public static readonly readMtApproval = ({
    mtId,
    approvalId,
    isRead
  }: MtApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_MT_URL}/${mtId}/approvals/${approvalId}/read`,
      instance: 'CORE',
      method: 'patch',
      data: {
        isRead
      }
    })
  }

  public static readonly getMtLog = (id: string): Promise<ApiResponse<MtLogType>> => {
    return request({
      url: `${BASE_MT_URL}/logs/${id}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly getMtLogList = (
    mtId: string,
    params?: ListParams
  ): Promise<ApiResponse<ListResponse<MtLogType>>> => {
    return request({
      url: `${BASE_MT_URL}/${mtId}/logs`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly addSj = (mtId: string, payload: SjPayload): Promise<ApiResponse<SjType>> => {
    return request({
      url: `${BASE_MT_URL}/${mtId}/delivery-notes`,
      instance: 'CORE',
      method: 'post',
      data: payload
    })
  }

  public static readonly getSj = (id: string, mtId: string): Promise<ApiResponse<SjType>> => {
    return request({
      url: `${BASE_MT_URL}/${mtId}/delivery-notes/${id}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly getSjList = (mtId: string, params?: MtParams): Promise<ApiResponse<ListResponse<SjType>>> => {
    return request({
      url: `${BASE_MT_URL}/${mtId}/delivery-notes`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly receiveSj = (
    mtId: string,
    sjId: string,
    payload: SjReceiptPayload
  ): Promise<ApiResponse<SjReceiptType>> => {
    return request({
      url: `${BASE_MT_URL}/${mtId}/delivery-notes/${sjId}/receipts`,
      instance: 'CORE',
      method: 'patch',
      data: payload
    })
  }

  public static readonly getCountApprovals = (params?: MtParams): Promise<ApiResponse<ApprovalsCountType>> => {
    return request({
      method: 'get',
      url: `${BASE_MT_URL}/to-me/waitings-count`,
      instance: 'CORE',
      params
    })
  }
}
