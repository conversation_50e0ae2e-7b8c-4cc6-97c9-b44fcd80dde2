import MtService from './service'
import { ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import { ListParams } from '@/types/payload'
import { MtLogType, MtParams, MtType } from '@/pages/material-transfer/config/types'
import { SjType } from '@/pages/material-transfer/config/types'
import { ApprovalsCountType } from '@/types/appTypes'

export const MT_QUERY_KEY = 'MT_QUERY_KEY'
export const MT_LIST_QUERY_KEY = 'MT_LIST_QUERY_KEY'
export const MT_BY_ME_LIST_QUERY_KEY = 'MT_BY_ME_LIST_QUERY_KEY'
export const MT_TO_ME_LIST_QUERY_KEY = 'MT_TO_ME_LIST_QUERY_KEY'
export const MT_LOG_QUERY_KEY = 'MT_LOG_QUERY_KEY'
export const MT_LOG_LIST_QUERY_KEY = 'MT_LOG_LIST_QUERY_KEY'

export const SJ_QUERY_KEY = 'SJ_QUERY_KEY'
export const SJ_LIST_QUERY_KEY = 'SJ_LIST_QUERY_KEY'

export default class MtQueryMethods {
  public static readonly getMt = async (id: string, isCancelation?: boolean): Promise<MtType> => {
    const { data } = await MtService.getMt(id, isCancelation)
    return data
  }

  public static readonly getMtList = async (params?: MtParams): Promise<ListResponse<MtType>> => {
    const res = await MtService.getMtList(params)
    return res.data ?? defaultListData
  }

  public static readonly getByMeMtList = async (params?: MtParams): Promise<ListResponse<MtType>> => {
    const res = await MtService.getByMeMtList(params)
    return res.data ?? defaultListData
  }

  public static readonly getToMeMtList = async (params?: MtParams): Promise<ListResponse<MtType>> => {
    const res = await MtService.getToMeMtList(params)
    return res.data ?? defaultListData
  }

  public static readonly getMtLog = async (id: string): Promise<MtLogType> => {
    const { data } = await MtService.getMtLog(id)
    return data
  }

  public static readonly getMtLogList = async (prId: string, params?: ListParams): Promise<MtLogType[]> => {
    const { data } = await MtService.getMtLogList(prId, params)
    return data.items ?? []
  }

  public static readonly getSj = async (id: string, mtId: string): Promise<SjType> => {
    const { data } = await MtService.getSj(id, mtId)
    return data
  }

  public static readonly getSjList = async (mtId: string, params?: MtParams): Promise<SjType[]> => {
    const { data } = await MtService.getSjList(mtId, params)
    return data?.items ?? []
  }

  public static readonly getCountApprovals = async (params?: MtParams): Promise<ApprovalsCountType> => {
    const { data } = await MtService.getCountApprovals(params)
    return data
  }
}
