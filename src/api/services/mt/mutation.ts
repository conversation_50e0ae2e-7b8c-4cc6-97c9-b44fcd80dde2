import { ApiResponse } from '@/types/api'
import { UseMutationResult, useMutation } from '@tanstack/react-query'
import MtService from './service'
import {
  CancelMtPayload,
  MtApprovalPayload,
  MtItemType,
  MtPayload,
  MtType,
  SjPayload,
  SjReceiptPayload,
  SjReceiptType,
  SjType
} from '@/pages/material-transfer/config/types'

export const useAddMt = (): UseMutationResult<ApiResponse<MtType>, Error, MtPayload, void> => {
  return useMutation({
    mutationFn: (payload: MtPayload) => {
      return MtService.addMt(payload)
    }
  })
}

export const useUpdateMt = (): UseMutationResult<ApiResponse<MtType>, Error, MtPayload, void> => {
  return useMutation({
    mutationFn: (payload: MtPayload) => {
      return MtService.updateMt(payload)
    }
  })
}

export const useDeleteMt = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (mtId: string) => {
      return MtService.deleteMt(mtId)
    }
  })
}

export const useUpdateMtItem = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  { mtId: string; payload: MtItemType },
  void
> => {
  return useMutation({
    mutationFn: ({ mtId, payload }) => {
      return MtService.updateMtItem(mtId, payload)
    }
  })
}

export const useDeleteMtItem = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  { mtId: string; itemId: number },
  void
> => {
  return useMutation({
    mutationFn: ({ mtId, itemId }) => {
      return MtService.deleteMtItem(mtId, itemId)
    }
  })
}

export const useUpdateMtApprover = (): UseMutationResult<ApiResponse<any>, Error, MtApprovalPayload, void> => {
  return useMutation({
    mutationFn: (payload: MtApprovalPayload) => {
      return MtService.updateMtApprover(payload)
    }
  })
}

export const useUpdateMtApprovalStatus = (): UseMutationResult<ApiResponse<any>, Error, MtApprovalPayload, void> => {
  return useMutation({
    mutationFn: (payload: MtApprovalPayload) => {
      return MtService.updateMtApprovalStatus(payload)
    }
  })
}

export const useUpdateMtCancelationStatus = (): UseMutationResult<ApiResponse<any>, Error, MtApprovalPayload, void> => {
  return useMutation({
    mutationFn: (payload: MtApprovalPayload) => {
      return MtService.updateMtCancelationStatus(payload)
    }
  })
}

export const useReadMtApproval = (): UseMutationResult<ApiResponse<any>, Error, MtApprovalPayload, void> => {
  return useMutation({
    mutationFn: (payload: MtApprovalPayload) => {
      return MtService.readMtApproval(payload)
    }
  })
}

export const useCancelMt = (): UseMutationResult<ApiResponse<any>, Error, CancelMtPayload, void> => {
  return useMutation({
    mutationFn: (payload: CancelMtPayload) => {
      return MtService.cancelMt(payload)
    }
  })
}

export const useCloseMt = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (mtId: string) => {
      return MtService.closeMt(mtId)
    }
  })
}

export const useAddSj = (): UseMutationResult<ApiResponse<SjType>, Error, [string, SjPayload], void> => {
  return useMutation({
    mutationFn: ([mtId, payload]: [string, SjPayload]) => {
      return MtService.addSj(mtId, payload)
    }
  })
}

export const useReceiveSj = (): UseMutationResult<
  ApiResponse<SjReceiptType>,
  Error,
  [string, string, SjPayload],
  void
> => {
  return useMutation({
    mutationFn: ([mtId, sjId, payload]: [string, string, SjReceiptPayload]) => {
      return MtService.receiveSj(mtId, sjId, payload)
    }
  })
}
