import request from '@/api/request'
import { AccountMasterType, AccountParams, AccountType, DivisionType, GeneralLedgerType } from '@/types/accountTypes'
import { ApiResponse, ListResponse } from '@/types/api'
import {
  AccountPayload,
  DivisionPayload,
  GeneralLedgerPayload,
  ListParams,
  UpdateDivisionPayload
} from '@/types/payload'
import { SalaryParams, SalaryType, SalaryTypeMaster } from '@/types/salaryTypes'

export const BASE_URL_ACCOUNTS = 'accounts'
export const BASE_URL_DIVISIONS = 'divisions'
export const BASE_URL_JOURNALS = 'journals'

export default class AccountsService {
  public static readonly createAccount = (data: AccountPayload): Promise<ApiResponse<AccountType>> => {
    return request({
      method: 'POST',
      url: `${BASE_URL_ACCOUNTS}`,
      instance: 'CORE',
      data
    })
  }

  public static readonly updateAccount = (data: { id: string } & AccountPayload): Promise<ApiResponse<AccountType>> => {
    const { id, ...rest } = data
    return request({
      method: 'patch',
      url: `${BASE_URL_ACCOUNTS}/${id}`,
      instance: 'CORE',
      data: rest
    })
  }

  public static readonly deleteAccount = (id: string): Promise<ApiResponse<any>> => {
    return request({
      method: 'DELETE',
      url: `${BASE_URL_ACCOUNTS}/${id}`,
      instance: 'CORE'
    })
  }

  public static readonly getAccountList = (params?: AccountParams): Promise<ApiResponse<ListResponse<AccountType>>> => {
    return request({
      method: 'GET',
      url: `${BASE_URL_ACCOUNTS}`,
      instance: 'CORE',
      params
    })
  }

  public static readonly getAccount = (id: string): Promise<ApiResponse<AccountType>> => {
    return request({
      method: 'GET',
      url: `${BASE_URL_ACCOUNTS}/${id}`,
      instance: 'CORE'
    })
  }

  public static readonly getAccountTypeList = (
    params?: ListParams
  ): Promise<ApiResponse<ListResponse<AccountMasterType>>> => {
    return request({
      method: 'GET',
      url: `${BASE_URL_ACCOUNTS}/master-data/types`,
      instance: 'CORE',
      params
    })
  }

  public static readonly getDivisionList = (params?: ListParams): Promise<ApiResponse<ListResponse<DivisionType>>> => {
    return request({
      method: 'GET',
      url: `${BASE_URL_DIVISIONS}`,
      instance: 'ACCOUNT',
      params
    })
  }

  public static readonly getDivision = (id: string): Promise<ApiResponse<DivisionType>> => {
    return request({
      method: 'GET',
      url: `${BASE_URL_DIVISIONS}/${id}`,
      instance: 'ACCOUNT'
    })
  }

  public static readonly addDivision = (payload: DivisionPayload): Promise<ApiResponse<DivisionType>> => {
    return request({
      method: 'POST',
      url: `${BASE_URL_DIVISIONS}`,
      instance: 'ACCOUNT',
      data: payload
    })
  }

  public static readonly updateDivision = (
    payload: UpdateDivisionPayload & { id: string }
  ): Promise<ApiResponse<DivisionType>> => {
    return request({
      method: 'PATCH',
      url: `${BASE_URL_DIVISIONS}/${payload.id}`,
      instance: 'ACCOUNT',
      data: payload
    })
  }

  public static readonly deleteDivision = (id: string): Promise<ApiResponse<any>> => {
    return request({
      method: 'DELETE',
      url: `${BASE_URL_DIVISIONS}/${id}`,
      instance: 'ACCOUNT'
    })
  }

  public static readonly getGeneralLedgerList = (
    params?: ListParams
  ): Promise<ApiResponse<ListResponse<GeneralLedgerType>>> => {
    return request({
      method: 'GET',
      url: `${BASE_URL_JOURNALS}`,
      instance: 'CORE',
      params
    })
  }

  public static readonly createGeneralLedger = (
    payload: GeneralLedgerPayload
  ): Promise<ApiResponse<GeneralLedgerType>> => {
    return request({
      method: 'POST',
      url: `${BASE_URL_JOURNALS}`,
      instance: 'CORE',
      data: payload
    })
  }

  public static readonly getGeneralLedger = (id: string): Promise<ApiResponse<GeneralLedgerType>> => {
    return request({
      method: 'GET',
      url: `${BASE_URL_JOURNALS}/${id}`,
      instance: 'CORE'
    })
  }
}
