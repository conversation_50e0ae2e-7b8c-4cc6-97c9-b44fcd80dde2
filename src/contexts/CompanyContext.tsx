import { createContext, ReactNode, useContext } from 'react'
import { useQuery } from '@tanstack/react-query'
import usePartialState from '@/core/hooks/usePartialState'
import { ListParams } from '@/types/payload'
import CompanyQueryMethods, { SITE_LIST_QUERY_KEY } from '@/api/services/company/query'
import { SiteType } from '@/types/companyTypes'
import { useAuth } from './AuthContext'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'

interface CompanyContextProps {
  showLoading: boolean
  siteListResponse: ListResponse<SiteType>
  sitesParams: ListParams
  setSitesParams: React.Dispatch<React.SetStateAction<ListParams>>
  setPartialSitesParams: (fieldName: string, value: any) => void
}

export const CompanyContext = createContext<CompanyContextProps>({} as CompanyContextProps)

interface CompanyContextProviderProps {
  children: ReactNode
}

export const useCompany = () => {
  return useContext(CompanyContext)
}

export function CompanyContextProvider({ children }: CompanyContextProviderProps) {
  const [sitesParams, setPartialSitesParams, setSitesParams] = usePartialState<ListParams>(undefined)

  const {
    data: siteListResponse,
    refetch: fetchSiteList,
    isLoading: fetchSiteListLoading
  } = useQuery({
    queryKey: [SITE_LIST_QUERY_KEY, JSON.stringify(sitesParams)],
    queryFn: () => {
      const { search, ...params } = sitesParams
      return CompanyQueryMethods.getSiteList({
        ...(search && { search }),
        ...params
      })
    },
    placeholderData: defaultListData as ListResponse<SiteType>
  })

  const value = {
    showLoading: fetchSiteListLoading,
    siteListResponse,
    fetchSiteList,
    sitesParams,
    setPartialSitesParams,
    setSitesParams
  }

  return (
    <>
      <CompanyContext.Provider value={value}>{children}</CompanyContext.Provider>
    </>
  )
}
