import * as Sentry from '@sentry/react'
import { Suspense, useEffect } from 'react'

import { QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import type { FallbackProps } from 'react-error-boundary'
import { ErrorBoundary } from 'react-error-boundary'
import { HelmetProvider } from 'react-helmet-async'
import { BrowserRouter } from 'react-router-dom'

// Type Imports
import { Button } from '@mui/material'

// Context Imports
import ThemeProvider from '@/components/theme'

// Styled Component Imports
import AppReactToastify from '@/components/libs/styles/AppReactToastify'

// Util Imports
type Props = ChildrenType

import { useRouter } from '@/routes/hooks'
import { MenuProvider } from '@/components/menu/contexts/menuContext'
import { ChildrenType } from '@/core/types'
import { getMode, getSettingsFromCookie, getSystemMode } from '@/core/utils/serverHelpers'
import { SettingsProvider } from '@/core/contexts/settingsContext'
import { queryClient } from '@/api/queryClient'
import { AuthContextProvider } from './AuthContext'
import { CameraProvider } from '@/components/camera/camera-provider'
import { uuidv4 } from 'uuidv7'
import localforage from 'localforage'
import OneSignal from 'react-onesignal'
import useNotificationPermission from '@/core/hooks/useNotification'

export const CLIENT_ID_KEY = 'eq_cltId'

const ErrorFallback = ({ error }: FallbackProps) => {
  const router = useRouter()

  Sentry.captureException(error)

  console.error('error', error)

  return (
    <div className='flex h-screen w-screen flex-col items-center  justify-center text-red-500 gap-4' role='alert'>
      <h2 className='text-2xl font-semibold'>Ooops, telah terjadi kesalahan... </h2>
      <br />
      <pre className='text-2xl font-bold'>{error.message}</pre>
      <pre>{error.stack}</pre>
      <Button className='mt-4' onClick={() => window.location.reload()}>
        MUAT ULANG
      </Button>
      <Button className='mt-4' onClick={() => router.back()}>
        KEMBALI
      </Button>
    </div>
  )
}

const AppProvider = (props: Props) => {
  // Props
  const { children } = props

  const mode = getMode()
  const settingsCookie = getSettingsFromCookie()
  const systemMode = getSystemMode()
  const notificationPermission = useNotificationPermission()

  useEffect(() => {
    if (typeof window !== 'undefined') {
      OneSignal.init({
        appId: import.meta.env.VITE_ONE_SIGNAL_APP_ID,
        notifyButton: {
          enable: true
        } as any
      })
        .then(res => {
          if (notificationPermission === 'ASK') {
            OneSignal.Slidedown.promptPush({ force: true })
          }
        })
        .catch(error => {
          console.error(error)
        })
    }
    localforage.getItem(CLIENT_ID_KEY).then(clientId => {
      if (!clientId) {
        localforage.setItem(CLIENT_ID_KEY, uuidv4())
      }
    })
  }, [])

  return (
    <Suspense>
      <HelmetProvider>
        <BrowserRouter>
          <ErrorBoundary FallbackComponent={ErrorFallback}>
            <QueryClientProvider client={queryClient}>
              {/* <ReactQueryDevtools initialIsOpen={false} /> */}
              <SettingsProvider settingsCookie={settingsCookie} mode={mode}>
                <ThemeProvider systemMode={systemMode}>
                  <AuthContextProvider>
                    <MenuProvider>
                      <CameraProvider>{children}</CameraProvider>
                    </MenuProvider>
                  </AuthContextProvider>
                  <AppReactToastify hideProgressBar />
                </ThemeProvider>
              </SettingsProvider>
            </QueryClientProvider>
          </ErrorBoundary>
        </BrowserRouter>
      </HelmetProvider>
    </Suspense>
  )
}

export default AppProvider
