import { createContext, ReactNode, useContext, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import {
  NotificationListResponse,
  NotificationParams,
  NotificationsType,
  NotificationUnreadCountType
} from '@/types/notificationType'
import { QueryFn, SetState } from '@/types/alias'
import NotificationMessageQueryMethods, {
  NOTIFICATION_MESSAGE_QUERY_KEY,
  NOTIFICATION_UNREAD_COUNT_QUERY_KEY
} from '@/api/services/notification/query'
import { ApiResponse } from '@/types/api'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

interface NotificationContextProps {
  notificationListResponse: NotificationsType[]
  fetchNotificationList: QueryFn<NotificationsType[]>
  notificationParams: NotificationParams
  setNotificationParams: SetState<NotificationParams>
  unreadNotificationCount: ApiResponse<NotificationUnreadCountType>
  fetchNotificationUnreadCount: QueryFn<ApiResponse<NotificationUnreadCountType>>
  notificationMeta: NotificationListResponse<any>
}

export const NotificationContext = createContext<NotificationContextProps>({} as NotificationContextProps)

interface NotificationContextProviderProps {
  children: ReactNode
}

export const useNotification = () => {
  return useContext(NotificationContext)
}

export function NotificationContextProvider({ children }: NotificationContextProviderProps) {
  const [notificationMeta, setNotificationMeta] = useState<NotificationListResponse<any>>({
    page: 1,
    limit: 10,
    hasMoreItems: true,
    lastId: 0
  } as NotificationListResponse<any>)
  const [notificationParams, setNotificationParams] = useState<NotificationParams>({
    limit: 20
    // lastId: 1
    // limit: 6,
  })

  const { data: notificationListResponse, refetch: fetchNotificationList } = useQuery({
    enabled: false || notificationParams?.limit > 20,
    keepPreviousData: true,
    queryKey: [NOTIFICATION_MESSAGE_QUERY_KEY, notificationParams],
    queryFn: () =>
      NotificationMessageQueryMethods.getNotifications(notificationParams).then(res => {
        setNotificationMeta(res)
        return res.items.map(notifData => {
          return {
            id: notifData.id,
            title: notifData.title,
            subtitle: notifData.body,
            time: formatDate(notifData.createdAt, 'eeee, dd MMMM yyyy, HH:mm', { locale: id }),
            read: notifData.isRead,
            appPath: notifData.appPath
          }
        })
      }),
    placeholderData: []
  })

  const { data: unreadNotificationCount, refetch: fetchNotificationUnreadCount } = useQuery({
    queryKey: [NOTIFICATION_UNREAD_COUNT_QUERY_KEY, notificationParams],
    queryFn: () => NotificationMessageQueryMethods.getNotificationsUnreadCount(notificationParams)
  })

  const value = {
    notificationListResponse,
    fetchNotificationList,
    notificationParams,
    setNotificationParams,
    unreadNotificationCount,
    fetchNotificationUnreadCount,
    notificationMeta
  }

  return (
    <>
      <NotificationContext.Provider value={value}>{children}</NotificationContext.Provider>
    </>
  )
}
