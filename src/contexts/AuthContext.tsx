/* eslint-disable @typescript-eslint/no-explicit-any */
import { AuthState } from '@/types/payload'
import { createContext, ReactNode, useContext, useEffect, useState } from 'react'
import * as Sentry from '@sentry/react'
import OneSignal from 'react-onesignal'
import localforage from 'localforage'
import { getFromCookies, saveToCookies } from '@/utils/storage'
import Cookies from 'js-cookie'
import UserQueryMethods, { ACCOUNT_PERMISSIONS_QUERY_KEY, USER_QUERY_KEY } from '@/api/services/user/query'
import { QueryObserverResult, RefetchOptions, RefetchQueryFilters, useQueries, useQuery } from '@tanstack/react-query'
import { UserType } from '@/types/userTypes'
import { useLocalStorage, useUpdateEffect } from 'react-use'
import { defaultListData, queryClient } from '@/api/queryClient'
import { DepartmentType, Site, SiteType } from '@/types/companyTypes'
import CompanyQueryMethods, {
  CURRENCIES_LIST_QUERY_KEY,
  DEPARTMENT_LIST_QUERY_KEY,
  SITE_LIST_QUERY_KEY
} from '@/api/services/company/query'
import { ListResponse } from '@/types/api'
import mailStringRemover from '@/core/utils/mailStringRemover'
import truncateString from '@/core/utils/truncate'
import useLocalForage from '@/core/hooks/useLocalforage'
import { useObjectCookie } from '@/core/hooks/useObjectCookie'
import useCookieState from '@/core/hooks/useCookiestate'
import { objectsEqual } from '@/utils/helper'
import MrQueryMethods from '@/api/services/mr/query'
import PrQueryMethods from '@/api/services/pr/query'
import MgQueryMethods from '@/api/services/mg/query'
import MtQueryMethods from '@/api/services/mt/query'
import { MaterialTransferType } from '@/pages/material-transfer/config/enum'
import SmQueryMethods from '@/api/services/stock-movement/query'
import RmaQueryMethods from '@/api/services/rma/query'
import SrQueryMethods from '@/api/services/sr/query'
import PreReleaseQueryMethods from '@/api/services/pre-release/query'
import ServiceRequisitionQueryMethods from '@/api/services/service-requisitions/query'
import PartSwapQueryMethods from '@/api/services/part-swap/query'
import PoQueryMethods from '@/api/services/po/query'
import ServiceOrderQueryMethods from '@/api/services/service-order/query'
import { WpQueryMethods } from '@/api/services/wp/query'
import { CurrenciesType } from '@/types/currenciesTypes'
import CashBankQueryMethods from '@/api/services/cashbank/query'
import PurchaseInvoiceQueryMethods from '@/api/services/purchase-invoice/query'
export const AUTH_STATE_KEY = 'xr-auth-state'
export const ACCOUNTS_STORAGE_KEY = 'xr-accs'
export const AUTH_STORAGE_KEY = 'xr-auth'
export const SIGNUP_STATE_STORAGE_KEY = 'xr-sgnup'
export const LAST_REFRESH_TOKEN_AT = 'xr_lastrefreshAt'
export const LAST_REFRESH_RESPONSE = 'xr_lastrefresh'
export const LAST_REFRESH_ERROR_RESPONSE = 'xr_lastrefreshError'
export const USER_PROFILE_LOCAL_STORAGE_KEY = 'xr-user-profile'

export interface AuthContextProps {
  authState?: AuthState
  updateAuthState: (authState?: AuthState) => void
  clearAllData: () => void
  activeAccount: string
  setAccount: (email: string) => Promise<AuthState>
  accounts: Record<string, AuthState>
  addAccount: (email: string, companyCode: string, state: AuthState) => void
  removeAccount: (email: string) => void
  showLoading: boolean
  fetchUserProfile: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<UserType, unknown>>
  userProfile?: UserType
  accountPermissions: string[]
  allSites: SiteType[]
  ownSiteList: SiteType[]
  offlinePermissions: string[]
  departmentList?: DepartmentType[]
  currenciesList?: CurrenciesType[]
  approvalCounts: {
    mrCount: number
    prCount: number
    mtCount: number
    mbCount: number
    mgOutCount: number
    smCount: number
    rmaCount: number
    srCount: number
    preReleaseCount: number
    sreqCount: number
    pwCount: number
    poCount: number
    purchaseInvoiceCount: number
    soCount: number
    wpTakeCount: number
    wpReturnCount: number
    paymentCount: number
    cashReceiptCount: number
  }
}

export const AuthContext = createContext<AuthContextProps>({} as AuthContextProps)

interface AuthContextProviderProps {
  children: ReactNode
}

export const useAuth = () => {
  return useContext(AuthContext)
}

export function AuthContextProvider({ children }: AuthContextProviderProps) {
  const [authState, setAuthState] = useCookieState<AuthState>(AUTH_STORAGE_KEY, {}, true)
  const [accounts, setAccounts] = useLocalStorage<Record<keyof AuthState, AuthState>>(ACCOUNTS_STORAGE_KEY, {})
  const [activeAccount, setActiveAccount] = useLocalStorage<string>(AUTH_STORAGE_KEY, '')
  const { value: offlinePermissions, setValue: setAccountPermissions } = useLocalForage<Array<string>>(
    'ACCOUNT_PERMISSIONS',
    []
  )
  const [userProfile, setUserProfile] = useLocalStorage<UserType>('USER_PROFILE')

  const queries = useQueries({
    queries: [
      {
        enabled: !!authState.authenticated,
        queryKey: ['MR_COUNT_QUERY_KEY'],
        queryFn: () => MrQueryMethods.getCountApprovals()
      },
      {
        enabled: !!authState.authenticated,
        queryKey: ['PR_COUNT_QUERY_KEY'],
        queryFn: () => PrQueryMethods.getCountApprovals()
      },
      {
        enabled: !!authState.authenticated,
        queryKey: ['MT_COUNT_QUERY_KEY'],
        queryFn: () =>
          MtQueryMethods.getCountApprovals({
            types: `${MaterialTransferType.IN_SITE},${MaterialTransferType.S2S_TRANSFER}`
          })
      },
      {
        enabled: !!authState.authenticated,
        queryKey: ['MB_COUNT_QUERY_KEY'],
        queryFn: () => MtQueryMethods.getCountApprovals({ types: `${MaterialTransferType.S2S_BORROW}` })
      },
      {
        enabled: !!authState.authenticated,
        queryKey: ['MG_OUT_COUNT_QUERY_KEY'],
        queryFn: () => MgQueryMethods.getCountMgOutApprovals()
      },
      {
        enabled: !!authState.authenticated,
        queryKey: ['SM_COUNT_QUERY_KEY'],
        queryFn: () => SmQueryMethods.getCountApprovals()
      },
      {
        enabled: !!authState.authenticated,
        queryKey: ['RMA_COUNT_QUERY_KEY'],
        queryFn: () => RmaQueryMethods.getCountApprovals()
      },
      {
        enabled: !!authState.authenticated,
        queryKey: ['SR_COUNT_QUERY_KEY'],
        queryFn: () => SrQueryMethods.getCountApprovals()
      },
      {
        enabled: !!authState.authenticated,
        queryKey: ['PRE_RELEASE_COUNT_QUERY_KEY'],
        queryFn: () => PreReleaseQueryMethods.getCountApprovals()
      },
      {
        enabled: !!authState.authenticated,
        queryKey: ['SREQ_COUNT_QUERY_KEY'],
        queryFn: () => ServiceRequisitionQueryMethods.getCountApprovals()
      },
      {
        enabled: !!authState.authenticated,
        queryKey: ['PART_SWAP_COUNT_QUERY_KEY'],
        queryFn: () => PartSwapQueryMethods.getCountApprovals()
      },
      {
        enabled: !!authState.authenticated,
        queryKey: ['PO_COUNT_QUERY_KEY'],
        queryFn: () => PoQueryMethods.getCountApprovals()
      },
      {
        enabled: !!authState.authenticated,
        queryKey: ['PURCHASE_INVOICE_COUNT_QUERY_KEY'],
        queryFn: () => PurchaseInvoiceQueryMethods.getCountApprovals()
      },
      {
        enabled: !!authState.authenticated,
        queryKey: ['SO_COUNT_QUERY_KEY'],
        queryFn: () => ServiceOrderQueryMethods.getCountApprovals()
      },
      {
        enabled: !!authState.authenticated,
        queryKey: ['WP_COUNT_QUERY_KEY', 'TAKE'],
        queryFn: () => WpQueryMethods.getCountApprovals('TAKE')
      },
      {
        enabled: !!authState.authenticated,
        queryKey: ['WP_COUNT_QUERY_KEY', 'RETURN'],
        queryFn: () => WpQueryMethods.getCountApprovals('RETURN')
      },
      {
        enabled: !!authState.authenticated,
        queryKey: ['PAYMENT_COUNT_QUERY_KEY'],
        queryFn: () => CashBankQueryMethods.getCountApprovals()
      },
      {
        enabled: !!authState.authenticated,
        queryKey: ['RECEIPT_COUNT_QUERY_KEY'],
        queryFn: () => CashBankQueryMethods.getCashReceiptCount()
      }
    ]
  })

  const [
    mrCount,
    prCount,
    mtCount,
    mbCount,
    mgOutCount,
    smCount,
    rmaCount,
    srCount,
    preReleaseCount,
    sreqCount,
    pwCount,
    poCount,
    purchaseInvoiceCount,
    soCount,
    wpTakeCount,
    wpReturnCount,
    paymentCount,
    cashReceiptCount
  ] = queries?.map(res => res.data)?.map(res => res?.count) || []

  const {
    data: departmentList,
    refetch: fetchDepartmentList,
    isLoading: fetchDepartmentsLoading
  } = useQuery({
    enabled: !!authState?.token,
    queryKey: [DEPARTMENT_LIST_QUERY_KEY, authState?.token],
    queryFn: async () => {
      const res = await CompanyQueryMethods.getDepartmentList({
        limit: Number.MAX_SAFE_INTEGER
      })
      return res?.items ?? []
    },
    placeholderData: [] as DepartmentType[]
  })

  const { refetch: fetchUserProfile, isLoading } = useQuery({
    enabled: !!authState?.token,
    queryKey: [USER_QUERY_KEY, activeAccount, 'me'],
    queryFn: async () => {
      const response = await UserQueryMethods.getUser('me')
      if (response) {
        setUserProfile(response)
      }
      return response
    },
    cacheTime: 0
  })

  const { data: siteListResponse } = useQuery({
    queryKey: [SITE_LIST_QUERY_KEY],
    enabled: !!authState?.token,
    queryFn: () => {
      const sitesParams = { limit: Number.MAX_SAFE_INTEGER, page: 1 }
      return CompanyQueryMethods.getSiteList({ ...sitesParams })
    },
    placeholderData: defaultListData as ListResponse<SiteType>
  })

  const { data: accountPermissions } = useQuery({
    enabled: !!userProfile?.permissionGroupId,
    queryKey: [ACCOUNT_PERMISSIONS_QUERY_KEY, activeAccount, userProfile?.permissionGroupId ?? ''],
    queryFn: () => UserQueryMethods.getPermissionsByRole(userProfile?.permissionGroupId),
    staleTime: 0,
    cacheTime: 0,
    placeholderData: []
  })

  const {
    data: { items: currenciesList }
  } = useQuery({
    queryKey: [CURRENCIES_LIST_QUERY_KEY],
    enabled: !!authState?.token,
    queryFn: async () => {
      return await CompanyQueryMethods.getCurrenciesList({ page: 1, limit: Number.MAX_SAFE_INTEGER })
    },
    placeholderData: defaultListData as ListResponse<CurrenciesType>
  })

  const updateAuthState = (state?: AuthState) => {
    setAuthState(current => {
      const updatedState = {
        ...current,
        ...state,
        authenticated: !!state?.token || !!current?.token
      } as AuthState
      if (updatedState?.token) {
        saveToCookies<AuthState>(AUTH_STORAGE_KEY, updatedState)
      }
      return updatedState
    })
  }

  const addAccount = (email: string, companyCode: string, state: AuthState) => {
    const cleanmail = mailStringRemover(email)
    setAccounts({ ...accounts, [cleanmail]: { ...state, companyCode } })
    saveToCookies<AuthState>(`${AUTH_STORAGE_KEY}_${cleanmail}`, state)
  }

  const removeAccount = (email: string) => {
    try {
      const cleanmail = mailStringRemover(email)
      const deepCloneAccounts = JSON.parse(JSON.stringify(accounts))

      if (!deepCloneAccounts[cleanmail]) return

      delete deepCloneAccounts[cleanmail]

      const remainKeys = Object.keys(deepCloneAccounts)
      Sentry.captureMessage('REMAIN ACCOUNTS: ' + JSON.stringify(deepCloneAccounts))
      OneSignal.logout()
      if (remainKeys.length === 0 || objectsEqual(deepCloneAccounts, {})) {
        clearAllData()
        window.location.href = '/auth/login'
      } else {
        setAccounts(deepCloneAccounts)
        setActiveAccount(remainKeys[0])
        Cookies.remove(`${AUTH_STORAGE_KEY}_${cleanmail}`)
        window.location.href = '/'
      }
    } catch (error) {
      Sentry.captureException(error)
    }
  }

  const setAccount = (email: string): Promise<AuthState> => {
    return new Promise(resolve => {
      const cleanmail = mailStringRemover(email)
      setActiveAccount(cleanmail)
      if (accounts[cleanmail]) {
        saveToCookies<AuthState>(`${AUTH_STORAGE_KEY}`, accounts[cleanmail])
      }
      resolve(accounts[cleanmail])
    })
  }

  const clearAllData = () => {
    localStorage.clear()
    localforage.clear()
    const cookies = Cookies.get()
    setAuthState({ authenticated: false, registered: false })
    for (const key in cookies) {
      Cookies.remove(key)
    }
  }

  const loginOnesignal = async () => {
    try {
      await OneSignal.login(userProfile.id)
      OneSignal.User.addAliases({ userId: userProfile.id, email: userProfile.email })
    } catch (error) {
      console.error(error)
    }
  }

  useEffect(() => {
    const newAuthState = accounts[activeAccount]
    const savedAuthState = getFromCookies<AuthState>(`${AUTH_STORAGE_KEY}_${activeAccount}`)
    const originAuthState = getFromCookies<AuthState>(`${AUTH_STORAGE_KEY}`)

    const resState = newAuthState ?? savedAuthState ?? originAuthState

    if (resState) {
      setAuthState(resState)
    } else {
      setAuthState({ authenticated: false, registered: false })
    }
  }, [activeAccount])

  useUpdateEffect(() => {
    if (!activeAccount) {
      const arrAccounts = Object.keys(accounts)
      if (arrAccounts.length > 0) {
        setActiveAccount(arrAccounts[0])
      }
    }
  }, [activeAccount, accounts])

  useUpdateEffect(() => {
    if (userProfile) {
      localforage.setItem(USER_PROFILE_LOCAL_STORAGE_KEY, userProfile)
      const accs = Object.keys(accounts)

      if (accs.length === 0) {
        const auth = getFromCookies<AuthState>(`${AUTH_STORAGE_KEY}`)
        if (auth?.token) {
          const cleanmail = mailStringRemover(auth?.email)
          if (cleanmail && !accounts[cleanmail]) {
            addAccount(cleanmail, `${userProfile?.company?.code} - ${userProfile?.permissionGroup?.code}`, auth)
          }
        }
      } else {
        const currentAccount = accounts[activeAccount]
        if (
          !currentAccount?.companyCode.toLowerCase().includes(userProfile?.permissionGroup?.code.toLowerCase()) &&
          currentAccount?.email === userProfile?.email
        ) {
          currentAccount.companyCode = `${currentAccount.companyCode} - ${userProfile?.permissionGroup?.code}`
          setAccounts({ ...accounts, [activeAccount]: currentAccount })
        }
      }
      loginOnesignal()
    }
  }, [userProfile, accounts, activeAccount])

  useUpdateEffect(() => {
    if (accountPermissions?.length > 0) {
      setAccountPermissions(accountPermissions)
    }
  }, [accountPermissions])

  const value = {
    accounts,
    activeAccount,
    addAccount,
    removeAccount,
    setAccount,
    authState,
    updateAuthState,
    clearAllData,
    showLoading: isLoading,
    fetchUserProfile,
    userProfile,
    accountPermissions,
    allSites: siteListResponse?.items ?? [],
    ownSiteList:
      siteListResponse?.items?.filter(site => !!(userProfile?.sites ?? []).find(userSite => userSite.id === site.id)) ??
      [],
    departmentList,
    offlinePermissions,
    currenciesList,
    approvalCounts: {
      mrCount,
      prCount,
      mtCount,
      mbCount,
      mgOutCount,
      smCount,
      rmaCount,
      srCount,
      preReleaseCount,
      sreqCount,
      pwCount,
      poCount,
      purchaseInvoiceCount,
      soCount,
      wpTakeCount,
      wpReturnCount,
      paymentCount,
      cashReceiptCount
    }
  }

  return (
    <>
      <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
    </>
  )
}
