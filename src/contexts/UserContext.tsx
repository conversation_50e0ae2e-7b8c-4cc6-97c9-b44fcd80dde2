import { create<PERSON>ontext, <PERSON>actN<PERSON>, useContext, useState } from 'react'
import { QueryObserverResult, RefetchOptions, RefetchQueryFilters, useQuery } from '@tanstack/react-query'
import UserQueryMethods, {
  PERMISSION_LIST_QUERY_KEY,
  ROLE_LIST_QUERY_KEY,
  USER_LIST_QUERY_KEY,
  USER_QUERY_KEY
} from '@/api/services/user/query'
import { PermissionType, RoleType, UserType } from '@/types/userTypes'
import { ListParams, UserParams } from '@/types/payload'
import usePartialState from '@/core/hooks/usePartialState'
import { ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import CompanyQueryMethods, { DEPARTMENT_LIST_QUERY_KEY, SITE_LIST_QUERY_KEY } from '@/api/services/company/query'
import { DepartmentType, SiteType } from '@/types/companyTypes'
import { usePathname } from '@/routes/hooks'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'

interface UserContextProps {
  showLoading: boolean
  userListResponse: ListResponse<UserType>
  fetchUserList: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<ListResponse<UserType>, unknown>>
  usersParams: UserParams
  setUsersParams: React.Dispatch<React.SetStateAction<UserParams>>
  setPartialUsersParams: (fieldName: string, value: any) => void
  userData: UserType
  selectedUserId?: string
  setSelectedUserId: React.Dispatch<React.SetStateAction<string>>
  clearUserData: () => void
  departmentList: DepartmentType[]
  siteList: SiteType[]
  roleList: RoleType[]
  isMobile: boolean
}

export const UserContext = createContext<UserContextProps>({} as UserContextProps)

interface UserContextProviderProps {
  children: ReactNode
}

export const useUser = () => {
  return useContext(UserContext)
}

export function UserContextProvider({ children }: UserContextProviderProps) {
  const pathname = usePathname()
  const { isMobile } = useMobileScreen()
  const [selectedUserId, setSelectedUserId] = useState<string>()
  const [usersParams, setPartialUsersParams, setUsersParams] = usePartialState<UserParams>(
    {
      limit: 10,
      page: 1
    },
    'USERS_PARAMS'
  )

  const {
    data: userData,
    refetch: fetchUserData,
    isLoading: fetchUserDataLoading,
    remove: removeUserData
  } = useQuery({
    enabled: !!selectedUserId,
    queryKey: [USER_QUERY_KEY, selectedUserId],
    queryFn: () => UserQueryMethods.getUser(selectedUserId)
  })

  const {
    data: userListResponse,
    refetch: fetchUserList,
    isLoading: fetchUsersLoading
  } = useQuery({
    queryKey: [USER_LIST_QUERY_KEY, JSON.stringify(usersParams)],
    queryFn: () => {
      const { search, permissionGroupId, departmentId, ...params } = usersParams
      return UserQueryMethods.getUserList({
        ...(search && { search }),
        ...(permissionGroupId && { permissionGroupId }),
        ...(departmentId && { departmentId }),
        ...params
      })
    },
    placeholderData: defaultListData as ListResponse<UserType>
  })

  const {
    data: { items: departmentList }
  } = useQuery({
    queryKey: [DEPARTMENT_LIST_QUERY_KEY],
    queryFn: () => CompanyQueryMethods.getDepartmentList({ limit: 1000 }),
    placeholderData: defaultListData as ListResponse<DepartmentType>
  })

  const {
    data: { items: siteList }
  } = useQuery({
    queryKey: [SITE_LIST_QUERY_KEY],
    queryFn: () => CompanyQueryMethods.getSiteList({ limit: 1000 }),
    placeholderData: defaultListData as ListResponse<SiteType>
  })

  const {
    data: { items: roleList }
  } = useQuery({
    enabled: pathname === '/user/list',
    queryKey: [ROLE_LIST_QUERY_KEY],
    queryFn: () => UserQueryMethods.getRoleList({ limit: 1000 }),
    placeholderData: defaultListData as ListResponse<RoleType>
  })

  const clearUserData = () => {
    setSelectedUserId(undefined)
    removeUserData()
  }

  const value = {
    showLoading: fetchUsersLoading || fetchUserDataLoading,
    fetchUserList,
    userListResponse: userListResponse ?? defaultListData,
    usersParams,
    setUsersParams,
    setPartialUsersParams,
    userData,
    selectedUserId,
    setSelectedUserId,
    clearUserData,
    departmentList,
    siteList,
    roleList,
    isMobile
  }

  return (
    <>
      <UserContext.Provider value={value}>{children}</UserContext.Provider>
    </>
  )
}
