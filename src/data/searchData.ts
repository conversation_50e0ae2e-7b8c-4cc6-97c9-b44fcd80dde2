type SearchData = {
  id: string
  name: string
  url: string
  excludeLang?: boolean
  icon: string
  section: string
  shortcut?: string
}

const data: SearchData[] = [
  // Dashboards
  {
    id: 'warehouse-dashboard',
    name: 'Warehouse',
    url: '/dashboard/warehouse',
    icon: 'ri-home-smile-line',
    section: 'Dashboards'
  },
  {
    id: 'purchasing-dashboard',
    name: 'Purchasing',
    url: '/dashboard/purchasing',
    icon: 'ri-home-smile-line',
    section: 'Dashboards'
  },
  {
    id: 'workshop-dashboard',
    name: 'Workshop',
    url: '/dashboard/workshop',
    icon: 'ri-home-smile-line',
    section: 'Dashboards'
  },
  {
    id: 'accounting-dashboard',
    name: 'Accounting',
    url: '/dashboard/accounting',
    icon: 'ri-home-smile-line',
    section: 'Dashboards'
  },

  // Approvals
  {
    id: 'default-approval',
    name: 'Default Approval',
    url: '/user/default-approval',
    icon: 'ri-user-line',
    section: '<PERSON>u <PERSON>set<PERSON>'
  },
  {
    id: 'mr-approval',
    name: 'Material Request',
    url: '/mr/approval',
    icon: 'approvals-icon',
    section: 'Persetujuan'
  },
  {
    id: 'pr-approval',
    name: 'Purchase Request',
    url: '/pr/approval',
    icon: 'approvals-icon',
    section: 'Persetujuan'
  },
  {
    id: 'mt-approval',
    name: 'Material Transfer',
    url: '/mt/approval',
    icon: 'approvals-icon',
    section: 'Persetujuan'
  },
  {
    id: 'mb-approval',
    name: 'Material Borrow',
    url: '/mb/approval',
    icon: 'approvals-icon',
    section: 'Persetujuan'
  },
  {
    id: 'req-out',
    name: 'Barang Keluar',
    url: '/mg/out/approval',
    icon: 'approvals-icon',
    section: 'Persetujuan'
  },
  {
    id: 'approve-stock-movement',
    name: 'Pindah Barang',
    url: '/mg/sm/approval',
    icon: 'approvals-icon',
    section: 'Persetujuan'
  },
  {
    id: 'stock-approval',
    name: 'Pengembalian Stok',
    url: '/sr/approval',
    icon: 'approvals-icon',
    section: 'Persetujuan'
  },
  {
    id: 'po-approval',
    name: 'Purchase Order',
    url: '/po/approval',
    icon: 'approvals-icon',
    section: 'Persetujuan'
  },
  {
    id: 'so-approval',
    name: 'Service Order',
    url: '/service-order/approval',
    icon: 'approvals-icon',
    section: 'Persetujuan'
  },
  { id: 'rma-approval', name: 'RMA', url: '/rma/approval', icon: 'approvals-icon', section: 'Persetujuan' },
  {
    id: 'stuff-request-approval-take',
    name: 'Pengambilan Barang WO',
    url: '/stuff-request/approvals-take',
    icon: 'approvals-icon',
    section: 'Persetujuan'
  },
  {
    id: 'stuff-request-approval-return',
    name: 'Pengembalian Barang WO',
    url: '/stuff-request/approvals-return',
    icon: 'approvals-icon',
    section: 'Persetujuan'
  },
  {
    id: 'approval-pre-release-list',
    name: 'Pre-Release',
    url: '/wo/approval-pre-releases',
    icon: 'approvals-icon',
    section: 'Persetujuan'
  },
  {
    id: 'service-request-approval',
    name: 'Service Request',
    url: '/service-request/approval',
    icon: 'approvals-icon',
    section: 'Persetujuan'
  },
  {
    id: 'part-swap-approval',
    name: 'Part Swap',
    url: '/part-swap/approval',
    icon: 'approvals-icon',
    section: 'Persetujuan'
  },

  // Warehouse Management
  {
    id: 'item-list',
    name: 'List Barang',
    url: '/company-data/assets/goods',
    icon: 'mdi-layers-triple-outline',
    section: 'Warehouse Management'
  },
  {
    id: 'category-list-item',
    name: 'Kategori Barang',
    url: '/company-data/category/item',
    icon: 'mdi-layers-triple-outline',
    section: 'Warehouse Management'
  },
  {
    id: 'mg-in',
    name: 'Barang Masuk',
    url: '/mg/in',
    icon: 'mdi-layers-triple-outline',
    section: 'Warehouse Management'
  },
  {
    id: 'mg-out-request',
    name: 'Barang Keluar',
    url: '/mg/out/request',
    icon: 'mdi-layers-triple-outline',
    section: 'Warehouse Management'
  },
  {
    id: 'mg-out-draft',
    name: 'Draft Barang Keluar',
    url: '/mg/out/draft',
    icon: 'mdi-layers-triple-outline',
    section: 'Warehouse Management'
  },
  {
    id: 'created-stock-movement',
    name: 'Pindah Barang Terbuat',
    url: '/mg/sm/list',
    icon: 'mdi-layers-triple-outline',
    section: 'Warehouse Management'
  },
  {
    id: 'receive-stock-movement',
    name: 'Penerimaan Pindah Barang',
    url: '/mg/sm/receive',
    icon: 'mdi-layers-triple-outline',
    section: 'Warehouse Management'
  },
  {
    id: 'mr-list',
    name: 'Material Request Terbuat',
    url: '/mr/list',
    icon: 'ic-outline-file-upload',
    section: 'Warehouse Management'
  },
  {
    id: 'mr-create',
    name: 'Buat Material Request',
    url: '/mr/create',
    icon: 'ic-outline-file-upload',
    section: 'Warehouse Management'
  },
  {
    id: 'mr-draft',
    name: 'Draft Material Request',
    url: '/mr/draft',
    icon: 'ic-outline-file-upload',
    section: 'Warehouse Management'
  },
  {
    id: 'mr-created',
    name: 'Material Request Masuk',
    url: '/mr-in',
    icon: 'ri-folder-download-line',
    section: 'Warehouse Management'
  },
  {
    id: 'pr-list',
    name: 'Purchase Request Terbuat',
    url: '/pr/list',
    icon: 'mdi-file-document-outline',
    section: 'Warehouse Management'
  },
  {
    id: 'pr-drafts',
    name: 'Draft Purchase Request',
    url: '/pr/draft',
    icon: 'mdi-file-document-outline',
    section: 'Warehouse Management'
  },
  {
    id: 'mt-created',
    name: 'Material Transfer Terbuat',
    url: '/mt/created',
    icon: 'solar-card-transfer-linear',
    section: 'Warehouse Management'
  },
  {
    id: 'mt-list',
    name: 'Permintaan Masuk Material Transfer',
    url: '/mt/list',
    icon: 'solar-card-transfer-linear',
    section: 'Warehouse Management'
  },
  {
    id: 'mt-receive',
    name: 'Penerimaan Material Transfer',
    url: '/mt/receive',
    icon: 'solar-card-transfer-linear',
    section: 'Warehouse Management'
  },
  {
    id: 'mt-draft',
    name: 'Draft Material Transfer',
    url: '/mt/draft',
    icon: 'solar-card-transfer-linear',
    section: 'Warehouse Management'
  },
  {
    id: 'mb-created',
    name: 'Material Borrow Terbuat',
    url: '/mb/created',
    icon: 'streamline-arrow-infinite-loop',
    section: 'Warehouse Management'
  },
  {
    id: 'mb-list',
    name: 'Permintaan Masuk Material Borrow',
    url: '/mb/list',
    icon: 'streamline-arrow-infinite-loop',
    section: 'Warehouse Management'
  },
  {
    id: 'mb-receive',
    name: 'Penerimaan Material Borrow',
    url: '/mb/receive',
    icon: 'streamline-arrow-infinite-loop',
    section: 'Warehouse Management'
  },
  {
    id: 'mb-return-list',
    name: 'Pengembalian Material Borrow',
    url: '/mb/return-list',
    icon: 'streamline-arrow-infinite-loop',
    section: 'Warehouse Management'
  },
  {
    id: 'mg-stock',
    name: 'Stok Barang',
    url: '/mg/stock',
    icon: 'stock-icon',
    section: 'Warehouse Management'
  },
  {
    id: 'mg-stock-opname',
    name: 'Stok Opnam',
    url: '/mg/so',
    icon: 'stock-icon',
    section: 'Warehouse Management'
  },
  {
    id: 'stock-return-request',
    name: 'Pengajuan Pengembalian Stok',
    url: '/sr/request',
    icon: 'stock-icon',
    section: 'Warehouse Management'
  },
  {
    id: 'stock-return-draft',
    name: 'Draft Pengembalian Stok',
    url: '/sr/draft',
    icon: 'stock-icon',
    section: 'Warehouse Management'
  },

  // Purchasing
  {
    id: 'vendor-list',
    name: 'List Vendor',
    url: '/company-data/vendor',
    icon: 'ri-store-2-line',
    section: 'Purchasing'
  },
  {
    id: 'category-list-vendor',
    name: 'Kategori Vendor',
    url: '/company-data/category/vendor',
    icon: 'ri-store-2-line',
    section: 'Purchasing'
  },
  {
    id: 'pr-created',
    name: 'Permintaan Masuk Purchase Order',
    url: '/po/pr-list',
    icon: 'heroicons-document-arrow-down',
    section: 'Purchasing'
  },
  {
    id: 'po-list',
    name: 'Purchase Order Terbuat',
    url: '/po/list',
    icon: 'heroicons-document-arrow-down',
    section: 'Purchasing'
  },
  {
    id: 'po-draft',
    name: 'Draft Purchase Order',
    url: '/po/draft',
    icon: 'heroicons-document-arrow-down',
    section: 'Purchasing'
  },
  {
    id: 'sr-created',
    name: 'Permintaan Masuk Service Order',
    url: '/service-order/sr-list',
    icon: 'heroicons-document-arrow-down',
    section: 'Purchasing'
  },
  {
    id: 'so-list',
    name: 'Service Order Terbuat',
    url: '/service-order/list',
    icon: 'heroicons-document-arrow-down',
    section: 'Purchasing'
  },
  {
    id: 'pi-created',
    name: 'Faktur Pembelian Terbuat',
    url: '/purchase-invoice/list',
    icon: 'invoice-icon',
    section: 'Purchasing'
  },
  {
    id: 'pi-create',
    name: 'Buat Faktur Pembelian',
    url: '/purchase-invoice/create',
    icon: 'invoice-icon',
    section: 'Purchasing'
  },
  { id: 'rma-list', name: 'RMA Terbuat', url: '/rma/list', icon: 'ic-rma-outline', section: 'Purchasing' },
  { id: 'draft-rma', name: 'Draft RMA', url: '/rma/draft', icon: 'ic-rma-outline', section: 'Purchasing' },

  // MRO
  {
    id: 'unit-asset-list',
    name: 'List Unit',
    url: '/company-data/assets/unit',
    icon: 'asset-icon',
    section: 'MRO'
  },
  {
    id: 'document-asset',
    name: 'List Dokumen',
    url: '/company-data/assets/document',
    icon: 'asset-icon',
    section: 'MRO'
  },
  {
    id: 'insurance-asset',
    name: 'List Asuransi',
    url: '/company-data/assets/insurance',
    icon: 'asset-icon',
    section: 'MRO'
  },
  {
    id: 'category-list-unit',
    name: 'Kategori Unit',
    url: '/company-data/category/unit',
    icon: 'asset-icon',
    section: 'MRO'
  },
  { id: 'component', name: 'Component', url: '/rnm/component', icon: 'fluens-number-symbol-16', section: 'MRO' },
  { id: 'job-code', name: 'Job Code', url: '/rnm/job-code', icon: 'fluens-number-symbol-16', section: 'MRO' },
  {
    id: 'kode-modifier',
    name: 'Kode Modifier',
    url: '/rnm/modifier',
    icon: 'fluens-number-symbol-16',
    section: 'MRO'
  },
  { id: 'unit-section', name: 'Unit', url: '/rnm/unit', icon: 'car-unit', section: 'MRO' },
  { id: 'created-fr', name: 'Field Report Terbuat', url: '/fr/created', icon: 'paste-icon', section: 'MRO' },
  { id: 'create-fr', name: 'Buat Field Report', url: '/fr/new-fr', icon: 'paste-icon', section: 'MRO' },
  {
    id: 'wo-list-fr',
    name: 'Report Masuk Work Order',
    url: '/wo/list',
    icon: 'wo-icon',
    section: 'MRO'
  },
  { id: 'wo-list-created', name: 'Work Order Terbuat', url: '/wo/created', icon: 'wo-icon', section: 'MRO' },
  { id: 'work-process-in', name: 'Masuk Work Process', url: '/wp-in', icon: 'work-process', section: 'MRO' },
  {
    id: 'work-process',
    name: 'Work Process Terbuat',
    url: '/wp/list',
    icon: 'work-process',
    section: 'MRO'
  },
  { id: 'created-wp', name: 'Buat Work Process', url: '/wp/create', icon: 'work-process', section: 'MRO' },
  { id: 'wp-review', name: 'Review Work Process', url: '/wp/review', icon: 'work-process', section: 'MRO' },
  {
    id: 'part-swap-list',
    name: 'Part Swap Terbuat',
    url: '/part-swap/list',
    icon: 'part-swap-icon',
    section: 'MRO'
  },
  {
    id: 'part-swap-create',
    name: 'Buat Request Part Swap',
    url: '/part-swap/create',
    icon: 'part-swap-icon',
    section: 'MRO'
  },
  {
    id: 'pre-release-format',
    name: 'Format Pre-Release',
    url: '/wo/format-pre-release',
    icon: 'pre-release-icon',
    section: 'MRO'
  },
  {
    id: 'pre-release-created',
    name: 'Pre-Release Terbuat',
    url: '/wo/pre-release-created',
    icon: 'pre-release-icon',
    section: 'MRO'
  },
  {
    id: 'pre-release-list-submission',
    name: 'Pengajuan Pre-Release',
    url: '/wo/pre-releases',
    icon: 'pre-release-icon',
    section: 'MRO'
  },
  { id: 'unit-taking', name: 'Pengambilan Unit', url: '/wo/unit-taking', icon: 'pre-release-icon', section: 'MRO' },
  {
    id: 'service-request-list',
    name: 'Service Request Terbuat',
    url: '/service-request/list',
    icon: 'sr-icon',
    section: 'MRO'
  },
  {
    id: 'service-request-create',
    name: 'Buat Service Request',
    url: '/service-request/create',
    icon: 'sr-icon',
    section: 'MRO'
  },

  // Accounting
  {
    id: 'accounts-section',
    name: 'Akun Perkiraan',
    url: '/accounting/accounts',
    icon: 'ri-building-4-line',
    section: 'Accounting'
  },
  {
    id: 'salary-section',
    name: 'Gaji / Tunjangan',
    url: '/accounting/salary',
    icon: 'ri-building-4-line',
    section: 'Accounting'
  },
  { id: 'tax-section', name: 'Pajak', url: '/accounting/tax', icon: 'ri-building-4-line', section: 'Accounting' },
  {
    id: 'currency-section',
    name: 'Mata Uang',
    url: '/accounting/currency',
    icon: 'ri-building-4-line',
    section: 'Accounting'
  },
  {
    id: 'customer-section',
    name: 'Customer/Pelanggan',
    url: '/accounting/customer',
    icon: 'ri-building-4-line',
    section: 'Accounting'
  },
  {
    id: 'carrier-section',
    name: 'Pengiriman',
    url: '/accounting/carrier',
    icon: 'ri-building-4-line',
    section: 'Accounting'
  },
  { id: 'asset-list', name: 'List Aset', url: '/accounting/assets/list', icon: 'wealth-icon', section: 'Accounting' },
  {
    id: 'general-ledger',
    name: 'Jurnal Umum',
    url: '/accounting/general-ledger',
    icon: 'journal-icon',
    section: 'Accounting'
  },

  // Company Data
  {
    id: 'category-list',
    name: 'List Kategori',
    url: '/company-data/category',
    icon: 'tabler--category',
    section: 'Data Perusahaan'
  },
  {
    id: 'site-list',
    name: 'List Site',
    url: '/company-data/site',
    icon: 'ri-map-pin-line',
    section: 'Data Perusahaan'
  },
  {
    id: 'project-list',
    name: 'Manajemen Proyek',
    url: '/company-data/projects',
    icon: 'projects-icon',
    section: 'Data Perusahaan'
  },
  {
    id: 'department-list',
    name: 'List Departemen',
    url: '/company-data/department',
    icon: 'ri-building-4-line',
    section: 'Data Perusahaan'
  },
  {
    id: 'division-list',
    name: 'List Divisi',
    url: '/company-data/division',
    icon: 'ri-building-4-line',
    section: 'Data Perusahaan'
  },

  // Ekspor/Impor Data
  {
    id: 'data-export',
    name: 'List Ekspor Data',
    url: '/data-export',
    icon: 'tabler-file-export',
    section: 'Ekspor/Impor Data'
  },
  {
    id: 'data-import',
    name: 'List Impor Data',
    url: '/data-import',
    icon: 'tabler-file-import',
    section: 'Ekspor/Impor Data'
  },

  // Settings
  { id: 'user-list', name: 'List User', url: '/user/list', icon: 'ri-user-line', section: 'Pengaturan' },
  { id: 'role', name: 'Role User', url: '/user/role', icon: 'ri-user-line', section: 'Pengaturan' },
  {
    id: 'numbering-settings',
    name: 'Penomoran',
    url: '/setting/numbering',
    icon: 'lsicon--number-filled',
    section: 'Pengaturan'
  }
]

export default data
